using BusinessManagementSystem.Application.DTOs.Common;
using System.ComponentModel.DataAnnotations;

namespace BusinessManagementSystem.Application.DTOs.RolePermission;

/// <summary>
/// Role Permission DTO for data transfer
/// DTO صلاحية الدور لنقل البيانات
/// </summary>
public class RolePermissionDto : BaseDto
{
    /// <summary>
    /// Role ID
    /// معرف الدور
    /// </summary>
    public int RoleId { get; set; }

    /// <summary>
    /// Role name
    /// اسم الدور
    /// </summary>
    public string RoleName { get; set; } = string.Empty;

    /// <summary>
    /// Page name
    /// اسم الصفحة
    /// </summary>
    public string PageName { get; set; } = string.Empty;

    /// <summary>
    /// Can add permission
    /// صلاحية الإضافة
    /// </summary>
    public bool CanAdd { get; set; }

    /// <summary>
    /// Can edit permission
    /// صلاحية التعديل
    /// </summary>
    public bool CanEdit { get; set; }

    /// <summary>
    /// Can delete permission
    /// صلاحية الحذف
    /// </summary>
    public bool CanDelete { get; set; }

    /// <summary>
    /// Can view permission
    /// صلاحية العرض
    /// </summary>
    public bool CanView { get; set; }

    /// <summary>
    /// Permission summary as string
    /// ملخص الصلاحية كنص
    /// </summary>
    public string PermissionSummary => GetPermissionSummary();

    /// <summary>
    /// Get permission summary string
    /// الحصول على ملخص الصلاحية كنص
    /// </summary>
    private string GetPermissionSummary()
    {
        var permissions = new List<string>();
        if (CanView) permissions.Add("View");
        if (CanAdd) permissions.Add("Add");
        if (CanEdit) permissions.Add("Edit");
        if (CanDelete) permissions.Add("Delete");
        return string.Join(", ", permissions);
    }
}

/// <summary>
/// Create Role Permission DTO
/// DTO إنشاء صلاحية الدور
/// </summary>
public class CreateRolePermissionDto
{
    /// <summary>
    /// Role ID
    /// معرف الدور
    /// </summary>
    [Required(ErrorMessage = "Role ID is required")]
    public int RoleId { get; set; }

    /// <summary>
    /// Page name
    /// اسم الصفحة
    /// </summary>
    [Required(ErrorMessage = "Page name is required")]
    [StringLength(50, ErrorMessage = "Page name cannot exceed 50 characters")]
    public string PageName { get; set; } = string.Empty;

    /// <summary>
    /// Can add permission
    /// صلاحية الإضافة
    /// </summary>
    public bool CanAdd { get; set; }

    /// <summary>
    /// Can edit permission
    /// صلاحية التعديل
    /// </summary>
    public bool CanEdit { get; set; }

    /// <summary>
    /// Can delete permission
    /// صلاحية الحذف
    /// </summary>
    public bool CanDelete { get; set; }

    /// <summary>
    /// Can view permission
    /// صلاحية العرض
    /// </summary>
    public bool CanView { get; set; }
}

/// <summary>
/// Update Role Permission DTO
/// DTO تحديث صلاحية الدور
/// </summary>
public class UpdateRolePermissionDto
{
    /// <summary>
    /// Can add permission
    /// صلاحية الإضافة
    /// </summary>
    public bool CanAdd { get; set; }

    /// <summary>
    /// Can edit permission
    /// صلاحية التعديل
    /// </summary>
    public bool CanEdit { get; set; }

    /// <summary>
    /// Can delete permission
    /// صلاحية الحذف
    /// </summary>
    public bool CanDelete { get; set; }

    /// <summary>
    /// Can view permission
    /// صلاحية العرض
    /// </summary>
    public bool CanView { get; set; }
}

/// <summary>
/// Permission Matrix DTO
/// DTO مصفوفة الصلاحيات
/// </summary>
public class PermissionMatrixDto
{
    /// <summary>
    /// Role information
    /// معلومات الدور
    /// </summary>
    public RoleInfoDto Role { get; set; } = null!;

    /// <summary>
    /// Permissions by page
    /// الصلاحيات حسب الصفحة
    /// </summary>
    public List<PagePermissionDto> PagePermissions { get; set; } = new();

    /// <summary>
    /// Total permissions count
    /// إجمالي عدد الصلاحيات
    /// </summary>
    public int TotalPermissions { get; set; }

    /// <summary>
    /// Granted permissions count
    /// عدد الصلاحيات الممنوحة
    /// </summary>
    public int GrantedPermissions { get; set; }

    /// <summary>
    /// Permission coverage percentage
    /// نسبة تغطية الصلاحيات
    /// </summary>
    public decimal PermissionCoverage => TotalPermissions > 0 ? (decimal)GrantedPermissions / TotalPermissions * 100 : 0;
}

/// <summary>
/// Role Info DTO
/// DTO معلومات الدور
/// </summary>
public class RoleInfoDto
{
    /// <summary>
    /// Role ID
    /// معرف الدور
    /// </summary>
    public int Id { get; set; }

    /// <summary>
    /// Role name
    /// اسم الدور
    /// </summary>
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// Role description
    /// وصف الدور
    /// </summary>
    public string? Description { get; set; }

    /// <summary>
    /// Is system role
    /// هل هو دور نظام
    /// </summary>
    public bool IsSystemRole { get; set; }

    /// <summary>
    /// Is active
    /// هل نشط
    /// </summary>
    public bool IsActive { get; set; }
}

/// <summary>
/// Page Permission DTO
/// DTO صلاحية الصفحة
/// </summary>
public class PagePermissionDto
{
    /// <summary>
    /// Page name
    /// اسم الصفحة
    /// </summary>
    public string PageName { get; set; } = string.Empty;

    /// <summary>
    /// Page display name (localized)
    /// اسم الصفحة للعرض (محلي)
    /// </summary>
    public string PageDisplayName { get; set; } = string.Empty;

    /// <summary>
    /// Page category/module
    /// فئة/وحدة الصفحة
    /// </summary>
    public string PageCategory { get; set; } = string.Empty;

    /// <summary>
    /// Can add permission
    /// صلاحية الإضافة
    /// </summary>
    public bool CanAdd { get; set; }

    /// <summary>
    /// Can edit permission
    /// صلاحية التعديل
    /// </summary>
    public bool CanEdit { get; set; }

    /// <summary>
    /// Can delete permission
    /// صلاحية الحذف
    /// </summary>
    public bool CanDelete { get; set; }

    /// <summary>
    /// Can view permission
    /// صلاحية العرض
    /// </summary>
    public bool CanView { get; set; }

    /// <summary>
    /// Has any permission
    /// لديه أي صلاحية
    /// </summary>
    public bool HasAnyPermission => CanAdd || CanEdit || CanDelete || CanView;

    /// <summary>
    /// Permission level (None, Read, Write, Full)
    /// مستوى الصلاحية (لا شيء، قراءة، كتابة، كامل)
    /// </summary>
    public string PermissionLevel => GetPermissionLevel();

    /// <summary>
    /// Get permission level string
    /// الحصول على مستوى الصلاحية كنص
    /// </summary>
    private string GetPermissionLevel()
    {
        if (CanAdd && CanEdit && CanDelete && CanView)
            return "Full";
        if (CanAdd || CanEdit || CanDelete)
            return "Write";
        if (CanView)
            return "Read";
        return "None";
    }
}

/// <summary>
/// Bulk Permission Update DTO
/// DTO تحديث الصلاحيات بالجملة
/// </summary>
public class BulkPermissionUpdateDto
{
    /// <summary>
    /// Role ID
    /// معرف الدور
    /// </summary>
    [Required(ErrorMessage = "Role ID is required")]
    public int RoleId { get; set; }

    /// <summary>
    /// Permissions to update
    /// الصلاحيات المراد تحديثها
    /// </summary>
    [Required(ErrorMessage = "Permissions are required")]
    public List<PagePermissionUpdateDto> Permissions { get; set; } = new();

    /// <summary>
    /// Whether to replace all existing permissions
    /// ما إذا كان سيتم استبدال جميع الصلاحيات الموجودة
    /// </summary>
    public bool ReplaceExisting { get; set; } = true;
}

/// <summary>
/// Page Permission Update DTO
/// DTO تحديث صلاحية الصفحة
/// </summary>
public class PagePermissionUpdateDto
{
    /// <summary>
    /// Page name
    /// اسم الصفحة
    /// </summary>
    [Required(ErrorMessage = "Page name is required")]
    public string PageName { get; set; } = string.Empty;

    /// <summary>
    /// Can add permission
    /// صلاحية الإضافة
    /// </summary>
    public bool CanAdd { get; set; }

    /// <summary>
    /// Can edit permission
    /// صلاحية التعديل
    /// </summary>
    public bool CanEdit { get; set; }

    /// <summary>
    /// Can delete permission
    /// صلاحية الحذف
    /// </summary>
    public bool CanDelete { get; set; }

    /// <summary>
    /// Can view permission
    /// صلاحية العرض
    /// </summary>
    public bool CanView { get; set; }
}

/// <summary>
/// Permission Template DTO
/// DTO قالب الصلاحيات
/// </summary>
public class PermissionTemplateDto
{
    /// <summary>
    /// Template name
    /// اسم القالب
    /// </summary>
    public string TemplateName { get; set; } = string.Empty;

    /// <summary>
    /// Template description
    /// وصف القالب
    /// </summary>
    public string? Description { get; set; }

    /// <summary>
    /// Template permissions
    /// صلاحيات القالب
    /// </summary>
    public List<PagePermissionDto> Permissions { get; set; } = new();
}

/// <summary>
/// Available Pages DTO
/// DTO الصفحات المتاحة
/// </summary>
public class AvailablePagesDto
{
    /// <summary>
    /// All available pages in the system
    /// جميع الصفحات المتاحة في النظام
    /// </summary>
    public List<SystemPageDto> Pages { get; set; } = new();

    /// <summary>
    /// Pages grouped by category
    /// الصفحات مجمعة حسب الفئة
    /// </summary>
    public Dictionary<string, List<SystemPageDto>> PagesByCategory { get; set; } = new();
}

/// <summary>
/// System Page DTO
/// DTO صفحة النظام
/// </summary>
public class SystemPageDto
{
    /// <summary>
    /// Page name (internal identifier)
    /// اسم الصفحة (المعرف الداخلي)
    /// </summary>
    public string PageName { get; set; } = string.Empty;

    /// <summary>
    /// Display name (user-friendly)
    /// اسم العرض (سهل الاستخدام)
    /// </summary>
    public string DisplayName { get; set; } = string.Empty;

    /// <summary>
    /// Arabic display name
    /// اسم العرض بالعربية
    /// </summary>
    public string DisplayNameArabic { get; set; } = string.Empty;

    /// <summary>
    /// Page category/module
    /// فئة/وحدة الصفحة
    /// </summary>
    public string Category { get; set; } = string.Empty;

    /// <summary>
    /// Page description
    /// وصف الصفحة
    /// </summary>
    public string? Description { get; set; }

    /// <summary>
    /// Whether this page requires special permissions
    /// ما إذا كانت هذه الصفحة تتطلب صلاحيات خاصة
    /// </summary>
    public bool RequiresSpecialPermissions { get; set; }

    /// <summary>
    /// Sort order for display
    /// ترتيب الفرز للعرض
    /// </summary>
    public int SortOrder { get; set; }
}
