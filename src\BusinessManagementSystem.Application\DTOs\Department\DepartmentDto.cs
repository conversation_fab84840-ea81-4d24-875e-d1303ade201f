using BusinessManagementSystem.Application.DTOs.Common;

namespace BusinessManagementSystem.Application.DTOs.Department;

/// <summary>
/// Department DTO for data transfer
/// DTO القسم لنقل البيانات
/// </summary>
public class DepartmentDto : BaseDto
{
    /// <summary>
    /// Department name
    /// اسم القسم
    /// </summary>
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// Department description
    /// وصف القسم
    /// </summary>
    public string? Description { get; set; }

    /// <summary>
    /// Whether the department is active
    /// ما إذا كان القسم نشطًا
    /// </summary>
    public bool IsActive { get; set; }

    /// <summary>
    /// Company ID
    /// معرف الشركة
    /// </summary>
    public int CompanyId { get; set; }

    /// <summary>
    /// Company name
    /// اسم الشركة
    /// </summary>
    public string CompanyName { get; set; } = string.Empty;
}

/// <summary>
/// Create Department DTO
/// DTO إنشاء القسم
/// </summary>
public class CreateDepartmentDto
{
    /// <summary>
    /// Department name
    /// اسم القسم
    /// </summary>
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// Department description
    /// وصف القسم
    /// </summary>
    public string? Description { get; set; }

    /// <summary>
    /// Whether the department is active
    /// ما إذا كان القسم نشطًا
    /// </summary>
    public bool IsActive { get; set; } = true;

    /// <summary>
    /// Company ID
    /// معرف الشركة
    /// </summary>
    public int CompanyId { get; set; }
}

/// <summary>
/// Update Department DTO
/// DTO تحديث القسم
/// </summary>
public class UpdateDepartmentDto
{
    /// <summary>
    /// Department name
    /// اسم القسم
    /// </summary>
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// Department description
    /// وصف القسم
    /// </summary>
    public string? Description { get; set; }

    /// <summary>
    /// Whether the department is active
    /// ما إذا كان القسم نشطًا
    /// </summary>
    public bool IsActive { get; set; }
}
