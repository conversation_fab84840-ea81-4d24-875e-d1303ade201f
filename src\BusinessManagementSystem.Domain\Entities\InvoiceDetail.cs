using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using BusinessManagementSystem.Domain.Common;

namespace BusinessManagementSystem.Domain.Entities;

/// <summary>
/// Invoice detail entity - stores invoice line items
/// كيان تفاصيل الفاتورة - يخزن بنود الفاتورة
/// </summary>
public class InvoiceDetail : BaseEntity
{
    /// <summary>
    /// Item quantity
    /// كمية الصنف
    /// </summary>
    public int Quantity { get; set; }

    /// <summary>
    /// Unit price at time of invoice
    /// سعر الوحدة وقت الفاتورة
    /// </summary>
    [Column(TypeName = "decimal(18,2)")]
    public decimal UnitPrice { get; set; }

    /// <summary>
    /// Line total (Quantity * UnitPrice - Discount)
    /// إجمالي السطر (الكمية × سعر الوحدة - الخصم)
    /// </summary>
    [Column(TypeName = "decimal(18,2)")]
    public decimal LineTotal { get; set; }

    /// <summary>
    /// Discount percentage for this line
    /// نسبة الخصم لهذا السطر
    /// </summary>
    [Column(TypeName = "decimal(5,2)")]
    public decimal DiscountPercentage { get; set; }

    /// <summary>
    /// Invoice identifier (Foreign Key)
    /// معرف الفاتورة (مفتاح خارجي)
    /// </summary>
    [Required]
    public int InvoiceId { get; set; }

    /// <summary>
    /// Item identifier (Foreign Key)
    /// معرف الصنف (مفتاح خارجي)
    /// </summary>
    [Required]
    public int ItemId { get; set; }

    // Navigation properties
    [ForeignKey(nameof(InvoiceId))]
    public virtual Invoice Invoice { get; set; } = null!;

    [ForeignKey(nameof(ItemId))]
    public virtual Item Item { get; set; } = null!;
}
