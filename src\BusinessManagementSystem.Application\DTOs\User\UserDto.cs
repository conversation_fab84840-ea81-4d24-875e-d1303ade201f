using BusinessManagementSystem.Application.DTOs.Common;

namespace BusinessManagementSystem.Application.DTOs.User;

/// <summary>
/// User data transfer object
/// كائن نقل بيانات المستخدم
/// </summary>
public class UserDto : BaseDto
{
    /// <summary>
    /// Username for login
    /// اسم المستخدم لتسجيل الدخول
    /// </summary>
    public string Username { get; set; } = string.Empty;

    /// <summary>
    /// Full name of the user
    /// الاسم الكامل للمستخدم
    /// </summary>
    public string FullName { get; set; } = string.Empty;

    /// <summary>
    /// Email address
    /// عنوان البريد الإلكتروني
    /// </summary>
    public string? Email { get; set; }

    /// <summary>
    /// Whether the user account is active
    /// ما إذا كان حساب المستخدم نشطًا
    /// </summary>
    public bool IsActive { get; set; }

    /// <summary>
    /// Last login timestamp
    /// طابع زمني لآخر تسجيل دخول
    /// </summary>
    public DateTime? LastLoginAt { get; set; }

    /// <summary>
    /// Role identifier
    /// معرف الدور
    /// </summary>
    public int RoleId { get; set; }

    /// <summary>
    /// Role name
    /// اسم الدور
    /// </summary>
    public string RoleName { get; set; } = string.Empty;

    /// <summary>
    /// Company identifier
    /// معرف الشركة
    /// </summary>
    public int CompanyId { get; set; }

    /// <summary>
    /// Company name
    /// اسم الشركة
    /// </summary>
    public string CompanyName { get; set; } = string.Empty;
}

/// <summary>
/// User creation DTO
/// DTO إنشاء المستخدم
/// </summary>
public class CreateUserDto
{
    /// <summary>
    /// Username for login
    /// اسم المستخدم لتسجيل الدخول
    /// </summary>
    public string Username { get; set; } = string.Empty;

    /// <summary>
    /// Password
    /// كلمة المرور
    /// </summary>
    public string Password { get; set; } = string.Empty;

    /// <summary>
    /// Full name of the user
    /// الاسم الكامل للمستخدم
    /// </summary>
    public string FullName { get; set; } = string.Empty;

    /// <summary>
    /// Email address
    /// عنوان البريد الإلكتروني
    /// </summary>
    public string? Email { get; set; }

    /// <summary>
    /// Whether the user account is active
    /// ما إذا كان حساب المستخدم نشطًا
    /// </summary>
    public bool IsActive { get; set; } = true;

    /// <summary>
    /// Role identifier
    /// معرف الدور
    /// </summary>
    public int RoleId { get; set; }

    /// <summary>
    /// Company identifier
    /// معرف الشركة
    /// </summary>
    public int CompanyId { get; set; }
}

/// <summary>
/// User update DTO
/// DTO تحديث المستخدم
/// </summary>
public class UpdateUserDto
{
    /// <summary>
    /// Full name of the user
    /// الاسم الكامل للمستخدم
    /// </summary>
    public string FullName { get; set; } = string.Empty;

    /// <summary>
    /// Email address
    /// عنوان البريد الإلكتروني
    /// </summary>
    public string? Email { get; set; }

    /// <summary>
    /// Whether the user account is active
    /// ما إذا كان حساب المستخدم نشطًا
    /// </summary>
    public bool IsActive { get; set; }

    /// <summary>
    /// Role identifier
    /// معرف الدور
    /// </summary>
    public int RoleId { get; set; }
}

/// <summary>
/// Change password DTO
/// DTO تغيير كلمة المرور
/// </summary>
public class ChangePasswordDto
{
    /// <summary>
    /// Current password
    /// كلمة المرور الحالية
    /// </summary>
    public string CurrentPassword { get; set; } = string.Empty;

    /// <summary>
    /// New password
    /// كلمة المرور الجديدة
    /// </summary>
    public string NewPassword { get; set; } = string.Empty;

    /// <summary>
    /// Confirm new password
    /// تأكيد كلمة المرور الجديدة
    /// </summary>
    public string ConfirmNewPassword { get; set; } = string.Empty;
}
