using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using BusinessManagementSystem.Domain.Common;

namespace BusinessManagementSystem.Domain.Entities;

/// <summary>
/// Inventory transaction entity - tracks inventory movements
/// كيان معاملة المخزون - يتتبع حركات المخزون
/// </summary>
public class InventoryTransaction : BaseEntity
{
    /// <summary>
    /// Transaction date
    /// تاريخ المعاملة
    /// </summary>
    [Required]
    public DateTime TransactionDate { get; set; }

    /// <summary>
    /// Transaction type (In/Out)
    /// نوع المعاملة (دخول/خروج)
    /// </summary>
    [Required]
    [MaxLength(10)]
    public string TransactionType { get; set; } = string.Empty;

    /// <summary>
    /// Quantity moved
    /// الكمية المنقولة
    /// </summary>
    public int Quantity { get; set; }

    /// <summary>
    /// Reference number for tracking
    /// رقم المرجع للتتبع
    /// </summary>
    [MaxLength(50)]
    public string? ReferenceNumber { get; set; }

    /// <summary>
    /// Additional notes
    /// ملاحظات إضافية
    /// </summary>
    [MaxLength(1000)]
    public string? Notes { get; set; }

    /// <summary>
    /// Item identifier (Foreign Key)
    /// معرف الصنف (مفتاح خارجي)
    /// </summary>
    [Required]
    public int ItemId { get; set; }

    /// <summary>
    /// Warehouse identifier (Foreign Key)
    /// معرف المستودع (مفتاح خارجي)
    /// </summary>
    [Required]
    public int WarehouseId { get; set; }

    /// <summary>
    /// Company identifier (Foreign Key)
    /// معرف الشركة (مفتاح خارجي)
    /// </summary>
    [Required]
    public int CompanyId { get; set; }

    // Navigation properties
    [ForeignKey(nameof(ItemId))]
    public virtual Item Item { get; set; } = null!;

    [ForeignKey(nameof(WarehouseId))]
    public virtual Warehouse Warehouse { get; set; } = null!;

    [ForeignKey(nameof(CompanyId))]
    public virtual Company Company { get; set; } = null!;
}
