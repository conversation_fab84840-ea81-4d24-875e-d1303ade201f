using BusinessManagementSystem.Domain.Entities;

namespace BusinessManagementSystem.Application.Interfaces.Repositories;

/// <summary>
/// Inventory transaction repository interface with specific operations
/// واجهة مستودع معاملة المخزون مع العمليات المخصصة
/// </summary>
public interface IInventoryTransactionRepository : IGenericRepository<InventoryTransaction>
{
    /// <summary>
    /// Get inventory transactions by item
    /// الحصول على معاملات المخزون بواسطة الصنف
    /// </summary>
    Task<IEnumerable<InventoryTransaction>> GetByItemAsync(int itemId, CancellationToken cancellationToken = default);

    /// <summary>
    /// Get inventory transactions by warehouse
    /// الحصول على معاملات المخزون بواسطة المستودع
    /// </summary>
    Task<IEnumerable<InventoryTransaction>> GetByWarehouseAsync(int warehouseId, CancellationToken cancellationToken = default);

    /// <summary>
    /// Get inventory transactions by date range
    /// الحصول على معاملات المخزون بواسطة نطاق التاريخ
    /// </summary>
    Task<IEnumerable<InventoryTransaction>> GetByDateRangeAsync(int companyId, DateTime startDate, DateTime endDate, CancellationToken cancellationToken = default);

    /// <summary>
    /// Get current stock level for an item in a warehouse
    /// الحصول على مستوى المخزون الحالي لصنف في مستودع
    /// </summary>
    Task<int> GetCurrentStockLevelAsync(int itemId, int warehouseId, CancellationToken cancellationToken = default);

    /// <summary>
    /// Get stock levels for all items in a warehouse
    /// الحصول على مستويات المخزون لجميع الأصناف في مستودع
    /// </summary>
    Task<Dictionary<int, int>> GetStockLevelsByWarehouseAsync(int warehouseId, CancellationToken cancellationToken = default);

    /// <summary>
    /// Get stock movements for an item across all warehouses
    /// الحصول على حركات المخزون لصنف عبر جميع المستودعات
    /// </summary>
    Task<IEnumerable<InventoryTransaction>> GetItemMovementsAsync(int itemId, DateTime? startDate = null, DateTime? endDate = null, CancellationToken cancellationToken = default);
}
