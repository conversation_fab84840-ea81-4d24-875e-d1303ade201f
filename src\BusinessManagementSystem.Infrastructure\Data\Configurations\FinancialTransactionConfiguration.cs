using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using BusinessManagementSystem.Domain.Entities;

namespace BusinessManagementSystem.Infrastructure.Data.Configurations;

/// <summary>
/// Entity configuration for FinancialTransaction
/// تكوين الكيان للمعاملة المالية
/// </summary>
public class FinancialTransactionConfiguration : IEntityTypeConfiguration<FinancialTransaction>
{
    public void Configure(EntityTypeBuilder<FinancialTransaction> builder)
    {
        builder.ToTable("FinancialTransactions");

        builder.HasKey(ft => ft.Id);

        builder.Property(ft => ft.TransactionDate)
            .IsRequired();

        builder.Property(ft => ft.Amount)
            .HasColumnType("decimal(18,2)")
            .IsRequired();

        builder.Property(ft => ft.TransactionType)
            .IsRequired()
            .HasMaxLength(20);

        builder.Property(ft => ft.Description)
            .HasMaxLength(1000);

        builder.Property(ft => ft.ReferenceNumber)
            .HasMaxLength(50);

        // Indexes
        builder.HasIndex(ft => ft.TransactionDate);

        builder.HasIndex(ft => new { ft.CompanyId, ft.TransactionType, ft.TransactionDate });

        builder.HasIndex(ft => ft.ReferenceNumber)
            .HasFilter("[ReferenceNumber] IS NOT NULL");

        // Relationships
        builder.HasOne(ft => ft.Company)
            .WithMany(c => c.FinancialTransactions)
            .HasForeignKey(ft => ft.CompanyId)
            .OnDelete(DeleteBehavior.Cascade);

        builder.HasOne(ft => ft.MainCash)
            .WithMany(mc => mc.FinancialTransactions)
            .HasForeignKey(ft => ft.MainCashId)
            .OnDelete(DeleteBehavior.Restrict);
    }
}
