using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using BusinessManagementSystem.Domain.Common;

namespace BusinessManagementSystem.Domain.Entities;

/// <summary>
/// Warehouse entity - stores warehouse information
/// كيان المستودع - يخزن معلومات المستودعات
/// </summary>
public class Warehouse : BaseEntity
{
    /// <summary>
    /// Warehouse name
    /// اسم المستودع
    /// </summary>
    [Required]
    [MaxLength(200)]
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// Warehouse address
    /// عنوان المستودع
    /// </summary>
    [MaxLength(500)]
    public string? Address { get; set; }

    /// <summary>
    /// Company identifier (Foreign Key)
    /// معرف الشركة (مفتاح خارجي)
    /// </summary>
    [Required]
    public int CompanyId { get; set; }

    // Navigation properties
    [ForeignKey(nameof(CompanyId))]
    public virtual Company Company { get; set; } = null!;
    
    public virtual ICollection<InventoryTransaction> InventoryTransactions { get; set; } = new List<InventoryTransaction>();
}
