using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using BusinessManagementSystem.Domain.Common;

namespace BusinessManagementSystem.Domain.Entities;

/// <summary>
/// Warehouse entity - stores warehouse information
/// كيان المستودع - يخزن معلومات المستودعات
/// </summary>
public class Warehouse : BaseEntity
{
    /// <summary>
    /// Warehouse name
    /// اسم المستودع
    /// </summary>
    [Required]
    [MaxLength(200)]
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// Warehouse description
    /// وصف المستودع
    /// </summary>
    [MaxLength(500)]
    public string? Description { get; set; }

    /// <summary>
    /// Warehouse location
    /// موقع المستودع
    /// </summary>
    [MaxLength(300)]
    public string? Location { get; set; }

    /// <summary>
    /// Maximum capacity of the warehouse
    /// السعة القصوى للمستودع
    /// </summary>
    [Column(TypeName = "decimal(18,2)")]
    public decimal MaxCapacity { get; set; }

    /// <summary>
    /// Current utilization of the warehouse
    /// الاستخدام الحالي للمستودع
    /// </summary>
    [Column(TypeName = "decimal(18,2)")]
    public decimal CurrentUtilization { get; set; }

    /// <summary>
    /// Whether the warehouse is active
    /// ما إذا كان المستودع نشطًا
    /// </summary>
    public bool IsActive { get; set; } = true;

    /// <summary>
    /// Company identifier (Foreign Key)
    /// معرف الشركة (مفتاح خارجي)
    /// </summary>
    [Required]
    public int CompanyId { get; set; }

    // Navigation properties
    [ForeignKey(nameof(CompanyId))]
    public virtual Company Company { get; set; } = null!;
    
    public virtual ICollection<InventoryTransaction> InventoryTransactions { get; set; } = new List<InventoryTransaction>();
}
