using AutoMapper;
using BusinessManagementSystem.Application.DTOs.Invoice;
using BusinessManagementSystem.Application.Interfaces;
using BusinessManagementSystem.Application.Services.Interfaces;
using BusinessManagementSystem.Domain.Entities;

namespace BusinessManagementSystem.Application.Services.Implementations;

/// <summary>
/// Invoice service implementation for business logic operations
/// تنفيذ خدمة الفاتورة لعمليات منطق الأعمال
/// </summary>
public class InvoiceService : IInvoiceService
{
    private readonly IUnitOfWork _unitOfWork;
    private readonly IMapper _mapper;

    public InvoiceService(IUnitOfWork unitOfWork, IMapper mapper)
    {
        _unitOfWork = unitOfWork;
        _mapper = mapper;
    }

    public async Task<IEnumerable<InvoiceDto>> GetAllAsync(CancellationToken cancellationToken = default)
    {
        var invoices = await _unitOfWork.Invoices.GetAsync(
            includeProperties: "Person,Company,InvoiceDetails,InvoiceDetails.Item",
            cancellationToken: cancellationToken);
        return _mapper.Map<IEnumerable<InvoiceDto>>(invoices);
    }

    public async Task<InvoiceDto?> GetByIdAsync(int id, CancellationToken cancellationToken = default)
    {
        var invoice = await _unitOfWork.Invoices.GetWithDetailsAsync(id, cancellationToken);
        return invoice != null ? _mapper.Map<InvoiceDto>(invoice) : null;
    }

    public async Task<InvoiceDto?> GetByInvoiceNumberAsync(string invoiceNumber, CancellationToken cancellationToken = default)
    {
        var invoice = await _unitOfWork.Invoices.GetByInvoiceNumberAsync(invoiceNumber, cancellationToken);
        return invoice != null ? _mapper.Map<InvoiceDto>(invoice) : null;
    }

    public async Task<IEnumerable<InvoiceDto>> GetByCompanyAndTypeAsync(int companyId, string invoiceType, CancellationToken cancellationToken = default)
    {
        var invoices = await _unitOfWork.Invoices.GetByCompanyAndTypeAsync(companyId, invoiceType, cancellationToken);
        return _mapper.Map<IEnumerable<InvoiceDto>>(invoices);
    }

    public async Task<IEnumerable<InvoiceDto>> GetByPersonAsync(int personId, CancellationToken cancellationToken = default)
    {
        var invoices = await _unitOfWork.Invoices.GetByPersonAsync(personId, cancellationToken);
        return _mapper.Map<IEnumerable<InvoiceDto>>(invoices);
    }

    public async Task<IEnumerable<InvoiceDto>> GetByDateRangeAsync(int companyId, DateTime startDate, DateTime endDate, CancellationToken cancellationToken = default)
    {
        var invoices = await _unitOfWork.Invoices.GetByDateRangeAsync(companyId, startDate, endDate, cancellationToken);
        return _mapper.Map<IEnumerable<InvoiceDto>>(invoices);
    }

    public async Task<InvoiceDto> CreateAsync(CreateInvoiceDto createInvoiceDto, CancellationToken cancellationToken = default)
    {
        // Validate business rules
        await ValidateInvoiceForCreateAsync(createInvoiceDto, cancellationToken);

        await _unitOfWork.BeginTransactionAsync(cancellationToken);
        try
        {
            var invoice = _mapper.Map<Invoice>(createInvoiceDto);
            
            // Generate invoice number
            invoice.InvoiceNumber = await _unitOfWork.Invoices.GenerateNextInvoiceNumberAsync(
                createInvoiceDto.CompanyId, createInvoiceDto.InvoiceType, cancellationToken);

            // Calculate totals
            CalculateInvoiceTotals(invoice, createInvoiceDto);

            await _unitOfWork.Invoices.AddAsync(invoice, cancellationToken);
            await _unitOfWork.SaveChangesAsync(cancellationToken);

            // Add invoice details
            foreach (var detailDto in createInvoiceDto.InvoiceDetails)
            {
                var detail = _mapper.Map<InvoiceDetail>(detailDto);
                detail.InvoiceId = invoice.Id;
                detail.LineTotal = detail.Quantity * detail.UnitPrice * (1 - detail.DiscountPercentage / 100);
                
                await _unitOfWork.InvoiceDetails.AddAsync(detail, cancellationToken);
            }

            await _unitOfWork.SaveChangesAsync(cancellationToken);
            await _unitOfWork.CommitTransactionAsync(cancellationToken);

            // Return the created invoice with details
            var createdInvoice = await GetByIdAsync(invoice.Id, cancellationToken);
            return createdInvoice!;
        }
        catch
        {
            await _unitOfWork.RollbackTransactionAsync(cancellationToken);
            throw;
        }
    }

    public async Task<InvoiceDto> UpdateAsync(int id, CreateInvoiceDto updateInvoiceDto, CancellationToken cancellationToken = default)
    {
        var existingInvoice = await _unitOfWork.Invoices.GetWithDetailsAsync(id, cancellationToken);
        if (existingInvoice == null)
        {
            throw new ArgumentException($"Invoice with ID {id} not found.", nameof(id));
        }

        if (existingInvoice.Status == "Confirmed")
        {
            throw new InvalidOperationException("Cannot update a confirmed invoice.");
        }

        await _unitOfWork.BeginTransactionAsync(cancellationToken);
        try
        {
            // Update invoice header
            existingInvoice.InvoiceDate = updateInvoiceDto.InvoiceDate;
            existingInvoice.InvoiceType = updateInvoiceDto.InvoiceType;
            existingInvoice.TaxAmount = updateInvoiceDto.TaxAmount;
            existingInvoice.DiscountAmount = updateInvoiceDto.DiscountAmount;
            existingInvoice.Notes = updateInvoiceDto.Notes;
            existingInvoice.PersonId = updateInvoiceDto.PersonId;

            // Remove existing details
            foreach (var detail in existingInvoice.InvoiceDetails.ToList())
            {
                _unitOfWork.InvoiceDetails.Delete(detail);
            }

            // Calculate new totals
            var tempInvoice = _mapper.Map<Invoice>(updateInvoiceDto);
            CalculateInvoiceTotals(tempInvoice, updateInvoiceDto);
            existingInvoice.TotalAmount = tempInvoice.TotalAmount;

            _unitOfWork.Invoices.Update(existingInvoice);
            await _unitOfWork.SaveChangesAsync(cancellationToken);

            // Add new details
            foreach (var detailDto in updateInvoiceDto.InvoiceDetails)
            {
                var detail = _mapper.Map<InvoiceDetail>(detailDto);
                detail.InvoiceId = existingInvoice.Id;
                detail.LineTotal = detail.Quantity * detail.UnitPrice * (1 - detail.DiscountPercentage / 100);
                
                await _unitOfWork.InvoiceDetails.AddAsync(detail, cancellationToken);
            }

            await _unitOfWork.SaveChangesAsync(cancellationToken);
            await _unitOfWork.CommitTransactionAsync(cancellationToken);

            // Return the updated invoice with details
            var updatedInvoice = await GetByIdAsync(id, cancellationToken);
            return updatedInvoice!;
        }
        catch
        {
            await _unitOfWork.RollbackTransactionAsync(cancellationToken);
            throw;
        }
    }

    public async Task<bool> ConfirmInvoiceAsync(int id, CancellationToken cancellationToken = default)
    {
        var invoice = await _unitOfWork.Invoices.GetByIdAsync(id, cancellationToken);
        if (invoice == null || invoice.Status != "Draft")
        {
            return false;
        }

        invoice.Status = "Confirmed";
        _unitOfWork.Invoices.Update(invoice);
        await _unitOfWork.SaveChangesAsync(cancellationToken);

        return true;
    }

    public async Task<bool> CancelInvoiceAsync(int id, CancellationToken cancellationToken = default)
    {
        var invoice = await _unitOfWork.Invoices.GetByIdAsync(id, cancellationToken);
        if (invoice == null)
        {
            return false;
        }

        invoice.Status = "Cancelled";
        _unitOfWork.Invoices.Update(invoice);
        await _unitOfWork.SaveChangesAsync(cancellationToken);

        return true;
    }

    public async Task<bool> DeleteAsync(int id, CancellationToken cancellationToken = default)
    {
        var invoice = await _unitOfWork.Invoices.GetByIdAsync(id, cancellationToken);
        if (invoice == null)
        {
            return false;
        }

        if (invoice.Status == "Confirmed")
        {
            throw new InvalidOperationException("Cannot delete a confirmed invoice.");
        }

        _unitOfWork.Invoices.Delete(invoice);
        await _unitOfWork.SaveChangesAsync(cancellationToken);
        return true;
    }

    public async Task<decimal> GetSalesTotalByDateRangeAsync(int companyId, DateTime startDate, DateTime endDate, CancellationToken cancellationToken = default)
    {
        return await _unitOfWork.Invoices.GetSalesTotalByDateRangeAsync(companyId, startDate, endDate, cancellationToken);
    }

    public async Task<decimal> GetPurchasesTotalByDateRangeAsync(int companyId, DateTime startDate, DateTime endDate, CancellationToken cancellationToken = default)
    {
        return await _unitOfWork.Invoices.GetPurchasesTotalByDateRangeAsync(companyId, startDate, endDate, cancellationToken);
    }

    public async Task<string> GenerateNextInvoiceNumberAsync(int companyId, string invoiceType, CancellationToken cancellationToken = default)
    {
        return await _unitOfWork.Invoices.GenerateNextInvoiceNumberAsync(companyId, invoiceType, cancellationToken);
    }

    private async Task ValidateInvoiceForCreateAsync(CreateInvoiceDto createInvoiceDto, CancellationToken cancellationToken)
    {
        // Validate person exists
        var person = await _unitOfWork.Persons.GetByIdAsync(createInvoiceDto.PersonId, cancellationToken);
        if (person == null)
        {
            throw new ArgumentException("Invalid person ID.", nameof(createInvoiceDto.PersonId));
        }

        // Validate company exists
        var company = await _unitOfWork.Companies.GetByIdAsync(createInvoiceDto.CompanyId, cancellationToken);
        if (company == null)
        {
            throw new ArgumentException("Invalid company ID.", nameof(createInvoiceDto.CompanyId));
        }

        // Validate invoice details
        if (!createInvoiceDto.InvoiceDetails.Any())
        {
            throw new ArgumentException("Invoice must have at least one detail line.");
        }

        foreach (var detail in createInvoiceDto.InvoiceDetails)
        {
            var item = await _unitOfWork.Items.GetByIdAsync(detail.ItemId, cancellationToken);
            if (item == null)
            {
                throw new ArgumentException($"Invalid item ID: {detail.ItemId}");
            }
        }
    }

    private void CalculateInvoiceTotals(Invoice invoice, CreateInvoiceDto createInvoiceDto)
    {
        decimal totalAmount = 0;

        foreach (var detailDto in createInvoiceDto.InvoiceDetails)
        {
            var lineTotal = detailDto.Quantity * detailDto.UnitPrice * (1 - detailDto.DiscountPercentage / 100);
            totalAmount += lineTotal;
        }

        invoice.TotalAmount = totalAmount;
    }
}
