using BusinessManagementSystem.Application.DTOs.User;

namespace BusinessManagementSystem.Application.Services.Interfaces;

/// <summary>
/// User service interface for business logic operations
/// واجهة خدمة المستخدم لعمليات منطق الأعمال
/// </summary>
public interface IUserService
{
    /// <summary>
    /// Get all users
    /// الحصول على جميع المستخدمين
    /// </summary>
    Task<IEnumerable<UserDto>> GetAllAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// Get user by ID
    /// الحصول على المستخدم بواسطة المعرف
    /// </summary>
    Task<UserDto?> GetByIdAsync(int id, CancellationToken cancellationToken = default);

    /// <summary>
    /// Get user by username
    /// الحصول على المستخدم بواسطة اسم المستخدم
    /// </summary>
    Task<UserDto?> GetByUsernameAsync(string username, CancellationToken cancellationToken = default);

    /// <summary>
    /// Get users by company
    /// الحصول على المستخدمين بواسطة الشركة
    /// </summary>
    Task<IEnumerable<UserDto>> GetByCompanyAsync(int companyId, CancellationToken cancellationToken = default);

    /// <summary>
    /// Get active users by company
    /// الحصول على المستخدمين النشطين بواسطة الشركة
    /// </summary>
    Task<IEnumerable<UserDto>> GetActiveByCompanyAsync(int companyId, CancellationToken cancellationToken = default);

    /// <summary>
    /// Create new user
    /// إنشاء مستخدم جديد
    /// </summary>
    Task<UserDto> CreateAsync(CreateUserDto createUserDto, CancellationToken cancellationToken = default);

    /// <summary>
    /// Update existing user
    /// تحديث مستخدم موجود
    /// </summary>
    Task<UserDto> UpdateAsync(int id, UpdateUserDto updateUserDto, CancellationToken cancellationToken = default);

    /// <summary>
    /// Change user password
    /// تغيير كلمة مرور المستخدم
    /// </summary>
    Task<bool> ChangePasswordAsync(int userId, ChangePasswordDto changePasswordDto, CancellationToken cancellationToken = default);

    /// <summary>
    /// Activate or deactivate user
    /// تفعيل أو إلغاء تفعيل المستخدم
    /// </summary>
    Task<bool> SetActiveStatusAsync(int id, bool isActive, CancellationToken cancellationToken = default);

    /// <summary>
    /// Delete user
    /// حذف المستخدم
    /// </summary>
    Task<bool> DeleteAsync(int id, CancellationToken cancellationToken = default);

    /// <summary>
    /// Check if username exists
    /// التحقق من وجود اسم المستخدم
    /// </summary>
    Task<bool> IsUsernameExistsAsync(string username, int? excludeId = null, CancellationToken cancellationToken = default);

    /// <summary>
    /// Check if email exists
    /// التحقق من وجود البريد الإلكتروني
    /// </summary>
    Task<bool> IsEmailExistsAsync(string email, int? excludeId = null, CancellationToken cancellationToken = default);

    /// <summary>
    /// Verify user password
    /// التحقق من كلمة مرور المستخدم
    /// </summary>
    Task<bool> VerifyPasswordAsync(int userId, string password, CancellationToken cancellationToken = default);

    /// <summary>
    /// Update user's last login time
    /// تحديث وقت آخر تسجيل دخول للمستخدم
    /// </summary>
    Task UpdateLastLoginAsync(int userId, CancellationToken cancellationToken = default);
}
