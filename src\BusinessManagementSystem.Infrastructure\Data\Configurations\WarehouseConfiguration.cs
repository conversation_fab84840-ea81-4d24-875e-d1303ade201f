using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using BusinessManagementSystem.Domain.Entities;

namespace BusinessManagementSystem.Infrastructure.Data.Configurations;

/// <summary>
/// Entity configuration for Warehouse
/// تكوين الكيان للمستودع
/// </summary>
public class WarehouseConfiguration : IEntityTypeConfiguration<Warehouse>
{
    public void Configure(EntityTypeBuilder<Warehouse> builder)
    {
        builder.ToTable("Warehouses");

        builder.HasKey(w => w.Id);

        builder.Property(w => w.Name)
            .IsRequired()
            .HasMaxLength(200);

        builder.Property(w => w.Description)
            .HasMaxLength(500);

        builder.Property(w => w.Location)
            .HasMaxLength(300);

        builder.Property(w => w.MaxCapacity)
            .HasColumnType("decimal(18,2)");

        builder.Property(w => w.CurrentUtilization)
            .HasColumnType("decimal(18,2)");

        // Indexes
        builder.HasIndex(w => new { w.CompanyId, w.Name })
            .IsUnique();

        // Relationships
        builder.HasOne(w => w.Company)
            .WithMany(c => c.Warehouses)
            .HasForeignKey(w => w.CompanyId)
            .OnDelete(DeleteBehavior.Cascade);

        builder.HasMany(w => w.InventoryTransactions)
            .WithOne(it => it.Warehouse)
            .HasForeignKey(it => it.WarehouseId)
            .OnDelete(DeleteBehavior.Restrict);
    }
}
