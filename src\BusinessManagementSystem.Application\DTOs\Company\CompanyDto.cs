using BusinessManagementSystem.Application.DTOs.Common;

namespace BusinessManagementSystem.Application.DTOs.Company;

/// <summary>
/// Company data transfer object
/// كائن نقل بيانات الشركة
/// </summary>
public class CompanyDto : BaseDto
{
    /// <summary>
    /// Company name
    /// اسم الشركة
    /// </summary>
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// Company description
    /// وصف الشركة
    /// </summary>
    public string? Description { get; set; }

    /// <summary>
    /// Company address
    /// عنوان الشركة
    /// </summary>
    public string? Address { get; set; }

    /// <summary>
    /// Logo file path
    /// مسار ملف الشعار
    /// </summary>
    public string? LogoPath { get; set; }

    /// <summary>
    /// Commercial registration number
    /// رقم السجل التجاري
    /// </summary>
    public string? CommercialRegistrationNumber { get; set; }

    /// <summary>
    /// Tax identification number
    /// الرقم الضريبي
    /// </summary>
    public string? TaxNumber { get; set; }

    /// <summary>
    /// Insurance number
    /// رقم التأمين
    /// </summary>
    public string? InsuranceNumber { get; set; }

    /// <summary>
    /// Phone number
    /// رقم الهاتف
    /// </summary>
    public string? Phone { get; set; }
}

/// <summary>
/// Company creation DTO
/// DTO إنشاء الشركة
/// </summary>
public class CreateCompanyDto
{
    /// <summary>
    /// Company name
    /// اسم الشركة
    /// </summary>
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// Company description
    /// وصف الشركة
    /// </summary>
    public string? Description { get; set; }

    /// <summary>
    /// Company address
    /// عنوان الشركة
    /// </summary>
    public string? Address { get; set; }

    /// <summary>
    /// Logo file path
    /// مسار ملف الشعار
    /// </summary>
    public string? LogoPath { get; set; }

    /// <summary>
    /// Commercial registration number
    /// رقم السجل التجاري
    /// </summary>
    public string? CommercialRegistrationNumber { get; set; }

    /// <summary>
    /// Tax identification number
    /// الرقم الضريبي
    /// </summary>
    public string? TaxNumber { get; set; }

    /// <summary>
    /// Insurance number
    /// رقم التأمين
    /// </summary>
    public string? InsuranceNumber { get; set; }

    /// <summary>
    /// Phone number
    /// رقم الهاتف
    /// </summary>
    public string? Phone { get; set; }
}

/// <summary>
/// Company update DTO
/// DTO تحديث الشركة
/// </summary>
public class UpdateCompanyDto
{
    /// <summary>
    /// Company name
    /// اسم الشركة
    /// </summary>
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// Company description
    /// وصف الشركة
    /// </summary>
    public string? Description { get; set; }

    /// <summary>
    /// Company address
    /// عنوان الشركة
    /// </summary>
    public string? Address { get; set; }

    /// <summary>
    /// Logo file path
    /// مسار ملف الشعار
    /// </summary>
    public string? LogoPath { get; set; }

    /// <summary>
    /// Commercial registration number
    /// رقم السجل التجاري
    /// </summary>
    public string? CommercialRegistrationNumber { get; set; }

    /// <summary>
    /// Tax identification number
    /// الرقم الضريبي
    /// </summary>
    public string? TaxNumber { get; set; }

    /// <summary>
    /// Insurance number
    /// رقم التأمين
    /// </summary>
    public string? InsuranceNumber { get; set; }

    /// <summary>
    /// Phone number
    /// رقم الهاتف
    /// </summary>
    public string? Phone { get; set; }
}
