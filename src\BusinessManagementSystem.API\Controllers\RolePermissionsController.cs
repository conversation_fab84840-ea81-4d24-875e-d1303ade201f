using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using BusinessManagementSystem.Application.DTOs.RolePermission;
using BusinessManagementSystem.Application.Services.Interfaces;
using BusinessManagementSystem.API.Controllers.Base;

namespace BusinessManagementSystem.API.Controllers;

/// <summary>
/// Role Permissions controller for granular permission management
/// تحكم صلاحيات الأدوار لإدارة الصلاحيات التفصيلية
/// </summary>
[ApiController]
[Route("api/[controller]")]
[Authorize]
public class RolePermissionsController : BaseController
{
    private readonly IRolePermissionService _rolePermissionService;

    public RolePermissionsController(IRolePermissionService rolePermissionService)
    {
        _rolePermissionService = rolePermissionService;
    }

    /// <summary>
    /// Get all role permissions
    /// الحصول على جميع صلاحيات الأدوار
    /// </summary>
    [HttpGet]
    [Authorize(Roles = "Administrator")]
    public async Task<ActionResult<IEnumerable<RolePermissionDto>>> GetAll(CancellationToken cancellationToken = default)
    {
        try
        {
            var permissions = await _rolePermissionService.GetAllAsync(cancellationToken);
            return Ok(permissions);
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error occurred while getting all role permissions");
            return StatusCode(500, "An error occurred while processing your request.");
        }
    }

    /// <summary>
    /// Get role permission by ID
    /// الحصول على صلاحية الدور بواسطة المعرف
    /// </summary>
    [HttpGet("{id}")]
    [Authorize(Roles = "Administrator")]
    public async Task<ActionResult<RolePermissionDto>> GetById(int id, CancellationToken cancellationToken = default)
    {
        try
        {
            var permission = await _rolePermissionService.GetByIdAsync(id, cancellationToken);
            if (permission == null)
            {
                return NotFound($"Role permission with ID {id} not found.");
            }

            return Ok(permission);
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error occurred while getting role permission with ID: {PermissionId}", id);
            return StatusCode(500, "An error occurred while processing your request.");
        }
    }

    /// <summary>
    /// Get permissions by role ID
    /// الحصول على الصلاحيات بواسطة معرف الدور
    /// </summary>
    [HttpGet("role/{roleId}")]
    [Authorize(Roles = "Administrator")]
    public async Task<ActionResult<IEnumerable<RolePermissionDto>>> GetByRoleId(int roleId, CancellationToken cancellationToken = default)
    {
        try
        {
            var permissions = await _rolePermissionService.GetByRoleIdAsync(roleId, cancellationToken);
            return Ok(permissions);
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error occurred while getting permissions for role: {RoleId}", roleId);
            return StatusCode(500, "An error occurred while processing your request.");
        }
    }

    /// <summary>
    /// Get permissions by page name
    /// الحصول على الصلاحيات بواسطة اسم الصفحة
    /// </summary>
    [HttpGet("page/{pageName}")]
    [Authorize(Roles = "Administrator")]
    public async Task<ActionResult<IEnumerable<RolePermissionDto>>> GetByPageName(string pageName, CancellationToken cancellationToken = default)
    {
        try
        {
            var permissions = await _rolePermissionService.GetByPageNameAsync(pageName, cancellationToken);
            return Ok(permissions);
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error occurred while getting permissions for page: {PageName}", pageName);
            return StatusCode(500, "An error occurred while processing your request.");
        }
    }

    /// <summary>
    /// Get permission for specific role and page
    /// الحصول على الصلاحية لدور وصفحة محددين
    /// </summary>
    [HttpGet("role/{roleId}/page/{pageName}")]
    [Authorize(Roles = "Administrator")]
    public async Task<ActionResult<RolePermissionDto>> GetByRoleAndPage(int roleId, string pageName, CancellationToken cancellationToken = default)
    {
        try
        {
            var permission = await _rolePermissionService.GetByRoleAndPageAsync(roleId, pageName, cancellationToken);
            if (permission == null)
            {
                return NotFound($"Permission not found for role {roleId} and page {pageName}.");
            }

            return Ok(permission);
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error occurred while getting permission for role {RoleId} and page {PageName}", roleId, pageName);
            return StatusCode(500, "An error occurred while processing your request.");
        }
    }

    /// <summary>
    /// Create new role permission
    /// إنشاء صلاحية دور جديدة
    /// </summary>
    [HttpPost]
    [Authorize(Roles = "Administrator")]
    public async Task<ActionResult<RolePermissionDto>> Create([FromBody] CreateRolePermissionDto createRolePermissionDto, CancellationToken cancellationToken = default)
    {
        try
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ModelState);
            }

            var permission = await _rolePermissionService.CreateAsync(createRolePermissionDto, cancellationToken);
            
            Logger.LogInformation("Role permission created successfully with ID: {PermissionId} by user: {UserId}", permission.Id, GetCurrentUserId());
            return CreatedAtAction(nameof(GetById), new { id = permission.Id }, permission);
        }
        catch (ArgumentException ex)
        {
            Logger.LogWarning(ex, "Invalid argument while creating role permission");
            return BadRequest(ex.Message);
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error occurred while creating role permission");
            return StatusCode(500, "An error occurred while processing your request.");
        }
    }

    /// <summary>
    /// Update existing role permission
    /// تحديث صلاحية دور موجودة
    /// </summary>
    [HttpPut("{id}")]
    [Authorize(Roles = "Administrator")]
    public async Task<ActionResult<RolePermissionDto>> Update(int id, [FromBody] UpdateRolePermissionDto updateRolePermissionDto, CancellationToken cancellationToken = default)
    {
        try
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ModelState);
            }

            var permission = await _rolePermissionService.UpdateAsync(id, updateRolePermissionDto, cancellationToken);
            
            Logger.LogInformation("Role permission updated successfully with ID: {PermissionId} by user: {UserId}", id, GetCurrentUserId());
            return Ok(permission);
        }
        catch (ArgumentException ex)
        {
            Logger.LogWarning(ex, "Invalid argument while updating role permission with ID: {PermissionId}", id);
            return BadRequest(ex.Message);
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error occurred while updating role permission with ID: {PermissionId}", id);
            return StatusCode(500, "An error occurred while processing your request.");
        }
    }

    /// <summary>
    /// Delete role permission
    /// حذف صلاحية الدور
    /// </summary>
    [HttpDelete("{id}")]
    [Authorize(Roles = "Administrator")]
    public async Task<ActionResult> Delete(int id, CancellationToken cancellationToken = default)
    {
        try
        {
            var result = await _rolePermissionService.DeleteAsync(id, cancellationToken);
            if (!result)
            {
                return NotFound($"Role permission with ID {id} not found.");
            }

            Logger.LogInformation("Role permission deleted successfully with ID: {PermissionId} by user: {UserId}", id, GetCurrentUserId());
            return NoContent();
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error occurred while deleting role permission with ID: {PermissionId}", id);
            return StatusCode(500, "An error occurred while processing your request.");
        }
    }

    /// <summary>
    /// Delete all permissions for a role
    /// حذف جميع صلاحيات الدور
    /// </summary>
    [HttpDelete("role/{roleId}")]
    [Authorize(Roles = "Administrator")]
    public async Task<ActionResult> DeleteByRoleId(int roleId, CancellationToken cancellationToken = default)
    {
        try
        {
            var result = await _rolePermissionService.DeleteByRoleIdAsync(roleId, cancellationToken);
            if (!result)
            {
                return NotFound($"No permissions found for role {roleId}.");
            }

            Logger.LogInformation("All permissions deleted for role {RoleId} by user: {UserId}", roleId, GetCurrentUserId());
            return NoContent();
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error occurred while deleting permissions for role {RoleId}", roleId);
            return StatusCode(500, "An error occurred while processing your request.");
        }
    }

    /// <summary>
    /// Delete all permissions for a page
    /// حذف جميع صلاحيات الصفحة
    /// </summary>
    [HttpDelete("page/{pageName}")]
    [Authorize(Roles = "Administrator")]
    public async Task<ActionResult> DeleteByPageName(string pageName, CancellationToken cancellationToken = default)
    {
        try
        {
            var result = await _rolePermissionService.DeleteByPageNameAsync(pageName, cancellationToken);
            if (!result)
            {
                return NotFound($"No permissions found for page {pageName}.");
            }

            Logger.LogInformation("All permissions deleted for page {PageName} by user: {UserId}", pageName, GetCurrentUserId());
            return NoContent();
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error occurred while deleting permissions for page {PageName}", pageName);
            return StatusCode(500, "An error occurred while processing your request.");
        }
    }

    /// <summary>
    /// Get permission matrix for role
    /// الحصول على مصفوفة الصلاحيات للدور
    /// </summary>
    [HttpGet("matrix/role/{roleId}")]
    [Authorize(Roles = "Administrator")]
    public async Task<ActionResult<PermissionMatrixDto>> GetPermissionMatrix(int roleId, CancellationToken cancellationToken = default)
    {
        try
        {
            var matrix = await _rolePermissionService.GetPermissionMatrixAsync(roleId, cancellationToken);
            return Ok(matrix);
        }
        catch (ArgumentException ex)
        {
            Logger.LogWarning(ex, "Invalid argument while getting permission matrix for role {RoleId}", roleId);
            return BadRequest(ex.Message);
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error occurred while getting permission matrix for role {RoleId}", roleId);
            return StatusCode(500, "An error occurred while processing your request.");
        }
    }

    /// <summary>
    /// Bulk update permissions for role
    /// تحديث الصلاحيات بالجملة للدور
    /// </summary>
    [HttpPut("bulk-update")]
    [Authorize(Roles = "Administrator")]
    public async Task<ActionResult> BulkUpdatePermissions([FromBody] BulkPermissionUpdateDto bulkUpdateDto, CancellationToken cancellationToken = default)
    {
        try
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ModelState);
            }

            var result = await _rolePermissionService.BulkUpdatePermissionsAsync(bulkUpdateDto, cancellationToken);
            if (!result)
            {
                return BadRequest("Failed to update permissions.");
            }

            Logger.LogInformation("Bulk permissions updated for role {RoleId} by user: {UserId}", bulkUpdateDto.RoleId, GetCurrentUserId());
            return Ok(new { Message = "Permissions updated successfully." });
        }
        catch (ArgumentException ex)
        {
            Logger.LogWarning(ex, "Invalid argument while bulk updating permissions");
            return BadRequest(ex.Message);
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error occurred while bulk updating permissions");
            return StatusCode(500, "An error occurred while processing your request.");
        }
    }

    /// <summary>
    /// Copy permissions from one role to another
    /// نسخ الصلاحيات من دور إلى آخر
    /// </summary>
    [HttpPost("copy")]
    [Authorize(Roles = "Administrator")]
    public async Task<ActionResult> CopyPermissions([FromBody] CopyPermissionsRequestDto request, CancellationToken cancellationToken = default)
    {
        try
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ModelState);
            }

            var result = await _rolePermissionService.CopyPermissionsAsync(request.SourceRoleId, request.TargetRoleId, request.ReplaceExisting, cancellationToken);
            if (!result)
            {
                return BadRequest("Failed to copy permissions.");
            }

            Logger.LogInformation("Permissions copied from role {SourceRoleId} to role {TargetRoleId} by user: {UserId}", 
                request.SourceRoleId, request.TargetRoleId, GetCurrentUserId());
            return Ok(new { Message = "Permissions copied successfully." });
        }
        catch (ArgumentException ex)
        {
            Logger.LogWarning(ex, "Invalid argument while copying permissions");
            return BadRequest(ex.Message);
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error occurred while copying permissions");
            return StatusCode(500, "An error occurred while processing your request.");
        }
    }

    /// <summary>
    /// Check if user has permission for specific action
    /// التحقق من وجود صلاحية للمستخدم لإجراء محدد
    /// </summary>
    [HttpGet("check-permission")]
    [Authorize]
    public async Task<ActionResult<bool>> HasPermission([FromQuery] int userId, [FromQuery] string pageName, [FromQuery] string action, CancellationToken cancellationToken = default)
    {
        try
        {
            var hasPermission = await _rolePermissionService.HasPermissionAsync(userId, pageName, action, cancellationToken);
            return Ok(hasPermission);
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error occurred while checking permission for user {UserId} on page {PageName} for action {Action}", userId, pageName, action);
            return StatusCode(500, "An error occurred while processing your request.");
        }
    }

    /// <summary>
    /// Get user permissions summary
    /// الحصول على ملخص صلاحيات المستخدم
    /// </summary>
    [HttpGet("user/{userId}/permissions")]
    [Authorize(Roles = "Administrator,Manager")]
    public async Task<ActionResult<object>> GetUserPermissions(int userId, CancellationToken cancellationToken = default)
    {
        try
        {
            var permissions = await _rolePermissionService.GetUserPermissionsAsync(userId, cancellationToken);
            return Ok(permissions);
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error occurred while getting permissions for user {UserId}", userId);
            return StatusCode(500, "An error occurred while processing your request.");
        }
    }

    /// <summary>
    /// Get all available pages in the system
    /// الحصول على جميع الصفحات المتاحة في النظام
    /// </summary>
    [HttpGet("available-pages")]
    [Authorize(Roles = "Administrator")]
    public async Task<ActionResult<AvailablePagesDto>> GetAvailablePages(CancellationToken cancellationToken = default)
    {
        try
        {
            var pages = await _rolePermissionService.GetAvailablePagesAsync(cancellationToken);
            return Ok(pages);
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error occurred while getting available pages");
            return StatusCode(500, "An error occurred while processing your request.");
        }
    }

    /// <summary>
    /// Get permission templates
    /// الحصول على قوالب الصلاحيات
    /// </summary>
    [HttpGet("templates")]
    [Authorize(Roles = "Administrator")]
    public async Task<ActionResult<IEnumerable<PermissionTemplateDto>>> GetPermissionTemplates(CancellationToken cancellationToken = default)
    {
        try
        {
            var templates = await _rolePermissionService.GetPermissionTemplatesAsync(cancellationToken);
            return Ok(templates);
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error occurred while getting permission templates");
            return StatusCode(500, "An error occurred while processing your request.");
        }
    }

    /// <summary>
    /// Apply permission template to role
    /// تطبيق قالب الصلاحيات على الدور
    /// </summary>
    [HttpPost("apply-template")]
    [Authorize(Roles = "Administrator")]
    public async Task<ActionResult> ApplyPermissionTemplate([FromBody] ApplyTemplateRequestDto request, CancellationToken cancellationToken = default)
    {
        try
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ModelState);
            }

            var result = await _rolePermissionService.ApplyPermissionTemplateAsync(request.RoleId, request.TemplateName, cancellationToken);
            if (!result)
            {
                return BadRequest("Failed to apply permission template.");
            }

            Logger.LogInformation("Permission template {TemplateName} applied to role {RoleId} by user: {UserId}", 
                request.TemplateName, request.RoleId, GetCurrentUserId());
            return Ok(new { Message = "Permission template applied successfully." });
        }
        catch (ArgumentException ex)
        {
            Logger.LogWarning(ex, "Invalid argument while applying permission template");
            return BadRequest(ex.Message);
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error occurred while applying permission template");
            return StatusCode(500, "An error occurred while processing your request.");
        }
    }

    /// <summary>
    /// Validate permission consistency
    /// التحقق من اتساق الصلاحيات
    /// </summary>
    [HttpGet("validate-consistency/role/{roleId}")]
    [Authorize(Roles = "Administrator")]
    public async Task<ActionResult<object>> ValidatePermissionConsistency(int roleId, CancellationToken cancellationToken = default)
    {
        try
        {
            var validation = await _rolePermissionService.ValidatePermissionConsistencyAsync(roleId, cancellationToken);
            return Ok(validation);
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error occurred while validating permission consistency for role {RoleId}", roleId);
            return StatusCode(500, "An error occurred while processing your request.");
        }
    }

    /// <summary>
    /// Get permission usage statistics
    /// الحصول على إحصائيات استخدام الصلاحيات
    /// </summary>
    [HttpGet("usage-statistics")]
    [Authorize(Roles = "Administrator")]
    public async Task<ActionResult<object>> GetPermissionUsageStatistics(CancellationToken cancellationToken = default)
    {
        try
        {
            var statistics = await _rolePermissionService.GetPermissionUsageStatisticsAsync(cancellationToken);
            return Ok(statistics);
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error occurred while getting permission usage statistics");
            return StatusCode(500, "An error occurred while processing your request.");
        }
    }

    /// <summary>
    /// Export permissions to CSV format
    /// تصدير الصلاحيات إلى تنسيق CSV
    /// </summary>
    [HttpGet("export")]
    [Authorize(Roles = "Administrator")]
    public async Task<ActionResult> ExportPermissions([FromQuery] int? roleId = null, [FromQuery] string format = "csv", CancellationToken cancellationToken = default)
    {
        try
        {
            var data = await _rolePermissionService.ExportPermissionsAsync(roleId, format, cancellationToken);
            var fileName = roleId.HasValue 
                ? $"role_{roleId}_permissions_{DateTime.UtcNow:yyyyMMddHHmmss}.{format}"
                : $"all_permissions_{DateTime.UtcNow:yyyyMMddHHmmss}.{format}";
            var contentType = format.ToLower() == "csv" ? "text/csv" : "application/octet-stream";
            
            return File(data, contentType, fileName);
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error occurred while exporting permissions");
            return StatusCode(500, "An error occurred while processing your request.");
        }
    }

    /// <summary>
    /// Set default permissions for new role
    /// تعيين الصلاحيات الافتراضية للدور الجديد
    /// </summary>
    [HttpPost("set-default")]
    [Authorize(Roles = "Administrator")]
    public async Task<ActionResult> SetDefaultPermissions([FromBody] SetDefaultPermissionsRequestDto request, CancellationToken cancellationToken = default)
    {
        try
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ModelState);
            }

            var result = await _rolePermissionService.SetDefaultPermissionsAsync(request.RoleId, request.RoleType, cancellationToken);
            if (!result)
            {
                return BadRequest("Failed to set default permissions.");
            }

            Logger.LogInformation("Default permissions set for role {RoleId} with type {RoleType} by user: {UserId}", 
                request.RoleId, request.RoleType, GetCurrentUserId());
            return Ok(new { Message = "Default permissions set successfully." });
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error occurred while setting default permissions");
            return StatusCode(500, "An error occurred while processing your request.");
        }
    }

    /// <summary>
    /// Get roles with specific permission
    /// الحصول على الأدوار التي لديها صلاحية محددة
    /// </summary>
    [HttpGet("roles-with-permission")]
    [Authorize(Roles = "Administrator")]
    public async Task<ActionResult<IEnumerable<object>>> GetRolesWithPermission([FromQuery] string pageName, [FromQuery] string action, CancellationToken cancellationToken = default)
    {
        try
        {
            if (string.IsNullOrWhiteSpace(pageName) || string.IsNullOrWhiteSpace(action))
            {
                return BadRequest("Page name and action are required.");
            }

            var roles = await _rolePermissionService.GetRolesWithPermissionAsync(pageName, action, cancellationToken);
            return Ok(roles);
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error occurred while getting roles with permission for page {PageName} and action {Action}", pageName, action);
            return StatusCode(500, "An error occurred while processing your request.");
        }
    }
}

// Request DTOs for specific operations
public class CopyPermissionsRequestDto
{
    public int SourceRoleId { get; set; }
    public int TargetRoleId { get; set; }
    public bool ReplaceExisting { get; set; } = true;
}

public class ApplyTemplateRequestDto
{
    public int RoleId { get; set; }
    public string TemplateName { get; set; } = string.Empty;
}

public class SetDefaultPermissionsRequestDto
{
    public int RoleId { get; set; }
    public string RoleType { get; set; } = "Employee";
}
