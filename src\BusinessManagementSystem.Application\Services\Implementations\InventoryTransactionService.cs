using AutoMapper;
using Microsoft.Extensions.Logging;
using BusinessManagementSystem.Application.DTOs.InventoryTransaction;
using BusinessManagementSystem.Application.Interfaces;
using BusinessManagementSystem.Application.Services.Interfaces;
using BusinessManagementSystem.Domain.Entities;

namespace BusinessManagementSystem.Application.Services.Implementations;

/// <summary>
/// Inventory Transaction service implementation for business logic operations
/// تنفيذ خدمة معاملة المخزون لعمليات منطق الأعمال
/// </summary>
public class InventoryTransactionService : IInventoryTransactionService
{
    private readonly IUnitOfWork _unitOfWork;
    private readonly IMapper _mapper;
    private readonly ILogger<InventoryTransactionService> _logger;

    public InventoryTransactionService(IUnitOfWork unitOfWork, IMapper mapper, ILogger<InventoryTransactionService> logger)
    {
        _unitOfWork = unitOfWork;
        _mapper = mapper;
        _logger = logger;
    }

    public async Task<IEnumerable<InventoryTransactionDto>> GetAllAsync(CancellationToken cancellationToken = default)
    {
        var transactions = await _unitOfWork.InventoryTransactions.GetAsync(
            includeProperties: "Item,Warehouse,Company,Invoice",
            cancellationToken: cancellationToken);
        return _mapper.Map<IEnumerable<InventoryTransactionDto>>(transactions);
    }

    public async Task<InventoryTransactionDto?> GetByIdAsync(int id, CancellationToken cancellationToken = default)
    {
        var transaction = await _unitOfWork.InventoryTransactions.GetByIdAsync(id, cancellationToken);
        return transaction != null ? _mapper.Map<InventoryTransactionDto>(transaction) : null;
    }

    public async Task<IEnumerable<InventoryTransactionDto>> GetByCompanyAsync(int companyId, CancellationToken cancellationToken = default)
    {
        var transactions = await _unitOfWork.InventoryTransactions.GetAsync(
            filter: t => t.CompanyId == companyId,
            includeProperties: "Item,Warehouse,Company,Invoice",
            orderBy: q => q.OrderByDescending(t => t.TransactionDate),
            cancellationToken: cancellationToken);
        return _mapper.Map<IEnumerable<InventoryTransactionDto>>(transactions);
    }

    public async Task<IEnumerable<InventoryTransactionDto>> GetByWarehouseAsync(int warehouseId, CancellationToken cancellationToken = default)
    {
        var transactions = await _unitOfWork.InventoryTransactions.GetAsync(
            filter: t => t.WarehouseId == warehouseId,
            includeProperties: "Item,Warehouse,Company,Invoice",
            orderBy: q => q.OrderByDescending(t => t.TransactionDate),
            cancellationToken: cancellationToken);
        return _mapper.Map<IEnumerable<InventoryTransactionDto>>(transactions);
    }

    public async Task<IEnumerable<InventoryTransactionDto>> GetByItemAsync(int itemId, CancellationToken cancellationToken = default)
    {
        var transactions = await _unitOfWork.InventoryTransactions.GetAsync(
            filter: t => t.ItemId == itemId,
            includeProperties: "Item,Warehouse,Company,Invoice",
            orderBy: q => q.OrderByDescending(t => t.TransactionDate),
            cancellationToken: cancellationToken);
        return _mapper.Map<IEnumerable<InventoryTransactionDto>>(transactions);
    }

    public async Task<IEnumerable<InventoryTransactionDto>> GetByTypeAsync(int companyId, string transactionType, CancellationToken cancellationToken = default)
    {
        var transactions = await _unitOfWork.InventoryTransactions.GetAsync(
            filter: t => t.CompanyId == companyId && t.TransactionType == transactionType,
            includeProperties: "Item,Warehouse,Company,Invoice",
            orderBy: q => q.OrderByDescending(t => t.TransactionDate),
            cancellationToken: cancellationToken);
        return _mapper.Map<IEnumerable<InventoryTransactionDto>>(transactions);
    }

    public async Task<IEnumerable<InventoryTransactionDto>> GetByDateRangeAsync(int companyId, DateTime startDate, DateTime endDate, CancellationToken cancellationToken = default)
    {
        var transactions = await _unitOfWork.InventoryTransactions.GetAsync(
            filter: t => t.CompanyId == companyId && t.TransactionDate >= startDate && t.TransactionDate <= endDate,
            includeProperties: "Item,Warehouse,Company,Invoice",
            orderBy: q => q.OrderByDescending(t => t.TransactionDate),
            cancellationToken: cancellationToken);
        return _mapper.Map<IEnumerable<InventoryTransactionDto>>(transactions);
    }

    public async Task<IEnumerable<InventoryTransactionDto>> GetByInvoiceAsync(int invoiceId, CancellationToken cancellationToken = default)
    {
        var transactions = await _unitOfWork.InventoryTransactions.GetAsync(
            filter: t => t.InvoiceId == invoiceId,
            includeProperties: "Item,Warehouse,Company,Invoice",
            orderBy: q => q.OrderByDescending(t => t.TransactionDate),
            cancellationToken: cancellationToken);
        return _mapper.Map<IEnumerable<InventoryTransactionDto>>(transactions);
    }

    public async Task<IEnumerable<InventoryTransactionDto>> SearchAsync(int companyId, string searchTerm, CancellationToken cancellationToken = default)
    {
        var transactions = await _unitOfWork.InventoryTransactions.GetAsync(
            filter: t => t.CompanyId == companyId && 
                        (t.ReferenceNumber != null && t.ReferenceNumber.Contains(searchTerm) ||
                         t.Notes != null && t.Notes.Contains(searchTerm) ||
                         t.Item.Name.Contains(searchTerm) ||
                         t.Warehouse.Name.Contains(searchTerm)),
            includeProperties: "Item,Warehouse,Company,Invoice",
            orderBy: q => q.OrderByDescending(t => t.TransactionDate),
            cancellationToken: cancellationToken);
        return _mapper.Map<IEnumerable<InventoryTransactionDto>>(transactions);
    }

    public async Task<InventoryTransactionDto> CreateAsync(CreateInventoryTransactionDto createInventoryTransactionDto, CancellationToken cancellationToken = default)
    {
        // Validate transaction
        await ValidateTransactionAsync(createInventoryTransactionDto, cancellationToken);

        await _unitOfWork.BeginTransactionAsync(cancellationToken);
        try
        {
            var transaction = _mapper.Map<InventoryTransaction>(createInventoryTransactionDto);
            transaction.TotalCost = transaction.Quantity * transaction.UnitCost;
            
            // Generate reference number if not provided
            if (string.IsNullOrEmpty(transaction.ReferenceNumber))
            {
                transaction.ReferenceNumber = await GenerateReferenceNumberAsync(
                    transaction.TransactionType, transaction.CompanyId, cancellationToken);
            }

            await _unitOfWork.InventoryTransactions.AddAsync(transaction, cancellationToken);

            // Update item stock quantity and warehouse utilization
            await UpdateStockAndWarehouseAsync(transaction, cancellationToken);

            await _unitOfWork.SaveChangesAsync(cancellationToken);
            await _unitOfWork.CommitTransactionAsync(cancellationToken);

            _logger.LogInformation("Inventory transaction created successfully with ID: {TransactionId}", transaction.Id);
            return _mapper.Map<InventoryTransactionDto>(transaction);
        }
        catch
        {
            await _unitOfWork.RollbackTransactionAsync(cancellationToken);
            throw;
        }
    }

    public async Task<InventoryTransactionDto> UpdateAsync(int id, UpdateInventoryTransactionDto updateInventoryTransactionDto, CancellationToken cancellationToken = default)
    {
        var existingTransaction = await _unitOfWork.InventoryTransactions.GetByIdAsync(id, cancellationToken);
        if (existingTransaction == null)
        {
            throw new ArgumentException($"Inventory transaction with ID {id} not found.", nameof(id));
        }

        await _unitOfWork.BeginTransactionAsync(cancellationToken);
        try
        {
            // Reverse the effect of the original transaction
            await ReverseStockAndWarehouseChangesAsync(existingTransaction, cancellationToken);

            // Update the transaction
            var originalQuantity = existingTransaction.Quantity;
            var originalUnitCost = existingTransaction.UnitCost;
            
            _mapper.Map(updateInventoryTransactionDto, existingTransaction);
            existingTransaction.TotalCost = existingTransaction.Quantity * existingTransaction.UnitCost;

            _unitOfWork.InventoryTransactions.Update(existingTransaction);

            // Apply the new transaction effects
            await UpdateStockAndWarehouseAsync(existingTransaction, cancellationToken);

            await _unitOfWork.SaveChangesAsync(cancellationToken);
            await _unitOfWork.CommitTransactionAsync(cancellationToken);

            _logger.LogInformation("Inventory transaction updated successfully with ID: {TransactionId}", id);
            return _mapper.Map<InventoryTransactionDto>(existingTransaction);
        }
        catch
        {
            await _unitOfWork.RollbackTransactionAsync(cancellationToken);
            throw;
        }
    }

    public async Task<bool> DeleteAsync(int id, CancellationToken cancellationToken = default)
    {
        var transaction = await _unitOfWork.InventoryTransactions.GetByIdAsync(id, cancellationToken);
        if (transaction == null)
        {
            return false;
        }

        await _unitOfWork.BeginTransactionAsync(cancellationToken);
        try
        {
            // Reverse the effect of the transaction
            await ReverseStockAndWarehouseChangesAsync(transaction, cancellationToken);

            _unitOfWork.InventoryTransactions.Delete(transaction);
            await _unitOfWork.SaveChangesAsync(cancellationToken);
            await _unitOfWork.CommitTransactionAsync(cancellationToken);

            _logger.LogInformation("Inventory transaction deleted successfully with ID: {TransactionId}", id);
            return true;
        }
        catch
        {
            await _unitOfWork.RollbackTransactionAsync(cancellationToken);
            throw;
        }
    }

    public async Task<InventoryTransactionDto> ProcessStockInAsync(int itemId, int warehouseId, decimal quantity, decimal unitCost, string? notes = null, int? invoiceId = null, CancellationToken cancellationToken = default)
    {
        var item = await _unitOfWork.Items.GetByIdAsync(itemId, cancellationToken);
        var warehouse = await _unitOfWork.Warehouses.GetByIdAsync(warehouseId, cancellationToken);

        if (item == null || warehouse == null)
        {
            throw new ArgumentException("Invalid item or warehouse ID.");
        }

        var createDto = new CreateInventoryTransactionDto
        {
            TransactionDate = DateTime.UtcNow,
            TransactionType = "In",
            Quantity = quantity,
            UnitCost = unitCost,
            Notes = notes,
            ItemId = itemId,
            WarehouseId = warehouseId,
            CompanyId = warehouse.CompanyId,
            InvoiceId = invoiceId
        };

        return await CreateAsync(createDto, cancellationToken);
    }

    public async Task<InventoryTransactionDto> ProcessStockOutAsync(int itemId, int warehouseId, decimal quantity, string? notes = null, int? invoiceId = null, CancellationToken cancellationToken = default)
    {
        var item = await _unitOfWork.Items.GetByIdAsync(itemId, cancellationToken);
        var warehouse = await _unitOfWork.Warehouses.GetByIdAsync(warehouseId, cancellationToken);

        if (item == null || warehouse == null)
        {
            throw new ArgumentException("Invalid item or warehouse ID.");
        }

        // Check if sufficient stock is available
        var currentStock = await CalculateCurrentStockAsync(itemId, warehouseId, cancellationToken);
        if (currentStock < quantity)
        {
            throw new InvalidOperationException($"Insufficient stock. Available: {currentStock}, Requested: {quantity}");
        }

        var createDto = new CreateInventoryTransactionDto
        {
            TransactionDate = DateTime.UtcNow,
            TransactionType = "Out",
            Quantity = quantity,
            UnitCost = item.CostPrice,
            Notes = notes,
            ItemId = itemId,
            WarehouseId = warehouseId,
            CompanyId = warehouse.CompanyId,
            InvoiceId = invoiceId
        };

        return await CreateAsync(createDto, cancellationToken);
    }

    public async Task<InventoryTransactionDto> ProcessStockAdjustmentAsync(int itemId, int warehouseId, decimal adjustmentQuantity, string reason, CancellationToken cancellationToken = default)
    {
        var item = await _unitOfWork.Items.GetByIdAsync(itemId, cancellationToken);
        var warehouse = await _unitOfWork.Warehouses.GetByIdAsync(warehouseId, cancellationToken);

        if (item == null || warehouse == null)
        {
            throw new ArgumentException("Invalid item or warehouse ID.");
        }

        var createDto = new CreateInventoryTransactionDto
        {
            TransactionDate = DateTime.UtcNow,
            TransactionType = "Adjustment",
            Quantity = Math.Abs(adjustmentQuantity),
            UnitCost = item.CostPrice,
            Notes = $"Stock adjustment: {reason}",
            ItemId = itemId,
            WarehouseId = warehouseId,
            CompanyId = warehouse.CompanyId
        };

        return await CreateAsync(createDto, cancellationToken);
    }

    public async Task<(InventoryTransactionDto OutTransaction, InventoryTransactionDto InTransaction)> ProcessStockTransferAsync(int itemId, int fromWarehouseId, int toWarehouseId, decimal quantity, string? notes = null, CancellationToken cancellationToken = default)
    {
        if (fromWarehouseId == toWarehouseId)
        {
            throw new ArgumentException("Source and destination warehouses cannot be the same.");
        }

        var item = await _unitOfWork.Items.GetByIdAsync(itemId, cancellationToken);
        var fromWarehouse = await _unitOfWork.Warehouses.GetByIdAsync(fromWarehouseId, cancellationToken);
        var toWarehouse = await _unitOfWork.Warehouses.GetByIdAsync(toWarehouseId, cancellationToken);

        if (item == null || fromWarehouse == null || toWarehouse == null)
        {
            throw new ArgumentException("Invalid item or warehouse ID.");
        }

        // Check if sufficient stock is available in source warehouse
        var currentStock = await CalculateCurrentStockAsync(itemId, fromWarehouseId, cancellationToken);
        if (currentStock < quantity)
        {
            throw new InvalidOperationException($"Insufficient stock in source warehouse. Available: {currentStock}, Requested: {quantity}");
        }

        await _unitOfWork.BeginTransactionAsync(cancellationToken);
        try
        {
            var transferRef = $"TRF-{DateTime.UtcNow:yyyyMMddHHmmss}";

            // Create outbound transaction
            var outTransaction = await ProcessStockOutAsync(itemId, fromWarehouseId, quantity, 
                $"Transfer out to {toWarehouse.Name}. Ref: {transferRef}. {notes}", null, cancellationToken);

            // Create inbound transaction
            var inTransaction = await ProcessStockInAsync(itemId, toWarehouseId, quantity, item.CostPrice,
                $"Transfer in from {fromWarehouse.Name}. Ref: {transferRef}. {notes}", null, cancellationToken);

            await _unitOfWork.CommitTransactionAsync(cancellationToken);

            _logger.LogInformation("Stock transfer completed successfully from warehouse {FromId} to {ToId} for item {ItemId}", 
                fromWarehouseId, toWarehouseId, itemId);

            return (outTransaction, inTransaction);
        }
        catch
        {
            await _unitOfWork.RollbackTransactionAsync(cancellationToken);
            throw;
        }
    }

    public async Task<decimal> CalculateCurrentStockAsync(int itemId, int warehouseId, CancellationToken cancellationToken = default)
    {
        var transactions = await _unitOfWork.InventoryTransactions.GetAsync(
            filter: t => t.ItemId == itemId && t.WarehouseId == warehouseId,
            cancellationToken: cancellationToken);

        var stockIn = transactions.Where(t => t.TransactionType == "In" || t.TransactionType == "Transfer-In" || t.TransactionType == "Adjustment")
            .Sum(t => t.Quantity);

        var stockOut = transactions.Where(t => t.TransactionType == "Out" || t.TransactionType == "Transfer-Out")
            .Sum(t => t.Quantity);

        return stockIn - stockOut;
    }

    public async Task<string> GenerateReferenceNumberAsync(string transactionType, int companyId, CancellationToken cancellationToken = default)
    {
        var transactions = await _unitOfWork.InventoryTransactions.GetAsync(
            filter: t => t.CompanyId == companyId && t.TransactionType == transactionType,
            cancellationToken: cancellationToken);

        var count = transactions.Count() + 1;
        var typePrefix = transactionType.ToUpper().Substring(0, Math.Min(3, transactionType.Length));
        
        return $"{typePrefix}-{companyId:D4}-{DateTime.UtcNow:yyyyMMdd}-{count:D6}";
    }

    public async Task<bool> ValidateTransactionAsync(CreateInventoryTransactionDto transactionDto, CancellationToken cancellationToken = default)
    {
        // Validate item exists and is active
        var item = await _unitOfWork.Items.GetByIdAsync(transactionDto.ItemId, cancellationToken);
        if (item == null || !item.IsActive)
        {
            throw new ArgumentException("Item not found or inactive.");
        }

        // Validate warehouse exists and is active
        var warehouse = await _unitOfWork.Warehouses.GetByIdAsync(transactionDto.WarehouseId, cancellationToken);
        if (warehouse == null || !warehouse.IsActive)
        {
            throw new ArgumentException("Warehouse not found or inactive.");
        }

        // Validate company matches
        if (warehouse.CompanyId != transactionDto.CompanyId)
        {
            throw new ArgumentException("Warehouse does not belong to the specified company.");
        }

        // For outbound transactions, check stock availability
        if (transactionDto.TransactionType == "Out" || transactionDto.TransactionType == "Transfer-Out")
        {
            var currentStock = await CalculateCurrentStockAsync(transactionDto.ItemId, transactionDto.WarehouseId, cancellationToken);
            if (currentStock < transactionDto.Quantity)
            {
                throw new InvalidOperationException($"Insufficient stock. Available: {currentStock}, Requested: {transactionDto.Quantity}");
            }
        }

        return true;
    }

    private async Task UpdateStockAndWarehouseAsync(InventoryTransaction transaction, CancellationToken cancellationToken)
    {
        var item = await _unitOfWork.Items.GetByIdAsync(transaction.ItemId, cancellationToken);
        var warehouse = await _unitOfWork.Warehouses.GetByIdAsync(transaction.WarehouseId, cancellationToken);

        if (item == null || warehouse == null) return;

        // Update item stock quantity
        var stockChange = GetStockChange(transaction.TransactionType, transaction.Quantity);
        item.StockQuantity += stockChange;

        // Update warehouse utilization (simplified - assuming 1:1 ratio)
        warehouse.CurrentUtilization += stockChange;

        _unitOfWork.Items.Update(item);
        _unitOfWork.Warehouses.Update(warehouse);
    }

    private async Task ReverseStockAndWarehouseChangesAsync(InventoryTransaction transaction, CancellationToken cancellationToken)
    {
        var item = await _unitOfWork.Items.GetByIdAsync(transaction.ItemId, cancellationToken);
        var warehouse = await _unitOfWork.Warehouses.GetByIdAsync(transaction.WarehouseId, cancellationToken);

        if (item == null || warehouse == null) return;

        // Reverse the stock change
        var stockChange = GetStockChange(transaction.TransactionType, transaction.Quantity);
        item.StockQuantity -= stockChange;

        // Reverse warehouse utilization change
        warehouse.CurrentUtilization -= stockChange;

        _unitOfWork.Items.Update(item);
        _unitOfWork.Warehouses.Update(warehouse);
    }

    private static decimal GetStockChange(string transactionType, decimal quantity)
    {
        return transactionType switch
        {
            "In" or "Transfer-In" or "Adjustment" => quantity,
            "Out" or "Transfer-Out" => -quantity,
            _ => 0
        };
    }

    public async Task<InventoryMovementReportDto> GetMovementReportAsync(int companyId, DateTime startDate, DateTime endDate, CancellationToken cancellationToken = default)
    {
        var transactions = await GetByDateRangeAsync(companyId, startDate, endDate, cancellationToken);

        var totalTransactions = transactions.Count();
        var totalQuantityIn = transactions.Where(t => t.TransactionType == "In" || t.TransactionType == "Transfer-In").Sum(t => t.Quantity);
        var totalQuantityOut = transactions.Where(t => t.TransactionType == "Out" || t.TransactionType == "Transfer-Out").Sum(t => t.Quantity);
        var totalValueIn = transactions.Where(t => t.TransactionType == "In" || t.TransactionType == "Transfer-In").Sum(t => t.TotalCost);
        var totalValueOut = transactions.Where(t => t.TransactionType == "Out" || t.TransactionType == "Transfer-Out").Sum(t => t.TotalCost);

        var transactionsByType = transactions.GroupBy(t => t.TransactionType)
            .Select(g => new TransactionTypeSummaryDto
            {
                TransactionType = g.Key,
                Count = g.Count(),
                TotalQuantity = g.Sum(t => t.Quantity),
                TotalValue = g.Sum(t => t.TotalCost)
            }).ToList();

        var topItemsByMovement = transactions.GroupBy(t => new { t.ItemId, t.ItemName })
            .Select(g => new ItemMovementSummaryDto
            {
                ItemId = g.Key.ItemId,
                ItemName = g.Key.ItemName,
                TotalTransactions = g.Count(),
                TotalQuantityMoved = g.Sum(t => t.Quantity),
                TotalValueMoved = g.Sum(t => t.TotalCost)
            })
            .OrderByDescending(i => i.TotalValueMoved)
            .Take(10)
            .ToList();

        return new InventoryMovementReportDto
        {
            StartDate = startDate,
            EndDate = endDate,
            TotalTransactions = totalTransactions,
            TotalQuantityIn = totalQuantityIn,
            TotalQuantityOut = totalQuantityOut,
            NetQuantityMovement = totalQuantityIn - totalQuantityOut,
            TotalValueIn = totalValueIn,
            TotalValueOut = totalValueOut,
            NetValueMovement = totalValueIn - totalValueOut,
            TransactionsByType = transactionsByType,
            TopItemsByMovement = topItemsByMovement
        };
    }

    public async Task<IEnumerable<InventoryTransactionDto>> GetItemTransactionHistoryAsync(int itemId, int? warehouseId = null, CancellationToken cancellationToken = default)
    {
        var filter = warehouseId.HasValue
            ? (System.Linq.Expressions.Expression<Func<InventoryTransaction, bool>>)(t => t.ItemId == itemId && t.WarehouseId == warehouseId.Value)
            : t => t.ItemId == itemId;

        var transactions = await _unitOfWork.InventoryTransactions.GetAsync(
            filter: filter,
            includeProperties: "Item,Warehouse,Company,Invoice",
            orderBy: q => q.OrderByDescending(t => t.TransactionDate),
            cancellationToken: cancellationToken);

        return _mapper.Map<IEnumerable<InventoryTransactionDto>>(transactions);
    }

    public async Task<object> GetWarehouseTransactionSummaryAsync(int warehouseId, DateTime? startDate = null, DateTime? endDate = null, CancellationToken cancellationToken = default)
    {
        var start = startDate ?? DateTime.UtcNow.AddMonths(-1);
        var end = endDate ?? DateTime.UtcNow;

        var transactions = await _unitOfWork.InventoryTransactions.GetAsync(
            filter: t => t.WarehouseId == warehouseId && t.TransactionDate >= start && t.TransactionDate <= end,
            includeProperties: "Item",
            cancellationToken: cancellationToken);

        var totalTransactions = transactions.Count();
        var totalValue = transactions.Sum(t => t.TotalCost);
        var inTransactions = transactions.Where(t => t.TransactionType == "In" || t.TransactionType == "Transfer-In");
        var outTransactions = transactions.Where(t => t.TransactionType == "Out" || t.TransactionType == "Transfer-Out");

        return new
        {
            WarehouseId = warehouseId,
            Period = new { StartDate = start, EndDate = end },
            TotalTransactions = totalTransactions,
            TotalValue = totalValue,
            InboundTransactions = new
            {
                Count = inTransactions.Count(),
                TotalQuantity = inTransactions.Sum(t => t.Quantity),
                TotalValue = inTransactions.Sum(t => t.TotalCost)
            },
            OutboundTransactions = new
            {
                Count = outTransactions.Count(),
                TotalQuantity = outTransactions.Sum(t => t.Quantity),
                TotalValue = outTransactions.Sum(t => t.TotalCost)
            },
            TransactionsByType = transactions.GroupBy(t => t.TransactionType)
                .Select(g => new { Type = g.Key, Count = g.Count(), TotalValue = g.Sum(t => t.TotalCost) })
                .ToList(),
            TopItems = transactions.GroupBy(t => new { t.ItemId, t.Item.Name })
                .Select(g => new {
                    ItemId = g.Key.ItemId,
                    ItemName = g.Key.Name,
                    TransactionCount = g.Count(),
                    TotalQuantity = g.Sum(t => t.Quantity),
                    TotalValue = g.Sum(t => t.TotalCost)
                })
                .OrderByDescending(i => i.TotalValue)
                .Take(5)
                .ToList()
        };
    }

    public async Task<object> GetStockValuationReportAsync(int companyId, DateTime? asOfDate = null, CancellationToken cancellationToken = default)
    {
        var cutoffDate = asOfDate ?? DateTime.UtcNow;

        var transactions = await _unitOfWork.InventoryTransactions.GetAsync(
            filter: t => t.CompanyId == companyId && t.TransactionDate <= cutoffDate,
            includeProperties: "Item,Warehouse",
            cancellationToken: cancellationToken);

        var stockByItemWarehouse = transactions
            .GroupBy(t => new { t.ItemId, t.WarehouseId, ItemName = t.Item.Name, WarehouseName = t.Warehouse.Name })
            .Select(g => new
            {
                ItemId = g.Key.ItemId,
                ItemName = g.Key.ItemName,
                WarehouseId = g.Key.WarehouseId,
                WarehouseName = g.Key.WarehouseName,
                StockIn = g.Where(t => t.TransactionType == "In" || t.TransactionType == "Transfer-In").Sum(t => t.Quantity),
                StockOut = g.Where(t => t.TransactionType == "Out" || t.TransactionType == "Transfer-Out").Sum(t => t.Quantity),
                AverageCost = g.Average(t => t.UnitCost)
            })
            .Select(s => new
            {
                s.ItemId,
                s.ItemName,
                s.WarehouseId,
                s.WarehouseName,
                CurrentStock = s.StockIn - s.StockOut,
                s.AverageCost,
                StockValue = (s.StockIn - s.StockOut) * s.AverageCost
            })
            .Where(s => s.CurrentStock > 0)
            .ToList();

        var totalStockValue = stockByItemWarehouse.Sum(s => s.StockValue);
        var totalItems = stockByItemWarehouse.Count;

        return new
        {
            AsOfDate = cutoffDate,
            TotalStockValue = totalStockValue,
            TotalItems = totalItems,
            StockByWarehouse = stockByItemWarehouse.GroupBy(s => new { s.WarehouseId, s.WarehouseName })
                .Select(g => new
                {
                    WarehouseId = g.Key.WarehouseId,
                    WarehouseName = g.Key.WarehouseName,
                    ItemCount = g.Count(),
                    TotalValue = g.Sum(s => s.StockValue)
                })
                .ToList(),
            StockByItem = stockByItemWarehouse.GroupBy(s => new { s.ItemId, s.ItemName })
                .Select(g => new
                {
                    ItemId = g.Key.ItemId,
                    ItemName = g.Key.ItemName,
                    TotalStock = g.Sum(s => s.CurrentStock),
                    TotalValue = g.Sum(s => s.StockValue),
                    WarehouseCount = g.Count()
                })
                .OrderByDescending(i => i.TotalValue)
                .ToList()
        };
    }

    public async Task<object> GetTransactionStatisticsAsync(int companyId, DateTime? startDate = null, DateTime? endDate = null, CancellationToken cancellationToken = default)
    {
        var start = startDate ?? DateTime.UtcNow.AddMonths(-1);
        var end = endDate ?? DateTime.UtcNow;

        var transactions = await GetByDateRangeAsync(companyId, start, end, cancellationToken);

        var dailyStats = transactions.GroupBy(t => t.TransactionDate.Date)
            .Select(g => new
            {
                Date = g.Key,
                TransactionCount = g.Count(),
                TotalValue = g.Sum(t => t.TotalCost),
                InboundCount = g.Count(t => t.TransactionType == "In" || t.TransactionType == "Transfer-In"),
                OutboundCount = g.Count(t => t.TransactionType == "Out" || t.TransactionType == "Transfer-Out")
            })
            .OrderBy(s => s.Date)
            .ToList();

        return new
        {
            Period = new { StartDate = start, EndDate = end },
            TotalTransactions = transactions.Count(),
            TotalValue = transactions.Sum(t => t.TotalCost),
            AverageTransactionValue = transactions.Any() ? transactions.Average(t => t.TotalCost) : 0,
            TransactionsByType = transactions.GroupBy(t => t.TransactionType)
                .Select(g => new { Type = g.Key, Count = g.Count(), Percentage = (decimal)g.Count() / transactions.Count() * 100 })
                .ToList(),
            DailyStatistics = dailyStats,
            BusiestDay = dailyStats.OrderByDescending(s => s.TransactionCount).FirstOrDefault(),
            HighestValueDay = dailyStats.OrderByDescending(s => s.TotalValue).FirstOrDefault()
        };
    }

    public async Task<InventoryTransactionDto> ReverseTransactionAsync(int transactionId, string reason, CancellationToken cancellationToken = default)
    {
        var originalTransaction = await _unitOfWork.InventoryTransactions.GetByIdAsync(transactionId, cancellationToken);
        if (originalTransaction == null)
        {
            throw new ArgumentException($"Transaction with ID {transactionId} not found.");
        }

        var reverseType = originalTransaction.TransactionType switch
        {
            "In" => "Out",
            "Out" => "In",
            "Transfer-In" => "Transfer-Out",
            "Transfer-Out" => "Transfer-In",
            _ => "Adjustment"
        };

        var reverseDto = new CreateInventoryTransactionDto
        {
            TransactionDate = DateTime.UtcNow,
            TransactionType = reverseType,
            Quantity = originalTransaction.Quantity,
            UnitCost = originalTransaction.UnitCost,
            ReferenceNumber = $"REV-{originalTransaction.ReferenceNumber}",
            Notes = $"Reversal of transaction {originalTransaction.Id}: {reason}",
            ItemId = originalTransaction.ItemId,
            WarehouseId = originalTransaction.WarehouseId,
            CompanyId = originalTransaction.CompanyId,
            InvoiceId = originalTransaction.InvoiceId
        };

        return await CreateAsync(reverseDto, cancellationToken);
    }
}
