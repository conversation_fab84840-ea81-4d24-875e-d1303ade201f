using Microsoft.EntityFrameworkCore.Storage;
using BusinessManagementSystem.Application.Interfaces;
using BusinessManagementSystem.Application.Interfaces.Repositories;
using BusinessManagementSystem.Domain.Entities;
using BusinessManagementSystem.Infrastructure.Data;
using BusinessManagementSystem.Infrastructure.Repositories;

namespace BusinessManagementSystem.Infrastructure.UnitOfWork;

/// <summary>
/// Unit of Work implementation for managing transactions across multiple repositories
/// تنفيذ وحدة العمل لإدارة المعاملات عبر مستودعات متعددة
/// </summary>
public class UnitOfWork : IUnitOfWork
{
    private readonly BusinessManagementDbContext _context;
    private IDbContextTransaction? _transaction;

    // Repository instances
    private ICompanyRepository? _companies;
    private IGenericRepository<Department>? _departments;
    private IGenericRepository<Partner>? _partners;
    private IGenericRepository<MainCash>? _mainCashes;
    private IGenericRepository<FinancialTransaction>? _financialTransactions;
    private IGenericRepository<Warehouse>? _warehouses;
    private IGenericRepository<Item>? _items;
    private IInventoryTransactionRepository? _inventoryTransactions;
    private IInvoiceRepository? _invoices;
    private IGenericRepository<InvoiceDetail>? _invoiceDetails;
    private IGenericRepository<Person>? _persons;
    private IUserRepository? _users;
    private IGenericRepository<Role>? _roles;
    private IGenericRepository<RolePermission>? _rolePermissions;

    public UnitOfWork(BusinessManagementDbContext context)
    {
        _context = context;
    }

    public ICompanyRepository Companies =>
        _companies ??= new CompanyRepository(_context);

    public IGenericRepository<Department> Departments =>
        _departments ??= new GenericRepository<Department>(_context);

    public IGenericRepository<Partner> Partners =>
        _partners ??= new GenericRepository<Partner>(_context);

    public IGenericRepository<MainCash> MainCashes =>
        _mainCashes ??= new GenericRepository<MainCash>(_context);

    public IGenericRepository<FinancialTransaction> FinancialTransactions =>
        _financialTransactions ??= new GenericRepository<FinancialTransaction>(_context);

    public IGenericRepository<Warehouse> Warehouses =>
        _warehouses ??= new GenericRepository<Warehouse>(_context);

    public IGenericRepository<Item> Items =>
        _items ??= new GenericRepository<Item>(_context);

    public IInventoryTransactionRepository InventoryTransactions =>
        _inventoryTransactions ??= new InventoryTransactionRepository(_context);

    public IInvoiceRepository Invoices =>
        _invoices ??= new InvoiceRepository(_context);

    public IGenericRepository<InvoiceDetail> InvoiceDetails =>
        _invoiceDetails ??= new GenericRepository<InvoiceDetail>(_context);

    public IGenericRepository<Person> Persons =>
        _persons ??= new GenericRepository<Person>(_context);

    public IUserRepository Users =>
        _users ??= new UserRepository(_context);

    public IGenericRepository<Role> Roles =>
        _roles ??= new GenericRepository<Role>(_context);

    public IGenericRepository<RolePermission> RolePermissions =>
        _rolePermissions ??= new GenericRepository<RolePermission>(_context);

    public async Task<int> SaveChangesAsync(CancellationToken cancellationToken = default)
    {
        return await _context.SaveChangesAsync(cancellationToken);
    }

    public async Task BeginTransactionAsync(CancellationToken cancellationToken = default)
    {
        _transaction = await _context.Database.BeginTransactionAsync(cancellationToken);
    }

    public async Task CommitTransactionAsync(CancellationToken cancellationToken = default)
    {
        if (_transaction != null)
        {
            await _transaction.CommitAsync(cancellationToken);
            await _transaction.DisposeAsync();
            _transaction = null;
        }
    }

    public async Task RollbackTransactionAsync(CancellationToken cancellationToken = default)
    {
        if (_transaction != null)
        {
            await _transaction.RollbackAsync(cancellationToken);
            await _transaction.DisposeAsync();
            _transaction = null;
        }
    }

    public void Dispose()
    {
        _transaction?.Dispose();
        _context.Dispose();
    }
}
