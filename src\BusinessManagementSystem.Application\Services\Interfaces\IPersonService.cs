using BusinessManagementSystem.Application.DTOs.Person;

namespace BusinessManagementSystem.Application.Services.Interfaces;

/// <summary>
/// Person service interface for business logic operations
/// واجهة خدمة الشخص لعمليات منطق الأعمال
/// </summary>
public interface IPersonService
{
    /// <summary>
    /// Get all persons
    /// الحصول على جميع الأشخاص
    /// </summary>
    Task<IEnumerable<PersonDto>> GetAllAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// Get person by ID
    /// الحصول على الشخص بواسطة المعرف
    /// </summary>
    Task<PersonDto?> GetByIdAsync(int id, CancellationToken cancellationToken = default);

    /// <summary>
    /// Get persons by company
    /// الحصول على الأشخاص بواسطة الشركة
    /// </summary>
    Task<IEnumerable<PersonDto>> GetByCompanyAsync(int companyId, CancellationToken cancellationToken = default);

    /// <summary>
    /// Get persons by type
    /// الحصول على الأشخاص بواسطة النوع
    /// </summary>
    Task<IEnumerable<PersonDto>> GetByTypeAsync(int companyId, string personType, CancellationToken cancellationToken = default);

    /// <summary>
    /// Search persons
    /// البحث في الأشخاص
    /// </summary>
    Task<IEnumerable<PersonDto>> SearchAsync(int companyId, string searchTerm, CancellationToken cancellationToken = default);

    /// <summary>
    /// Get customers
    /// الحصول على العملاء
    /// </summary>
    Task<IEnumerable<PersonDto>> GetCustomersAsync(int companyId, CancellationToken cancellationToken = default);

    /// <summary>
    /// Get suppliers
    /// الحصول على الموردين
    /// </summary>
    Task<IEnumerable<PersonDto>> GetSuppliersAsync(int companyId, CancellationToken cancellationToken = default);

    /// <summary>
    /// Create new person
    /// إنشاء شخص جديد
    /// </summary>
    Task<PersonDto> CreateAsync(CreatePersonDto createPersonDto, CancellationToken cancellationToken = default);

    /// <summary>
    /// Update existing person
    /// تحديث شخص موجود
    /// </summary>
    Task<PersonDto> UpdateAsync(int id, UpdatePersonDto updatePersonDto, CancellationToken cancellationToken = default);

    /// <summary>
    /// Set person active status
    /// تعيين حالة تفعيل الشخص
    /// </summary>
    Task<bool> SetActiveStatusAsync(int id, bool isActive, CancellationToken cancellationToken = default);

    /// <summary>
    /// Delete person
    /// حذف الشخص
    /// </summary>
    Task<bool> DeleteAsync(int id, CancellationToken cancellationToken = default);

    /// <summary>
    /// Get person balance
    /// الحصول على رصيد الشخص
    /// </summary>
    Task<object> GetPersonBalanceAsync(int id, CancellationToken cancellationToken = default);

    /// <summary>
    /// Get person transactions
    /// الحصول على معاملات الشخص
    /// </summary>
    Task<IEnumerable<object>> GetPersonTransactionsAsync(int id, CancellationToken cancellationToken = default);

    /// <summary>
    /// Get person invoices
    /// الحصول على فواتير الشخص
    /// </summary>
    Task<IEnumerable<object>> GetPersonInvoicesAsync(int id, CancellationToken cancellationToken = default);

    /// <summary>
    /// Get person statistics
    /// الحصول على إحصائيات الشخص
    /// </summary>
    Task<object> GetPersonStatisticsAsync(int id, CancellationToken cancellationToken = default);
}
