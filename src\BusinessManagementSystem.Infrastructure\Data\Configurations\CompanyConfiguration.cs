using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using BusinessManagementSystem.Domain.Entities;

namespace BusinessManagementSystem.Infrastructure.Data.Configurations;

/// <summary>
/// Entity configuration for Company
/// تكوين الكيان للشركة
/// </summary>
public class CompanyConfiguration : IEntityTypeConfiguration<Company>
{
    public void Configure(EntityTypeBuilder<Company> builder)
    {
        builder.ToTable("Companies");

        builder.HasKey(c => c.Id);

        builder.Property(c => c.Name)
            .IsRequired()
            .HasMaxLength(200);

        builder.Property(c => c.Description)
            .HasMaxLength(1000);

        builder.Property(c => c.Address)
            .HasMaxLength(500);

        builder.Property(c => c.LogoPath)
            .HasMaxLength(500);

        builder.Property(c => c.CommercialRegistrationNumber)
            .HasMaxLength(50);

        builder.Property(c => c.TaxNumber)
            .HasMaxLength(50);

        builder.Property(c => c.InsuranceNumber)
            .HasMaxLength(50);

        builder.Property(c => c.Phone)
            .HasMaxLength(20);

        // Indexes
        builder.HasIndex(c => c.Name)
            .IsUnique();

        builder.HasIndex(c => c.CommercialRegistrationNumber)
            .IsUnique()
            .HasFilter("[CommercialRegistrationNumber] IS NOT NULL");

        builder.HasIndex(c => c.TaxNumber)
            .IsUnique()
            .HasFilter("[TaxNumber] IS NOT NULL");

        // Relationships
        builder.HasMany(c => c.Departments)
            .WithOne(d => d.Company)
            .HasForeignKey(d => d.CompanyId)
            .OnDelete(DeleteBehavior.Cascade);

        builder.HasMany(c => c.Partners)
            .WithOne(p => p.Company)
            .HasForeignKey(p => p.CompanyId)
            .OnDelete(DeleteBehavior.Cascade);

        builder.HasOne(c => c.MainCash)
            .WithOne(mc => mc.Company)
            .HasForeignKey<MainCash>(mc => mc.CompanyId)
            .OnDelete(DeleteBehavior.Cascade);

        builder.HasMany(c => c.FinancialTransactions)
            .WithOne(ft => ft.Company)
            .HasForeignKey(ft => ft.CompanyId)
            .OnDelete(DeleteBehavior.Cascade);

        builder.HasMany(c => c.Warehouses)
            .WithOne(w => w.Company)
            .HasForeignKey(w => w.CompanyId)
            .OnDelete(DeleteBehavior.Cascade);

        builder.HasMany(c => c.Items)
            .WithOne(i => i.Company)
            .HasForeignKey(i => i.CompanyId)
            .OnDelete(DeleteBehavior.Cascade);

        builder.HasMany(c => c.InventoryTransactions)
            .WithOne(it => it.Company)
            .HasForeignKey(it => it.CompanyId)
            .OnDelete(DeleteBehavior.Cascade);

        builder.HasMany(c => c.Invoices)
            .WithOne(inv => inv.Company)
            .HasForeignKey(inv => inv.CompanyId)
            .OnDelete(DeleteBehavior.Cascade);

        builder.HasMany(c => c.Persons)
            .WithOne(p => p.Company)
            .HasForeignKey(p => p.CompanyId)
            .OnDelete(DeleteBehavior.Cascade);

        builder.HasMany(c => c.Users)
            .WithOne(u => u.Company)
            .HasForeignKey(u => u.CompanyId)
            .OnDelete(DeleteBehavior.Cascade);
    }
}
