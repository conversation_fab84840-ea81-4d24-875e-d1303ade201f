using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Mvc.Testing;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using BusinessManagementSystem.Infrastructure.Data;
using BusinessManagementSystem.Domain.Entities;
using System.Security.Claims;
using Microsoft.AspNetCore.Authentication;
using Microsoft.Extensions.Options;
using System.Text.Encodings.Web;

namespace BusinessManagementSystem.Tests.Integration;

/// <summary>
/// Custom WebApplicationFactory for integration tests
/// مصنع تطبيق الويب المخصص لاختبارات التكامل
/// </summary>
public class TestWebApplicationFactory<TStartup> : WebApplicationFactory<TStartup> where TStartup : class
{
    protected override void ConfigureWebHost(IWebHostBuilder builder)
    {
        builder.ConfigureServices(services =>
        {
            // Remove the existing DbContext registration
            var descriptor = services.SingleOrDefault(
                d => d.ServiceType == typeof(DbContextOptions<ApplicationDbContext>));
            if (descriptor != null)
            {
                services.Remove(descriptor);
            }

            // Add InMemory database for testing
            services.AddDbContext<ApplicationDbContext>(options =>
            {
                options.UseInMemoryDatabase("TestDatabase");
            });

            // Add test authentication
            services.AddAuthentication("Test")
                .AddScheme<AuthenticationSchemeOptions, TestAuthenticationHandler>("Test", options => { });

            // Build the service provider
            var serviceProvider = services.BuildServiceProvider();

            // Create a scope to obtain a reference to the database context
            using var scope = serviceProvider.CreateScope();
            var scopedServices = scope.ServiceProvider;
            var db = scopedServices.GetRequiredService<ApplicationDbContext>();
            var logger = scopedServices.GetRequiredService<ILogger<TestWebApplicationFactory<TStartup>>>();

            // Ensure the database is created
            db.Database.EnsureCreated();

            try
            {
                // Seed the database with test data
                SeedTestData(db);
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "An error occurred seeding the database with test data. Error: {Message}", ex.Message);
            }
        });

        builder.UseEnvironment("Testing");
    }

    private static void SeedTestData(ApplicationDbContext context)
    {
        // Clear existing data
        context.Database.EnsureDeleted();
        context.Database.EnsureCreated();

        // Seed test companies
        var testCompany = new Company
        {
            Id = 1,
            Name = "Test Company",
            TradeName = "Test Trade",
            TaxNumber = "*********",
            Address = "Test Address",
            Phone = "************",
            Email = "<EMAIL>",
            IsActive = true,
            CreatedAt = DateTime.UtcNow,
            CreatedBy = "System"
        };

        context.Companies.Add(testCompany);

        // Seed test roles
        var adminRole = new Role
        {
            Id = 1,
            Name = "Administrator",
            Description = "System Administrator",
            IsSystemRole = true,
            IsActive = true,
            CreatedAt = DateTime.UtcNow,
            CreatedBy = "System"
        };

        var managerRole = new Role
        {
            Id = 2,
            Name = "Manager",
            Description = "Manager Role",
            IsSystemRole = false,
            IsActive = true,
            CreatedAt = DateTime.UtcNow,
            CreatedBy = "System"
        };

        context.Roles.AddRange(adminRole, managerRole);

        // Seed test users
        var adminUser = new User
        {
            Id = 1,
            UserName = "admin",
            FullName = "Test Administrator",
            Email = "<EMAIL>",
            PasswordHash = BCrypt.Net.BCrypt.HashPassword("Admin123!"),
            CompanyId = 1,
            RoleId = 1,
            IsActive = true,
            CreatedAt = DateTime.UtcNow,
            CreatedBy = "System"
        };

        var managerUser = new User
        {
            Id = 2,
            UserName = "manager",
            FullName = "Test Manager",
            Email = "<EMAIL>",
            PasswordHash = BCrypt.Net.BCrypt.HashPassword("Manager123!"),
            CompanyId = 1,
            RoleId = 2,
            IsActive = true,
            CreatedAt = DateTime.UtcNow,
            CreatedBy = "System"
        };

        context.Users.AddRange(adminUser, managerUser);

        // Seed test departments
        var testDepartment = new Department
        {
            Id = 1,
            Name = "Test Department",
            Description = "Test Department Description",
            IsActive = true,
            CompanyId = 1,
            CreatedAt = DateTime.UtcNow,
            CreatedBy = "System"
        };

        context.Departments.Add(testDepartment);

        // Seed test warehouses
        var testWarehouse = new Warehouse
        {
            Id = 1,
            Name = "Test Warehouse",
            Description = "Test Warehouse Description",
            Location = "Test Location",
            MaxCapacity = 1000,
            CurrentUtilization = 0,
            IsActive = true,
            CompanyId = 1,
            CreatedAt = DateTime.UtcNow,
            CreatedBy = "System"
        };

        context.Warehouses.Add(testWarehouse);

        // Seed test items
        var testItem = new Item
        {
            Id = 1,
            Name = "Test Item",
            Description = "Test Item Description",
            UnitPrice = 100.00m,
            CostPrice = 80.00m,
            StockQuantity = 50,
            MinimumStockLevel = 10,
            MaximumStockLevel = 100,
            ReorderPoint = 15,
            Category = "Test Category",
            IsActive = true,
            CompanyId = 1,
            CreatedAt = DateTime.UtcNow,
            CreatedBy = "System"
        };

        context.Items.Add(testItem);

        // Seed test persons (customers/suppliers)
        var testCustomer = new Person
        {
            Id = 1,
            Name = "Test Customer",
            PersonType = "Customer",
            Email = "<EMAIL>",
            Phone = "************",
            Address = "Customer Address",
            IsActive = true,
            CompanyId = 1,
            CreatedAt = DateTime.UtcNow,
            CreatedBy = "System"
        };

        var testSupplier = new Person
        {
            Id = 2,
            Name = "Test Supplier",
            PersonType = "Supplier",
            Email = "<EMAIL>",
            Phone = "************",
            Address = "Supplier Address",
            IsActive = true,
            CompanyId = 1,
            CreatedAt = DateTime.UtcNow,
            CreatedBy = "System"
        };

        context.Persons.AddRange(testCustomer, testSupplier);

        // Seed test main cash
        var testMainCash = new MainCash
        {
            Id = 1,
            Balance = 10000.00m,
            Currency = "USD",
            LastUpdated = DateTime.UtcNow,
            CompanyId = 1,
            CreatedAt = DateTime.UtcNow,
            CreatedBy = "System"
        };

        context.MainCashes.Add(testMainCash);

        // Seed role permissions
        var adminPermissions = new List<RolePermission>
        {
            new RolePermission { RoleId = 1, PageName = "Companies", CanAdd = true, CanEdit = true, CanDelete = true, CanView = true, CreatedAt = DateTime.UtcNow, CreatedBy = "System" },
            new RolePermission { RoleId = 1, PageName = "Users", CanAdd = true, CanEdit = true, CanDelete = true, CanView = true, CreatedAt = DateTime.UtcNow, CreatedBy = "System" },
            new RolePermission { RoleId = 1, PageName = "Items", CanAdd = true, CanEdit = true, CanDelete = true, CanView = true, CreatedAt = DateTime.UtcNow, CreatedBy = "System" },
            new RolePermission { RoleId = 1, PageName = "Warehouses", CanAdd = true, CanEdit = true, CanDelete = true, CanView = true, CreatedAt = DateTime.UtcNow, CreatedBy = "System" }
        };

        var managerPermissions = new List<RolePermission>
        {
            new RolePermission { RoleId = 2, PageName = "Items", CanAdd = true, CanEdit = true, CanDelete = false, CanView = true, CreatedAt = DateTime.UtcNow, CreatedBy = "System" },
            new RolePermission { RoleId = 2, PageName = "Warehouses", CanAdd = true, CanEdit = true, CanDelete = false, CanView = true, CreatedAt = DateTime.UtcNow, CreatedBy = "System" }
        };

        context.RolePermissions.AddRange(adminPermissions);
        context.RolePermissions.AddRange(managerPermissions);

        context.SaveChanges();
    }
}

/// <summary>
/// Test authentication handler for integration tests
/// معالج المصادقة للاختبارات التكاملية
/// </summary>
public class TestAuthenticationHandler : AuthenticationHandler<AuthenticationSchemeOptions>
{
    public TestAuthenticationHandler(IOptionsMonitor<AuthenticationSchemeOptions> options,
        ILoggerFactory logger, UrlEncoder encoder)
        : base(options, logger, encoder)
    {
    }

    protected override Task<AuthenticateResult> HandleAuthenticateAsync()
    {
        var claims = new[]
        {
            new Claim(ClaimTypes.Name, "TestUser"),
            new Claim(ClaimTypes.NameIdentifier, "1"),
            new Claim(ClaimTypes.Role, "Administrator"),
            new Claim("CompanyId", "1")
        };

        var identity = new ClaimsIdentity(claims, "Test");
        var principal = new ClaimsPrincipal(identity);
        var ticket = new AuthenticationTicket(principal, "Test");

        return Task.FromResult(AuthenticateResult.Success(ticket));
    }
}
