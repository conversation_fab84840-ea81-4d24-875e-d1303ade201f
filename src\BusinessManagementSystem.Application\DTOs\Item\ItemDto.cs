using BusinessManagementSystem.Application.DTOs.Common;

namespace BusinessManagementSystem.Application.DTOs.Item;

/// <summary>
/// Item DTO for data transfer
/// DTO الصنف لنقل البيانات
/// </summary>
public class ItemDto : BaseDto
{
    /// <summary>
    /// Item name
    /// اسم الصنف
    /// </summary>
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// Item description
    /// وصف الصنف
    /// </summary>
    public string? Description { get; set; }

    /// <summary>
    /// Item category
    /// فئة الصنف
    /// </summary>
    public string? Category { get; set; }

    /// <summary>
    /// Item barcode
    /// باركود الصنف
    /// </summary>
    public string? Barcode { get; set; }

    /// <summary>
    /// Unit of measure
    /// وحدة القياس
    /// </summary>
    public string UnitOfMeasure { get; set; } = string.Empty;

    /// <summary>
    /// Unit price
    /// سعر الوحدة
    /// </summary>
    public decimal UnitPrice { get; set; }

    /// <summary>
    /// Cost price
    /// سعر التكلفة
    /// </summary>
    public decimal CostPrice { get; set; }

    /// <summary>
    /// Current stock quantity
    /// كمية المخزون الحالية
    /// </summary>
    public decimal StockQuantity { get; set; }

    /// <summary>
    /// Minimum stock level
    /// الحد الأدنى للمخزون
    /// </summary>
    public decimal MinimumStockLevel { get; set; }

    /// <summary>
    /// Maximum stock level
    /// الحد الأقصى للمخزون
    /// </summary>
    public decimal MaximumStockLevel { get; set; }

    /// <summary>
    /// Reorder point
    /// نقطة إعادة الطلب
    /// </summary>
    public decimal ReorderPoint { get; set; }

    /// <summary>
    /// Item image URL
    /// رابط صورة الصنف
    /// </summary>
    public string? ImageUrl { get; set; }

    /// <summary>
    /// Whether the item is active
    /// ما إذا كان الصنف نشطًا
    /// </summary>
    public bool IsActive { get; set; }

    /// <summary>
    /// Company ID
    /// معرف الشركة
    /// </summary>
    public int CompanyId { get; set; }

    /// <summary>
    /// Company name
    /// اسم الشركة
    /// </summary>
    public string CompanyName { get; set; } = string.Empty;
}

/// <summary>
/// Create Item DTO
/// DTO إنشاء الصنف
/// </summary>
public class CreateItemDto
{
    /// <summary>
    /// Item name
    /// اسم الصنف
    /// </summary>
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// Item description
    /// وصف الصنف
    /// </summary>
    public string? Description { get; set; }

    /// <summary>
    /// Item category
    /// فئة الصنف
    /// </summary>
    public string? Category { get; set; }

    /// <summary>
    /// Item barcode
    /// باركود الصنف
    /// </summary>
    public string? Barcode { get; set; }

    /// <summary>
    /// Unit of measure
    /// وحدة القياس
    /// </summary>
    public string UnitOfMeasure { get; set; } = string.Empty;

    /// <summary>
    /// Unit price
    /// سعر الوحدة
    /// </summary>
    public decimal UnitPrice { get; set; }

    /// <summary>
    /// Cost price
    /// سعر التكلفة
    /// </summary>
    public decimal CostPrice { get; set; }

    /// <summary>
    /// Current stock quantity
    /// كمية المخزون الحالية
    /// </summary>
    public decimal StockQuantity { get; set; }

    /// <summary>
    /// Minimum stock level
    /// الحد الأدنى للمخزون
    /// </summary>
    public decimal MinimumStockLevel { get; set; }

    /// <summary>
    /// Maximum stock level
    /// الحد الأقصى للمخزون
    /// </summary>
    public decimal MaximumStockLevel { get; set; }

    /// <summary>
    /// Reorder point
    /// نقطة إعادة الطلب
    /// </summary>
    public decimal ReorderPoint { get; set; }

    /// <summary>
    /// Whether the item is active
    /// ما إذا كان الصنف نشطًا
    /// </summary>
    public bool IsActive { get; set; } = true;

    /// <summary>
    /// Company ID
    /// معرف الشركة
    /// </summary>
    public int CompanyId { get; set; }
}

/// <summary>
/// Update Item DTO
/// DTO تحديث الصنف
/// </summary>
public class UpdateItemDto
{
    /// <summary>
    /// Item name
    /// اسم الصنف
    /// </summary>
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// Item description
    /// وصف الصنف
    /// </summary>
    public string? Description { get; set; }

    /// <summary>
    /// Item category
    /// فئة الصنف
    /// </summary>
    public string? Category { get; set; }

    /// <summary>
    /// Unit of measure
    /// وحدة القياس
    /// </summary>
    public string UnitOfMeasure { get; set; } = string.Empty;

    /// <summary>
    /// Unit price
    /// سعر الوحدة
    /// </summary>
    public decimal UnitPrice { get; set; }

    /// <summary>
    /// Cost price
    /// سعر التكلفة
    /// </summary>
    public decimal CostPrice { get; set; }

    /// <summary>
    /// Minimum stock level
    /// الحد الأدنى للمخزون
    /// </summary>
    public decimal MinimumStockLevel { get; set; }

    /// <summary>
    /// Maximum stock level
    /// الحد الأقصى للمخزون
    /// </summary>
    public decimal MaximumStockLevel { get; set; }

    /// <summary>
    /// Reorder point
    /// نقطة إعادة الطلب
    /// </summary>
    public decimal ReorderPoint { get; set; }

    /// <summary>
    /// Whether the item is active
    /// ما إذا كان الصنف نشطًا
    /// </summary>
    public bool IsActive { get; set; }
}
