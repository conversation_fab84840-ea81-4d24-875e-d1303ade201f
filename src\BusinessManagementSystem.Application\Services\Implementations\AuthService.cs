using System.Security.Claims;
using AutoMapper;
using Microsoft.Extensions.Options;
using BusinessManagementSystem.Application.DTOs.Auth;
using BusinessManagementSystem.Application.Interfaces;
using BusinessManagementSystem.Application.Services.Interfaces;
using BusinessManagementSystem.Application.Common.Models;

namespace BusinessManagementSystem.Application.Services.Implementations;

/// <summary>
/// Authentication service implementation for login and token management
/// تنفيذ خدمة المصادقة لتسجيل الدخول وإدارة الرموز
/// </summary>
public class AuthService : IAuthService
{
    private readonly IUnitOfWork _unitOfWork;
    private readonly IJwtTokenService _jwtTokenService;
    private readonly IPasswordHashService _passwordHashService;
    private readonly IMapper _mapper;
    private readonly JwtSettings _jwtSettings;

    public AuthService(
        IUnitOfWork unitOfWork,
        IJwtTokenService jwtTokenService,
        IPasswordHashService passwordHashService,
        IMapper mapper,
        IOptions<JwtSettings> jwtSettings)
    {
        _unitOfWork = unitOfWork;
        _jwtTokenService = jwtTokenService;
        _passwordHashService = passwordHashService;
        _mapper = mapper;
        _jwtSettings = jwtSettings.Value;
    }

    public async Task<LoginResponseDto?> LoginAsync(LoginDto loginDto, CancellationToken cancellationToken = default)
    {
        // Find user by username or email
        var user = await _unitOfWork.Users.GetByUsernameAsync(loginDto.Username, cancellationToken);
        if (user == null)
        {
            user = await _unitOfWork.Users.GetByEmailAsync(loginDto.Username, cancellationToken);
        }

        if (user == null || !user.IsActive)
        {
            return null;
        }

        // Verify password
        if (!_passwordHashService.VerifyPassword(loginDto.Password, user.PasswordHash))
        {
            return null;
        }

        // Generate tokens
        var accessToken = await GenerateAccessTokenAsync(user.Id, cancellationToken);
        var refreshToken = await GenerateRefreshTokenAsync(user.Id, cancellationToken);

        // Update last login time
        await _unitOfWork.Users.UpdateLastLoginAsync(user.Id, cancellationToken);
        await _unitOfWork.SaveChangesAsync(cancellationToken);

        // Map user info
        var userInfo = _mapper.Map<UserInfoDto>(user);

        return new LoginResponseDto
        {
            AccessToken = accessToken,
            RefreshToken = refreshToken,
            ExpiresAt = DateTime.UtcNow.AddMinutes(_jwtSettings.AccessTokenExpiryMinutes),
            User = userInfo
        };
    }

    public async Task<TokenResponseDto?> RefreshTokenAsync(RefreshTokenDto refreshTokenDto, CancellationToken cancellationToken = default)
    {
        var user = await _unitOfWork.Users.GetByRefreshTokenAsync(refreshTokenDto.RefreshToken, cancellationToken);
        if (user == null)
        {
            return null;
        }

        // Generate new tokens
        var accessToken = await GenerateAccessTokenAsync(user.Id, cancellationToken);
        var newRefreshToken = await GenerateRefreshTokenAsync(user.Id, cancellationToken);

        return new TokenResponseDto
        {
            AccessToken = accessToken,
            RefreshToken = newRefreshToken,
            ExpiresAt = DateTime.UtcNow.AddMinutes(_jwtSettings.AccessTokenExpiryMinutes)
        };
    }

    public async Task<bool> LogoutAsync(string refreshToken, CancellationToken cancellationToken = default)
    {
        var user = await _unitOfWork.Users.GetByRefreshTokenAsync(refreshToken, cancellationToken);
        if (user == null)
        {
            return false;
        }

        // Clear refresh token
        await _unitOfWork.Users.UpdateRefreshTokenAsync(user.Id, string.Empty, DateTime.UtcNow, cancellationToken);
        await _unitOfWork.SaveChangesAsync(cancellationToken);

        return true;
    }

    public async Task<string> GenerateAccessTokenAsync(int userId, CancellationToken cancellationToken = default)
    {
        var user = await _unitOfWork.Users.GetWithRoleAsync(userId, cancellationToken);
        if (user == null)
        {
            throw new ArgumentException("User not found", nameof(userId));
        }

        var claims = new List<Claim>
        {
            new(ClaimTypes.NameIdentifier, user.Id.ToString()),
            new("userId", user.Id.ToString()),
            new(ClaimTypes.Name, user.Username),
            new("fullName", user.FullName),
            new(ClaimTypes.Email, user.Email ?? string.Empty),
            new(ClaimTypes.Role, user.Role.Name),
            new("role", user.Role.Name),
            new("companyId", user.CompanyId.ToString()),
            new("companyName", user.Company.Name)
        };

        // Add permissions as claims
        foreach (var permission in user.Role.RolePermissions)
        {
            if (permission.CanAdd)
                claims.Add(new Claim($"permission_{permission.PageName}_Add", "true"));
            if (permission.CanEdit)
                claims.Add(new Claim($"permission_{permission.PageName}_Edit", "true"));
            if (permission.CanDelete)
                claims.Add(new Claim($"permission_{permission.PageName}_Delete", "true"));
            if (permission.CanView)
                claims.Add(new Claim($"permission_{permission.PageName}_View", "true"));
        }

        return _jwtTokenService.GenerateAccessToken(claims);
    }

    public async Task<string> GenerateRefreshTokenAsync(int userId, CancellationToken cancellationToken = default)
    {
        var refreshToken = _jwtTokenService.GenerateRefreshToken();
        var expiryTime = DateTime.UtcNow.AddDays(_jwtSettings.RefreshTokenExpiryDays);

        await _unitOfWork.Users.UpdateRefreshTokenAsync(userId, refreshToken, expiryTime, cancellationToken);
        await _unitOfWork.SaveChangesAsync(cancellationToken);

        return refreshToken;
    }

    public async Task<bool> ValidateRefreshTokenAsync(string refreshToken, CancellationToken cancellationToken = default)
    {
        var user = await _unitOfWork.Users.GetByRefreshTokenAsync(refreshToken, cancellationToken);
        return user != null;
    }

    public async Task<UserInfoDto?> GetUserInfoFromTokenAsync(string token, CancellationToken cancellationToken = default)
    {
        var claims = _jwtTokenService.GetClaimsFromToken(token);
        if (claims == null)
        {
            return null;
        }

        var userIdClaim = claims.FirstOrDefault(c => c.Type == "userId")?.Value;
        if (!int.TryParse(userIdClaim, out int userId))
        {
            return null;
        }

        var user = await _unitOfWork.Users.GetWithRoleAsync(userId, cancellationToken);
        if (user == null)
        {
            return null;
        }

        return _mapper.Map<UserInfoDto>(user);
    }
}
