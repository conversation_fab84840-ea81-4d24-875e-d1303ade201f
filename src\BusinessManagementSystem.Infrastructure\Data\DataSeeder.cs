using Microsoft.EntityFrameworkCore;
using BusinessManagementSystem.Domain.Entities;
using BusinessManagementSystem.Infrastructure.Services;

namespace BusinessManagementSystem.Infrastructure.Data;

/// <summary>
/// Data seeder for initial application data
/// مُزرع البيانات للبيانات الأولية للتطبيق
/// </summary>
public static class DataSeeder
{
    /// <summary>
    /// Seed initial data to the database
    /// زرع البيانات الأولية في قاعدة البيانات
    /// </summary>
    public static async Task SeedAsync(BusinessManagementDbContext context)
    {
        // Ensure database is created
        await context.Database.EnsureCreatedAsync();

        // Seed roles if they don't exist
        if (!await context.Roles.AnyAsync())
        {
            await SeedRolesAsync(context);
        }

        // Seed default company if it doesn't exist
        if (!await context.Companies.AnyAsync())
        {
            await SeedDefaultCompanyAsync(context);
        }

        // Seed default admin user if it doesn't exist
        if (!await context.Users.AnyAsync())
        {
            await SeedDefaultUserAsync(context);
        }

        await context.SaveChangesAsync();
    }

    /// <summary>
    /// Seed default roles
    /// زرع الأدوار الافتراضية
    /// </summary>
    private static async Task SeedRolesAsync(BusinessManagementDbContext context)
    {
        var roles = new[]
        {
            new Role
            {
                Id = 1,
                Name = "Administrator",
                Description = "System Administrator with full access",
                CreatedAt = DateTime.UtcNow,
                UpdatedAt = DateTime.UtcNow
            },
            new Role
            {
                Id = 2,
                Name = "Manager",
                Description = "Business Manager with management access",
                CreatedAt = DateTime.UtcNow,
                UpdatedAt = DateTime.UtcNow
            },
            new Role
            {
                Id = 3,
                Name = "Employee",
                Description = "Regular Employee with limited access",
                CreatedAt = DateTime.UtcNow,
                UpdatedAt = DateTime.UtcNow
            }
        };

        await context.Roles.AddRangeAsync(roles);
        await context.SaveChangesAsync();

        // Seed role permissions for Administrator
        await SeedRolePermissionsAsync(context, 1, "Administrator");
        await SeedRolePermissionsAsync(context, 2, "Manager");
        await SeedRolePermissionsAsync(context, 3, "Employee");
    }

    /// <summary>
    /// Seed role permissions
    /// زرع صلاحيات الأدوار
    /// </summary>
    private static async Task SeedRolePermissionsAsync(BusinessManagementDbContext context, int roleId, string roleName)
    {
        var pages = new[]
        {
            "Companies", "Departments", "Partners", "MainCash", "FinancialTransactions",
            "Warehouses", "Items", "InventoryTransactions", "Invoices", "InvoiceDetails",
            "Persons", "Users", "Roles", "RolePermissions"
        };

        var permissions = new List<RolePermission>();

        foreach (var page in pages)
        {
            bool canAdd, canEdit, canDelete, canView;

            // Set permissions based on role
            switch (roleName)
            {
                case "Administrator":
                    canAdd = canEdit = canDelete = canView = true;
                    break;
                case "Manager":
                    canAdd = canEdit = canView = true;
                    canDelete = page != "Users" && page != "Roles"; // Managers can't delete users or roles
                    break;
                case "Employee":
                    canView = true;
                    canAdd = page == "FinancialTransactions" || page == "InventoryTransactions" || page == "Invoices";
                    canEdit = page == "FinancialTransactions" || page == "InventoryTransactions" || page == "Invoices";
                    canDelete = false;
                    break;
                default:
                    canAdd = canEdit = canDelete = canView = false;
                    break;
            }

            permissions.Add(new RolePermission
            {
                RoleId = roleId,
                PageName = page,
                CanAdd = canAdd,
                CanEdit = canEdit,
                CanDelete = canDelete,
                CanView = canView,
                CreatedAt = DateTime.UtcNow,
                UpdatedAt = DateTime.UtcNow
            });
        }

        await context.RolePermissions.AddRangeAsync(permissions);
    }

    /// <summary>
    /// Seed default company
    /// زرع الشركة الافتراضية
    /// </summary>
    private static async Task SeedDefaultCompanyAsync(BusinessManagementDbContext context)
    {
        var company = new Company
        {
            Id = 1,
            Name = "Default Company",
            Description = "Default company for initial setup",
            Address = "123 Business Street, Business City",
            Phone = "******-567-8900",
            CreatedAt = DateTime.UtcNow,
            UpdatedAt = DateTime.UtcNow
        };

        await context.Companies.AddAsync(company);
        await context.SaveChangesAsync();

        // Create main cash for the company
        var mainCash = new MainCash
        {
            CompanyId = company.Id,
            Balance = 0,
            Currency = "USD",
            LastUpdated = DateTime.UtcNow,
            CreatedAt = DateTime.UtcNow,
            UpdatedAt = DateTime.UtcNow
        };

        await context.MainCashes.AddAsync(mainCash);
    }

    /// <summary>
    /// Seed default admin user
    /// زرع المستخدم الإداري الافتراضي
    /// </summary>
    private static async Task SeedDefaultUserAsync(BusinessManagementDbContext context)
    {
        var passwordHashService = new PasswordHashService();
        var hashedPassword = passwordHashService.HashPassword("Admin@123");

        var adminUser = new User
        {
            Username = "admin",
            PasswordHash = hashedPassword,
            FullName = "System Administrator",
            Email = "<EMAIL>",
            IsActive = true,
            RoleId = 1, // Administrator role
            CompanyId = 1, // Default company
            CreatedAt = DateTime.UtcNow,
            UpdatedAt = DateTime.UtcNow
        };

        await context.Users.AddAsync(adminUser);
    }
}
