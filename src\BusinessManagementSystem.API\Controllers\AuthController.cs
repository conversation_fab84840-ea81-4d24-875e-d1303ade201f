using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using BusinessManagementSystem.Application.DTOs.Auth;
using BusinessManagementSystem.Application.Services.Interfaces;

namespace BusinessManagementSystem.API.Controllers;

/// <summary>
/// Authentication controller for login and token management
/// وحدة تحكم المصادقة لتسجيل الدخول وإدارة الرموز
/// </summary>
[Route("api/[controller]")]
[ApiController]
public class AuthController : BaseApiController
{
    private readonly IAuthService _authService;
    private readonly ILogger<AuthController> _logger;

    public AuthController(IAuthService authService, ILogger<AuthController> logger)
    {
        _authService = authService;
        _logger = logger;
    }

    /// <summary>
    /// User login
    /// تسجيل دخول المستخدم
    /// </summary>
    [HttpPost("login")]
    [AllowAnonymous]
    public async Task<IActionResult> Login([FromBody] LoginDto loginDto, CancellationToken cancellationToken = default)
    {
        try
        {
            if (!ModelState.IsValid)
            {
                return CreateErrorResponse("Invalid login data.", 400);
            }

            var result = await _authService.LoginAsync(loginDto, cancellationToken);
            if (result == null)
            {
                _logger.LogWarning("Failed login attempt for username: {Username}", loginDto.Username);
                return CreateErrorResponse("Invalid username or password.", 401);
            }

            _logger.LogInformation("Successful login for user: {Username}", loginDto.Username);
            return CreateResponse(result, "Login successful.");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during login for username: {Username}", loginDto.Username);
            return CreateErrorResponse("An error occurred during login.", 500);
        }
    }

    /// <summary>
    /// Refresh access token
    /// تحديث رمز الوصول
    /// </summary>
    [HttpPost("refresh-token")]
    [AllowAnonymous]
    public async Task<IActionResult> RefreshToken([FromBody] RefreshTokenDto refreshTokenDto, CancellationToken cancellationToken = default)
    {
        try
        {
            if (!ModelState.IsValid)
            {
                return CreateErrorResponse("Invalid refresh token data.", 400);
            }

            var result = await _authService.RefreshTokenAsync(refreshTokenDto, cancellationToken);
            if (result == null)
            {
                _logger.LogWarning("Failed refresh token attempt");
                return CreateErrorResponse("Invalid refresh token.", 401);
            }

            _logger.LogInformation("Successful token refresh");
            return CreateResponse(result, "Token refreshed successfully.");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during token refresh");
            return CreateErrorResponse("An error occurred during token refresh.", 500);
        }
    }

    /// <summary>
    /// User logout
    /// تسجيل خروج المستخدم
    /// </summary>
    [HttpPost("logout")]
    [Authorize]
    public async Task<IActionResult> Logout([FromBody] RefreshTokenDto refreshTokenDto, CancellationToken cancellationToken = default)
    {
        try
        {
            var result = await _authService.LogoutAsync(refreshTokenDto.RefreshToken, cancellationToken);
            if (!result)
            {
                return CreateErrorResponse("Failed to logout.", 400);
            }

            var userId = GetCurrentUserId();
            _logger.LogInformation("Successful logout for user ID: {UserId}", userId);
            return CreateResponse<object>(null, "Logout successful.");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during logout");
            return CreateErrorResponse("An error occurred during logout.", 500);
        }
    }

    /// <summary>
    /// Get current user information
    /// الحصول على معلومات المستخدم الحالي
    /// </summary>
    [HttpGet("me")]
    [Authorize]
    public async Task<IActionResult> GetCurrentUser(CancellationToken cancellationToken = default)
    {
        try
        {
            var token = Request.Headers["Authorization"].ToString().Replace("Bearer ", "");
            var userInfo = await _authService.GetUserInfoFromTokenAsync(token, cancellationToken);
            
            if (userInfo == null)
            {
                return CreateErrorResponse("User information not found.", 404);
            }

            return CreateResponse(userInfo, "User information retrieved successfully.");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving current user information");
            return CreateErrorResponse("An error occurred while retrieving user information.", 500);
        }
    }

    /// <summary>
    /// Validate token
    /// التحقق من صحة الرمز
    /// </summary>
    [HttpPost("validate-token")]
    [AllowAnonymous]
    public IActionResult ValidateToken([FromBody] string token)
    {
        try
        {
            // This will be implemented by the JWT middleware
            // For now, return a simple validation
            if (string.IsNullOrWhiteSpace(token))
            {
                return CreateErrorResponse("Token is required.", 400);
            }

            return CreateResponse(new { IsValid = true }, "Token is valid.");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error validating token");
            return CreateErrorResponse("An error occurred while validating token.", 500);
        }
    }
}
