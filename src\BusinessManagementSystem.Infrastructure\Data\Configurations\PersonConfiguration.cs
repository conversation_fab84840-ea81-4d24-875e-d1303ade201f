using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using BusinessManagementSystem.Domain.Entities;

namespace BusinessManagementSystem.Infrastructure.Data.Configurations;

/// <summary>
/// Entity configuration for Person
/// تكوين الكيان للشخص
/// </summary>
public class PersonConfiguration : IEntityTypeConfiguration<Person>
{
    public void Configure(EntityTypeBuilder<Person> builder)
    {
        builder.ToTable("Persons");

        builder.HasKey(p => p.Id);

        builder.Property(p => p.Name)
            .IsRequired()
            .HasMaxLength(200);

        builder.Property(p => p.Address)
            .HasMaxLength(500);

        builder.Property(p => p.Phone)
            .HasMaxLength(20);

        builder.Property(p => p.Email)
            .HasMaxLength(100);

        builder.Property(p => p.PersonType)
            .IsRequired()
            .HasMaxLength(20);

        builder.Property(p => p.CurrentBalance)
            .HasColumnType("decimal(18,2)")
            .HasDefaultValue(0);

        builder.Property(p => p.TaxNumber)
            .HasMaxLength(50);

        // Indexes
        builder.HasIndex(p => new { p.CompanyId, p.Name });

        builder.HasIndex(p => new { p.CompanyId, p.PersonType });

        builder.HasIndex(p => p.Email)
            .HasFilter("[Email] IS NOT NULL");

        builder.HasIndex(p => p.TaxNumber)
            .HasFilter("[TaxNumber] IS NOT NULL");

        // Relationships
        builder.HasOne(p => p.Company)
            .WithMany(c => c.Persons)
            .HasForeignKey(p => p.CompanyId)
            .OnDelete(DeleteBehavior.Cascade);

        builder.HasMany(p => p.Invoices)
            .WithOne(i => i.Person)
            .HasForeignKey(i => i.PersonId)
            .OnDelete(DeleteBehavior.Restrict);
    }
}
