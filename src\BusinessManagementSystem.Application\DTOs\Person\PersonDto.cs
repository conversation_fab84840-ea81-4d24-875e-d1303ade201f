using BusinessManagementSystem.Application.DTOs.Common;

namespace BusinessManagementSystem.Application.DTOs.Person;

/// <summary>
/// Person DTO for data transfer
/// DTO الشخص لنقل البيانات
/// </summary>
public class PersonDto : BaseDto
{
    /// <summary>
    /// Person name
    /// اسم الشخص
    /// </summary>
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// Person type (Customer/Supplier)
    /// نوع الشخص (عميل/مورد)
    /// </summary>
    public string PersonType { get; set; } = string.Empty;

    /// <summary>
    /// Person email
    /// بريد الشخص الإلكتروني
    /// </summary>
    public string? Email { get; set; }

    /// <summary>
    /// Person phone
    /// هاتف الشخص
    /// </summary>
    public string? Phone { get; set; }

    /// <summary>
    /// Person address
    /// عنوان الشخص
    /// </summary>
    public string? Address { get; set; }

    /// <summary>
    /// Tax number
    /// الرقم الضريبي
    /// </summary>
    public string? TaxNumber { get; set; }

    /// <summary>
    /// Whether the person is active
    /// ما إذا كان الشخص نشطًا
    /// </summary>
    public bool IsActive { get; set; }

    /// <summary>
    /// Company ID
    /// معرف الشركة
    /// </summary>
    public int CompanyId { get; set; }

    /// <summary>
    /// Company name
    /// اسم الشركة
    /// </summary>
    public string CompanyName { get; set; } = string.Empty;
}

/// <summary>
/// Create Person DTO
/// DTO إنشاء الشخص
/// </summary>
public class CreatePersonDto
{
    /// <summary>
    /// Person name
    /// اسم الشخص
    /// </summary>
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// Person type (Customer/Supplier)
    /// نوع الشخص (عميل/مورد)
    /// </summary>
    public string PersonType { get; set; } = string.Empty;

    /// <summary>
    /// Person email
    /// بريد الشخص الإلكتروني
    /// </summary>
    public string? Email { get; set; }

    /// <summary>
    /// Person phone
    /// هاتف الشخص
    /// </summary>
    public string? Phone { get; set; }

    /// <summary>
    /// Person address
    /// عنوان الشخص
    /// </summary>
    public string? Address { get; set; }

    /// <summary>
    /// Tax number
    /// الرقم الضريبي
    /// </summary>
    public string? TaxNumber { get; set; }

    /// <summary>
    /// Whether the person is active
    /// ما إذا كان الشخص نشطًا
    /// </summary>
    public bool IsActive { get; set; } = true;

    /// <summary>
    /// Company ID
    /// معرف الشركة
    /// </summary>
    public int CompanyId { get; set; }
}

/// <summary>
/// Update Person DTO
/// DTO تحديث الشخص
/// </summary>
public class UpdatePersonDto
{
    /// <summary>
    /// Person name
    /// اسم الشخص
    /// </summary>
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// Person type (Customer/Supplier)
    /// نوع الشخص (عميل/مورد)
    /// </summary>
    public string PersonType { get; set; } = string.Empty;

    /// <summary>
    /// Person email
    /// بريد الشخص الإلكتروني
    /// </summary>
    public string? Email { get; set; }

    /// <summary>
    /// Person phone
    /// هاتف الشخص
    /// </summary>
    public string? Phone { get; set; }

    /// <summary>
    /// Person address
    /// عنوان الشخص
    /// </summary>
    public string? Address { get; set; }

    /// <summary>
    /// Tax number
    /// الرقم الضريبي
    /// </summary>
    public string? TaxNumber { get; set; }

    /// <summary>
    /// Whether the person is active
    /// ما إذا كان الشخص نشطًا
    /// </summary>
    public bool IsActive { get; set; }
}
