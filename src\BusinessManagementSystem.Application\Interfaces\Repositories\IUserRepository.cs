using BusinessManagementSystem.Domain.Entities;

namespace BusinessManagementSystem.Application.Interfaces.Repositories;

/// <summary>
/// User repository interface with specific operations
/// واجهة مستودع المستخدم مع العمليات المخصصة
/// </summary>
public interface IUserRepository : IGenericRepository<User>
{
    /// <summary>
    /// Get user by username
    /// الحصول على المستخدم بواسطة اسم المستخدم
    /// </summary>
    Task<User?> GetByUsernameAsync(string username, CancellationToken cancellationToken = default);

    /// <summary>
    /// Get user by email
    /// الحصول على المستخدم بواسطة البريد الإلكتروني
    /// </summary>
    Task<User?> GetByEmailAsync(string email, CancellationToken cancellationToken = default);

    /// <summary>
    /// Get user by refresh token
    /// الحصول على المستخدم بواسطة رمز التحديث
    /// </summary>
    Task<User?> GetByRefreshTokenAsync(string refreshToken, CancellationToken cancellationToken = default);

    /// <summary>
    /// Get user with role information
    /// الحصول على المستخدم مع معلومات الدور
    /// </summary>
    Task<User?> GetWithRoleAsync(int userId, CancellationToken cancellationToken = default);

    /// <summary>
    /// Get users by company
    /// الحصول على المستخدمين بواسطة الشركة
    /// </summary>
    Task<IEnumerable<User>> GetByCompanyAsync(int companyId, CancellationToken cancellationToken = default);

    /// <summary>
    /// Get active users by company
    /// الحصول على المستخدمين النشطين بواسطة الشركة
    /// </summary>
    Task<IEnumerable<User>> GetActiveByCompanyAsync(int companyId, CancellationToken cancellationToken = default);

    /// <summary>
    /// Update user's last login time
    /// تحديث وقت آخر تسجيل دخول للمستخدم
    /// </summary>
    Task UpdateLastLoginAsync(int userId, CancellationToken cancellationToken = default);

    /// <summary>
    /// Update user's refresh token
    /// تحديث رمز التحديث للمستخدم
    /// </summary>
    Task UpdateRefreshTokenAsync(int userId, string refreshToken, DateTime expiryTime, CancellationToken cancellationToken = default);
}
