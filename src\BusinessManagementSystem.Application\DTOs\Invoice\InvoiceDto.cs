using BusinessManagementSystem.Application.DTOs.Common;

namespace BusinessManagementSystem.Application.DTOs.Invoice;

/// <summary>
/// Invoice data transfer object
/// كائن نقل بيانات الفاتورة
/// </summary>
public class InvoiceDto : BaseDto
{
    /// <summary>
    /// Invoice number
    /// رقم الفاتورة
    /// </summary>
    public string InvoiceNumber { get; set; } = string.Empty;

    /// <summary>
    /// Invoice date
    /// تاريخ الفاتورة
    /// </summary>
    public DateTime InvoiceDate { get; set; }

    /// <summary>
    /// Invoice type (Sales/Purchase/Return)
    /// نوع الفاتورة (مبيعات/مشتريات/مرتجع)
    /// </summary>
    public string InvoiceType { get; set; } = string.Empty;

    /// <summary>
    /// Total amount before tax and discount
    /// المبلغ الإجمالي قبل الضريبة والخصم
    /// </summary>
    public decimal TotalAmount { get; set; }

    /// <summary>
    /// Tax amount
    /// مبلغ الضريبة
    /// </summary>
    public decimal TaxAmount { get; set; }

    /// <summary>
    /// Discount amount
    /// مبلغ الخصم
    /// </summary>
    public decimal DiscountAmount { get; set; }

    /// <summary>
    /// Final amount (Total + Tax - Discount)
    /// المبلغ النهائي (الإجمالي + الضريبة - الخصم)
    /// </summary>
    public decimal FinalAmount => TotalAmount + TaxAmount - DiscountAmount;

    /// <summary>
    /// Invoice status
    /// حالة الفاتورة
    /// </summary>
    public string Status { get; set; } = string.Empty;

    /// <summary>
    /// Additional notes
    /// ملاحظات إضافية
    /// </summary>
    public string? Notes { get; set; }

    /// <summary>
    /// Person identifier (Customer/Supplier)
    /// معرف الشخص (عميل/مورد)
    /// </summary>
    public int PersonId { get; set; }

    /// <summary>
    /// Person name
    /// اسم الشخص
    /// </summary>
    public string PersonName { get; set; } = string.Empty;

    /// <summary>
    /// Company identifier
    /// معرف الشركة
    /// </summary>
    public int CompanyId { get; set; }

    /// <summary>
    /// Invoice details
    /// تفاصيل الفاتورة
    /// </summary>
    public List<InvoiceDetailDto> InvoiceDetails { get; set; } = new();
}

/// <summary>
/// Invoice detail data transfer object
/// كائن نقل بيانات تفاصيل الفاتورة
/// </summary>
public class InvoiceDetailDto : BaseDto
{
    /// <summary>
    /// Item quantity
    /// كمية الصنف
    /// </summary>
    public int Quantity { get; set; }

    /// <summary>
    /// Unit price at time of invoice
    /// سعر الوحدة وقت الفاتورة
    /// </summary>
    public decimal UnitPrice { get; set; }

    /// <summary>
    /// Line total
    /// إجمالي السطر
    /// </summary>
    public decimal LineTotal { get; set; }

    /// <summary>
    /// Discount percentage for this line
    /// نسبة الخصم لهذا السطر
    /// </summary>
    public decimal DiscountPercentage { get; set; }

    /// <summary>
    /// Item identifier
    /// معرف الصنف
    /// </summary>
    public int ItemId { get; set; }

    /// <summary>
    /// Item name
    /// اسم الصنف
    /// </summary>
    public string ItemName { get; set; } = string.Empty;

    /// <summary>
    /// Item unit of measurement
    /// وحدة قياس الصنف
    /// </summary>
    public string ItemUnit { get; set; } = string.Empty;
}

/// <summary>
/// Create invoice DTO
/// DTO إنشاء الفاتورة
/// </summary>
public class CreateInvoiceDto
{
    /// <summary>
    /// Invoice date
    /// تاريخ الفاتورة
    /// </summary>
    public DateTime InvoiceDate { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// Invoice type (Sales/Purchase/Return)
    /// نوع الفاتورة (مبيعات/مشتريات/مرتجع)
    /// </summary>
    public string InvoiceType { get; set; } = string.Empty;

    /// <summary>
    /// Tax amount
    /// مبلغ الضريبة
    /// </summary>
    public decimal TaxAmount { get; set; }

    /// <summary>
    /// Discount amount
    /// مبلغ الخصم
    /// </summary>
    public decimal DiscountAmount { get; set; }

    /// <summary>
    /// Additional notes
    /// ملاحظات إضافية
    /// </summary>
    public string? Notes { get; set; }

    /// <summary>
    /// Person identifier (Customer/Supplier)
    /// معرف الشخص (عميل/مورد)
    /// </summary>
    public int PersonId { get; set; }

    /// <summary>
    /// Company identifier
    /// معرف الشركة
    /// </summary>
    public int CompanyId { get; set; }

    /// <summary>
    /// Invoice details
    /// تفاصيل الفاتورة
    /// </summary>
    public List<CreateInvoiceDetailDto> InvoiceDetails { get; set; } = new();
}

/// <summary>
/// Create invoice detail DTO
/// DTO إنشاء تفاصيل الفاتورة
/// </summary>
public class CreateInvoiceDetailDto
{
    /// <summary>
    /// Item quantity
    /// كمية الصنف
    /// </summary>
    public int Quantity { get; set; }

    /// <summary>
    /// Unit price at time of invoice
    /// سعر الوحدة وقت الفاتورة
    /// </summary>
    public decimal UnitPrice { get; set; }

    /// <summary>
    /// Discount percentage for this line
    /// نسبة الخصم لهذا السطر
    /// </summary>
    public decimal DiscountPercentage { get; set; }

    /// <summary>
    /// Item identifier
    /// معرف الصنف
    /// </summary>
    public int ItemId { get; set; }
}
