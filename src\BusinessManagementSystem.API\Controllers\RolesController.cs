using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using BusinessManagementSystem.Application.DTOs.Role;
using BusinessManagementSystem.Application.Services.Interfaces;
using Microsoft.Extensions.Logging;


namespace BusinessManagementSystem.API.Controllers;

/// <summary>
/// Roles controller for role-based access control management
/// تحكم الأدوار لإدارة التحكم في الوصول القائم على الأدوار
/// </summary>
[ApiController]
[Route("api/[controller]")]
[Authorize]
public class RolesController : BaseApiController
{
    private readonly IRoleService _roleService;

    public RolesController(IRoleService roleService, ILogger<RolesController> logger) : base(logger)
    {
        _roleService = roleService;
    }

    /// <summary>
    /// Get all roles
    /// الحصول على جميع الأدوار
    /// </summary>
    [HttpGet]
    [Authorize(Roles = "Administrator")]
    public async Task<ActionResult<IEnumerable<RoleDto>>> GetAll(CancellationToken cancellationToken = default)
    {
        try
        {
            var roles = await _roleService.GetAllAsync(cancellationToken);
            return Ok(roles);
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error occurred while getting all roles");
            return StatusCode(500, "An error occurred while processing your request.");
        }
    }

    /// <summary>
    /// Get role by ID
    /// الحصول على الدور بواسطة المعرف
    /// </summary>
    [HttpGet("{id}")]
    [Authorize(Roles = "Administrator")]
    public async Task<ActionResult<RoleDto>> GetById(int id, CancellationToken cancellationToken = default)
    {
        try
        {
            var role = await _roleService.GetByIdAsync(id, cancellationToken);
            if (role == null)
            {
                return NotFound($"Role with ID {id} not found.");
            }

            return Ok(role);
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error occurred while getting role with ID: {RoleId}", id);
            return StatusCode(500, "An error occurred while processing your request.");
        }
    }

    /// <summary>
    /// Get role by name
    /// الحصول على الدور بواسطة الاسم
    /// </summary>
    [HttpGet("name/{name}")]
    [Authorize(Roles = "Administrator")]
    public async Task<ActionResult<RoleDto>> GetByName(string name, CancellationToken cancellationToken = default)
    {
        try
        {
            var role = await _roleService.GetByNameAsync(name, cancellationToken);
            if (role == null)
            {
                return NotFound($"Role with name '{name}' not found.");
            }

            return Ok(role);
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error occurred while getting role with name: {RoleName}", name);
            return StatusCode(500, "An error occurred while processing your request.");
        }
    }

    /// <summary>
    /// Get active roles
    /// الحصول على الأدوار النشطة
    /// </summary>
    [HttpGet("active")]
    [Authorize(Roles = "Administrator,Manager")]
    public async Task<ActionResult<IEnumerable<RoleDto>>> GetActiveRoles(CancellationToken cancellationToken = default)
    {
        try
        {
            var roles = await _roleService.GetActiveRolesAsync(cancellationToken);
            return Ok(roles);
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error occurred while getting active roles");
            return StatusCode(500, "An error occurred while processing your request.");
        }
    }

    /// <summary>
    /// Get system roles
    /// الحصول على أدوار النظام
    /// </summary>
    [HttpGet("system")]
    [Authorize(Roles = "Administrator")]
    public async Task<ActionResult<IEnumerable<RoleDto>>> GetSystemRoles(CancellationToken cancellationToken = default)
    {
        try
        {
            var roles = await _roleService.GetSystemRolesAsync(cancellationToken);
            return Ok(roles);
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error occurred while getting system roles");
            return StatusCode(500, "An error occurred while processing your request.");
        }
    }

    /// <summary>
    /// Get custom roles (non-system)
    /// الحصول على الأدوار المخصصة (غير النظام)
    /// </summary>
    [HttpGet("custom")]
    [Authorize(Roles = "Administrator")]
    public async Task<ActionResult<IEnumerable<RoleDto>>> GetCustomRoles(CancellationToken cancellationToken = default)
    {
        try
        {
            var roles = await _roleService.GetCustomRolesAsync(cancellationToken);
            return Ok(roles);
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error occurred while getting custom roles");
            return StatusCode(500, "An error occurred while processing your request.");
        }
    }

    /// <summary>
    /// Search roles by name or description
    /// البحث في الأدوار بواسطة الاسم أو الوصف
    /// </summary>
    [HttpGet("search")]
    [Authorize(Roles = "Administrator")]
    public async Task<ActionResult<IEnumerable<RoleDto>>> Search([FromQuery] string searchTerm, CancellationToken cancellationToken = default)
    {
        try
        {
            if (string.IsNullOrWhiteSpace(searchTerm))
            {
                return BadRequest("Search term is required.");
            }

            var roles = await _roleService.SearchAsync(searchTerm, cancellationToken);
            return Ok(roles);
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error occurred while searching roles with term: {SearchTerm}", searchTerm);
            return StatusCode(500, "An error occurred while processing your request.");
        }
    }

    /// <summary>
    /// Create new role
    /// إنشاء دور جديد
    /// </summary>
    [HttpPost]
    [Authorize(Roles = "Administrator")]
    public async Task<ActionResult<RoleDto>> Create([FromBody] CreateRoleDto createRoleDto, CancellationToken cancellationToken = default)
    {
        try
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ModelState);
            }

            var role = await _roleService.CreateAsync(createRoleDto, cancellationToken);
            
            Logger.LogInformation("Role created successfully with ID: {RoleId} by user: {UserId}", role.Id, GetCurrentUserId());
            return CreatedAtAction(nameof(GetById), new { id = role.Id }, role);
        }
        catch (ArgumentException ex)
        {
            Logger.LogWarning(ex, "Invalid argument while creating role");
            return BadRequest(ex.Message);
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error occurred while creating role");
            return StatusCode(500, "An error occurred while processing your request.");
        }
    }

    /// <summary>
    /// Update existing role
    /// تحديث دور موجود
    /// </summary>
    [HttpPut("{id}")]
    [Authorize(Roles = "Administrator")]
    public async Task<ActionResult<RoleDto>> Update(int id, [FromBody] UpdateRoleDto updateRoleDto, CancellationToken cancellationToken = default)
    {
        try
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ModelState);
            }

            var role = await _roleService.UpdateAsync(id, updateRoleDto, cancellationToken);
            
            Logger.LogInformation("Role updated successfully with ID: {RoleId} by user: {UserId}", id, GetCurrentUserId());
            return Ok(role);
        }
        catch (ArgumentException ex)
        {
            Logger.LogWarning(ex, "Invalid argument while updating role with ID: {RoleId}", id);
            return BadRequest(ex.Message);
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error occurred while updating role with ID: {RoleId}", id);
            return StatusCode(500, "An error occurred while processing your request.");
        }
    }

    /// <summary>
    /// Set role active status
    /// تعيين حالة تفعيل الدور
    /// </summary>
    [HttpPatch("{id}/status")]
    [Authorize(Roles = "Administrator")]
    public async Task<ActionResult> SetActiveStatus(int id, [FromBody] bool isActive, CancellationToken cancellationToken = default)
    {
        try
        {
            var result = await _roleService.SetActiveStatusAsync(id, isActive, cancellationToken);
            if (!result)
            {
                return NotFound($"Role with ID {id} not found.");
            }

            Logger.LogInformation("Role status changed to {Status} for ID: {RoleId} by user: {UserId}", 
                isActive ? "Active" : "Inactive", id, GetCurrentUserId());
            return NoContent();
        }
        catch (InvalidOperationException ex)
        {
            Logger.LogWarning(ex, "Invalid operation while changing role status for ID: {RoleId}", id);
            return BadRequest(ex.Message);
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error occurred while changing role status for ID: {RoleId}", id);
            return StatusCode(500, "An error occurred while processing your request.");
        }
    }

    /// <summary>
    /// Delete role
    /// حذف الدور
    /// </summary>
    [HttpDelete("{id}")]
    [Authorize(Roles = "Administrator")]
    public async Task<ActionResult> Delete(int id, CancellationToken cancellationToken = default)
    {
        try
        {
            var result = await _roleService.DeleteAsync(id, cancellationToken);
            if (!result)
            {
                return NotFound($"Role with ID {id} not found.");
            }

            Logger.LogInformation("Role deleted successfully with ID: {RoleId} by user: {UserId}", id, GetCurrentUserId());
            return NoContent();
        }
        catch (InvalidOperationException ex)
        {
            Logger.LogWarning(ex, "Invalid operation while deleting role with ID: {RoleId}", id);
            return BadRequest(ex.Message);
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error occurred while deleting role with ID: {RoleId}", id);
            return StatusCode(500, "An error occurred while processing your request.");
        }
    }

    /// <summary>
    /// Get role statistics
    /// الحصول على إحصائيات الدور
    /// </summary>
    [HttpGet("{id}/statistics")]
    [Authorize(Roles = "Administrator")]
    public async Task<ActionResult<RoleStatisticsDto>> GetStatistics(int id, CancellationToken cancellationToken = default)
    {
        try
        {
            var statistics = await _roleService.GetRoleStatisticsAsync(id, cancellationToken);
            return Ok(statistics);
        }
        catch (ArgumentException ex)
        {
            Logger.LogWarning(ex, "Invalid argument while getting role statistics for ID: {RoleId}", id);
            return BadRequest(ex.Message);
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error occurred while getting role statistics for ID: {RoleId}", id);
            return StatusCode(500, "An error occurred while processing your request.");
        }
    }

    /// <summary>
    /// Get users assigned to role
    /// الحصول على المستخدمين المعينين للدور
    /// </summary>
    [HttpGet("{id}/users")]
    [Authorize(Roles = "Administrator")]
    public async Task<ActionResult<IEnumerable<object>>> GetRoleUsers(int id, CancellationToken cancellationToken = default)
    {
        try
        {
            var users = await _roleService.GetRoleUsersAsync(id, cancellationToken);
            return Ok(users);
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error occurred while getting users for role ID: {RoleId}", id);
            return StatusCode(500, "An error occurred while processing your request.");
        }
    }

    /// <summary>
    /// Assign role to user
    /// تعيين دور للمستخدم
    /// </summary>
    [HttpPost("{roleId}/assign-user/{userId}")]
    [Authorize(Roles = "Administrator")]
    public async Task<ActionResult> AssignRoleToUser(int roleId, int userId, CancellationToken cancellationToken = default)
    {
        try
        {
            var result = await _roleService.AssignRoleToUserAsync(roleId, userId, cancellationToken);
            if (!result)
            {
                return BadRequest("Failed to assign role to user. Please check if role and user exist.");
            }

            Logger.LogInformation("Role {RoleId} assigned to user {UserId} by user: {CurrentUserId}", roleId, userId, GetCurrentUserId());
            return Ok(new { Message = "Role assigned successfully." });
        }
        catch (InvalidOperationException ex)
        {
            Logger.LogWarning(ex, "Invalid operation while assigning role {RoleId} to user {UserId}", roleId, userId);
            return BadRequest(ex.Message);
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error occurred while assigning role {RoleId} to user {UserId}", roleId, userId);
            return StatusCode(500, "An error occurred while processing your request.");
        }
    }

    /// <summary>
    /// Remove role from user
    /// إزالة دور من المستخدم
    /// </summary>
    [HttpDelete("{roleId}/remove-user/{userId}")]
    [Authorize(Roles = "Administrator")]
    public async Task<ActionResult> RemoveRoleFromUser(int roleId, int userId, CancellationToken cancellationToken = default)
    {
        try
        {
            var result = await _roleService.RemoveRoleFromUserAsync(roleId, userId, cancellationToken);
            if (!result)
            {
                return BadRequest("Failed to remove role from user. Please check if the user has this role assigned.");
            }

            Logger.LogInformation("Role {RoleId} removed from user {UserId} by user: {CurrentUserId}", roleId, userId, GetCurrentUserId());
            return Ok(new { Message = "Role removed successfully." });
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error occurred while removing role {RoleId} from user {UserId}", roleId, userId);
            return StatusCode(500, "An error occurred while processing your request.");
        }
    }

    /// <summary>
    /// Bulk update role permissions
    /// تحديث صلاحيات الدور بالجملة
    /// </summary>
    [HttpPut("{id}/permissions")]
    [Authorize(Roles = "Administrator")]
    public async Task<ActionResult> BulkUpdatePermissions(int id, [FromBody] BulkRolePermissionUpdateDto bulkUpdateDto, CancellationToken cancellationToken = default)
    {
        try
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ModelState);
            }

            // Ensure the role ID matches
            bulkUpdateDto.RoleId = id;

            var result = await _roleService.BulkUpdatePermissionsAsync(bulkUpdateDto, cancellationToken);
            if (!result)
            {
                return BadRequest("Failed to update permissions.");
            }

            Logger.LogInformation("Bulk permissions updated for role {RoleId} by user: {UserId}", id, GetCurrentUserId());
            return Ok(new { Message = "Permissions updated successfully." });
        }
        catch (ArgumentException ex)
        {
            Logger.LogWarning(ex, "Invalid argument while updating permissions for role {RoleId}", id);
            return BadRequest(ex.Message);
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error occurred while updating permissions for role {RoleId}", id);
            return StatusCode(500, "An error occurred while processing your request.");
        }
    }

    /// <summary>
    /// Clone role with permissions
    /// استنساخ الدور مع الصلاحيات
    /// </summary>
    [HttpPost("{id}/clone")]
    [Authorize(Roles = "Administrator")]
    public async Task<ActionResult<RoleDto>> CloneRole(int id, [FromBody] CloneRoleRequestDto request, CancellationToken cancellationToken = default)
    {
        try
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ModelState);
            }

            var clonedRole = await _roleService.CloneRoleAsync(id, request.NewRoleName, request.Description, cancellationToken);
            
            Logger.LogInformation("Role cloned successfully from {SourceRoleId} to {NewRoleId} by user: {UserId}", id, clonedRole.Id, GetCurrentUserId());
            return CreatedAtAction(nameof(GetById), new { id = clonedRole.Id }, clonedRole);
        }
        catch (ArgumentException ex)
        {
            Logger.LogWarning(ex, "Invalid argument while cloning role {RoleId}", id);
            return BadRequest(ex.Message);
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error occurred while cloning role {RoleId}", id);
            return StatusCode(500, "An error occurred while processing your request.");
        }
    }

    /// <summary>
    /// Get role permissions summary
    /// الحصول على ملخص صلاحيات الدور
    /// </summary>
    [HttpGet("{id}/permissions")]
    [Authorize(Roles = "Administrator")]
    public async Task<ActionResult<IEnumerable<RolePermissionSummaryDto>>> GetRolePermissions(int id, CancellationToken cancellationToken = default)
    {
        try
        {
            var permissions = await _roleService.GetRolePermissionsAsync(id, cancellationToken);
            return Ok(permissions);
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error occurred while getting permissions for role {RoleId}", id);
            return StatusCode(500, "An error occurred while processing your request.");
        }
    }

    /// <summary>
    /// Check if role can be deleted
    /// التحقق من إمكانية حذف الدور
    /// </summary>
    [HttpGet("{id}/can-delete")]
    [Authorize(Roles = "Administrator")]
    public async Task<ActionResult<bool>> CanDeleteRole(int id, CancellationToken cancellationToken = default)
    {
        try
        {
            var canDelete = await _roleService.CanDeleteRoleAsync(id, cancellationToken);
            return Ok(canDelete);
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error occurred while checking if role {RoleId} can be deleted", id);
            return StatusCode(500, "An error occurred while processing your request.");
        }
    }

    /// <summary>
    /// Get role hierarchy
    /// الحصول على تسلسل الأدوار
    /// </summary>
    [HttpGet("hierarchy")]
    [Authorize(Roles = "Administrator")]
    public async Task<ActionResult<object>> GetRoleHierarchy(CancellationToken cancellationToken = default)
    {
        try
        {
            var hierarchy = await _roleService.GetRoleHierarchyAsync(cancellationToken);
            return Ok(hierarchy);
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error occurred while getting role hierarchy");
            return StatusCode(500, "An error occurred while processing your request.");
        }
    }

    /// <summary>
    /// Export roles to CSV format
    /// تصدير الأدوار إلى تنسيق CSV
    /// </summary>
    [HttpGet("export")]
    [Authorize(Roles = "Administrator")]
    public async Task<ActionResult> ExportRoles([FromQuery] string format = "csv", CancellationToken cancellationToken = default)
    {
        try
        {
            var data = await _roleService.ExportRolesAsync(format, cancellationToken);
            var fileName = $"roles_{DateTime.UtcNow:yyyyMMddHHmmss}.{format}";
            var contentType = format.ToLower() == "csv" ? "text/csv" : "application/octet-stream";
            
            return File(data, contentType, fileName);
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error occurred while exporting roles");
            return StatusCode(500, "An error occurred while processing your request.");
        }
    }

    /// <summary>
    /// Get role audit trail
    /// الحصول على مسار تدقيق الدور
    /// </summary>
    [HttpGet("{id}/audit-trail")]
    [Authorize(Roles = "Administrator")]
    public async Task<ActionResult<IEnumerable<object>>> GetRoleAuditTrail(int id, CancellationToken cancellationToken = default)
    {
        try
        {
            var auditTrail = await _roleService.GetRoleAuditTrailAsync(id, cancellationToken);
            return Ok(auditTrail);
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error occurred while getting audit trail for role {RoleId}", id);
            return StatusCode(500, "An error occurred while processing your request.");
        }
    }
}

// Request DTOs for specific operations
public class CloneRoleRequestDto
{
    public string NewRoleName { get; set; } = string.Empty;
    public string? Description { get; set; }
}
