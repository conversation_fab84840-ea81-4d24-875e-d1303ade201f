using AutoMapper;
using Microsoft.Extensions.Logging;
using BusinessManagementSystem.Application.DTOs.Role;
using BusinessManagementSystem.Application.Interfaces;
using BusinessManagementSystem.Application.Services.Interfaces;
using BusinessManagementSystem.Domain.Entities;

namespace BusinessManagementSystem.Application.Services.Implementations;

/// <summary>
/// Role service implementation for business logic operations
/// تنفيذ خدمة الدور لعمليات منطق الأعمال
/// </summary>
public class RoleService : IRoleService
{
    private readonly IUnitOfWork _unitOfWork;
    private readonly IMapper _mapper;
    private readonly ILogger<RoleService> _logger;

    public RoleService(IUnitOfWork unitOfWork, IMapper mapper, ILogger<RoleService> logger)
    {
        _unitOfWork = unitOfWork;
        _mapper = mapper;
        _logger = logger;
    }

    public async Task<IEnumerable<RoleDto>> GetAllAsync(CancellationToken cancellationToken = default)
    {
        var roles = await _unitOfWork.Roles.GetAsync(
            includeProperties: "RolePermissions",
            orderBy: q => q.OrderBy(r => r.Name),
            cancellationToken: cancellationToken);
        return _mapper.Map<IEnumerable<RoleDto>>(roles);
    }

    public async Task<RoleDto?> GetByIdAsync(int id, CancellationToken cancellationToken = default)
    {
        var role = await _unitOfWork.Roles.GetByIdAsync(id, cancellationToken);
        return role != null ? _mapper.Map<RoleDto>(role) : null;
    }

    public async Task<RoleDto?> GetByNameAsync(string name, CancellationToken cancellationToken = default)
    {
        var roles = await _unitOfWork.Roles.GetAsync(
            filter: r => r.Name == name,
            includeProperties: "RolePermissions",
            cancellationToken: cancellationToken);
        var role = roles.FirstOrDefault();
        return role != null ? _mapper.Map<RoleDto>(role) : null;
    }

    public async Task<IEnumerable<RoleDto>> GetActiveRolesAsync(CancellationToken cancellationToken = default)
    {
        var roles = await _unitOfWork.Roles.GetAsync(
            filter: r => r.IsActive,
            includeProperties: "RolePermissions",
            orderBy: q => q.OrderBy(r => r.Name),
            cancellationToken: cancellationToken);
        return _mapper.Map<IEnumerable<RoleDto>>(roles);
    }

    public async Task<IEnumerable<RoleDto>> GetSystemRolesAsync(CancellationToken cancellationToken = default)
    {
        var roles = await _unitOfWork.Roles.GetAsync(
            filter: r => r.IsSystemRole,
            includeProperties: "RolePermissions",
            orderBy: q => q.OrderBy(r => r.Name),
            cancellationToken: cancellationToken);
        return _mapper.Map<IEnumerable<RoleDto>>(roles);
    }

    public async Task<IEnumerable<RoleDto>> GetCustomRolesAsync(CancellationToken cancellationToken = default)
    {
        var roles = await _unitOfWork.Roles.GetAsync(
            filter: r => !r.IsSystemRole,
            includeProperties: "RolePermissions",
            orderBy: q => q.OrderBy(r => r.Name),
            cancellationToken: cancellationToken);
        return _mapper.Map<IEnumerable<RoleDto>>(roles);
    }

    public async Task<IEnumerable<RoleDto>> SearchAsync(string searchTerm, CancellationToken cancellationToken = default)
    {
        var roles = await _unitOfWork.Roles.GetAsync(
            filter: r => r.Name.Contains(searchTerm) || 
                        (r.Description != null && r.Description.Contains(searchTerm)),
            includeProperties: "RolePermissions",
            orderBy: q => q.OrderBy(r => r.Name),
            cancellationToken: cancellationToken);
        return _mapper.Map<IEnumerable<RoleDto>>(roles);
    }

    public async Task<RoleDto> CreateAsync(CreateRoleDto createRoleDto, CancellationToken cancellationToken = default)
    {
        // Validate business rules
        await ValidateRoleForCreateAsync(createRoleDto, cancellationToken);

        await _unitOfWork.BeginTransactionAsync(cancellationToken);
        try
        {
            var role = _mapper.Map<Role>(createRoleDto);
            role.IsSystemRole = false; // Custom roles are never system roles

            await _unitOfWork.Roles.AddAsync(role, cancellationToken);
            await _unitOfWork.SaveChangesAsync(cancellationToken);

            // Create initial permissions if provided
            if (createRoleDto.Permissions.Any())
            {
                foreach (var permissionDto in createRoleDto.Permissions)
                {
                    var permission = new RolePermission
                    {
                        RoleId = role.Id,
                        PageName = permissionDto.PageName,
                        CanAdd = permissionDto.CanAdd,
                        CanEdit = permissionDto.CanEdit,
                        CanDelete = permissionDto.CanDelete,
                        CanView = permissionDto.CanView
                    };
                    await _unitOfWork.RolePermissions.AddAsync(permission, cancellationToken);
                }
                await _unitOfWork.SaveChangesAsync(cancellationToken);
            }

            await _unitOfWork.CommitTransactionAsync(cancellationToken);

            _logger.LogInformation("Role created successfully with ID: {RoleId}", role.Id);
            return _mapper.Map<RoleDto>(role);
        }
        catch
        {
            await _unitOfWork.RollbackTransactionAsync(cancellationToken);
            throw;
        }
    }

    public async Task<RoleDto> UpdateAsync(int id, UpdateRoleDto updateRoleDto, CancellationToken cancellationToken = default)
    {
        var existingRole = await _unitOfWork.Roles.GetByIdAsync(id, cancellationToken);
        if (existingRole == null)
        {
            throw new ArgumentException($"Role with ID {id} not found.", nameof(id));
        }

        // Validate business rules
        await ValidateRoleForUpdateAsync(id, updateRoleDto, cancellationToken);

        _mapper.Map(updateRoleDto, existingRole);
        _unitOfWork.Roles.Update(existingRole);
        await _unitOfWork.SaveChangesAsync(cancellationToken);

        _logger.LogInformation("Role updated successfully with ID: {RoleId}", id);
        return _mapper.Map<RoleDto>(existingRole);
    }

    public async Task<bool> SetActiveStatusAsync(int id, bool isActive, CancellationToken cancellationToken = default)
    {
        var role = await _unitOfWork.Roles.GetByIdAsync(id, cancellationToken);
        if (role == null)
        {
            return false;
        }

        // Cannot deactivate system roles
        if (!isActive && role.IsSystemRole)
        {
            throw new InvalidOperationException("Cannot deactivate system roles.");
        }

        role.IsActive = isActive;
        _unitOfWork.Roles.Update(role);
        await _unitOfWork.SaveChangesAsync(cancellationToken);

        _logger.LogInformation("Role status changed to {Status} for ID: {RoleId}", 
            isActive ? "Active" : "Inactive", id);
        return true;
    }

    public async Task<bool> DeleteAsync(int id, CancellationToken cancellationToken = default)
    {
        var role = await _unitOfWork.Roles.GetByIdAsync(id, cancellationToken);
        if (role == null)
        {
            return false;
        }

        // Validate if role can be deleted
        if (!await CanDeleteRoleAsync(id, cancellationToken))
        {
            throw new InvalidOperationException("Role cannot be deleted. It may be a system role or have assigned users.");
        }

        await _unitOfWork.BeginTransactionAsync(cancellationToken);
        try
        {
            // Delete role permissions first
            var permissions = await _unitOfWork.RolePermissions.GetAsync(
                filter: p => p.RoleId == id, cancellationToken: cancellationToken);
            foreach (var permission in permissions)
            {
                _unitOfWork.RolePermissions.Delete(permission);
            }

            // Delete the role
            _unitOfWork.Roles.Delete(role);
            await _unitOfWork.SaveChangesAsync(cancellationToken);
            await _unitOfWork.CommitTransactionAsync(cancellationToken);

            _logger.LogInformation("Role deleted successfully with ID: {RoleId}", id);
            return true;
        }
        catch
        {
            await _unitOfWork.RollbackTransactionAsync(cancellationToken);
            throw;
        }
    }

    public async Task<bool> IsRoleNameExistsAsync(string name, int? excludeId = null, CancellationToken cancellationToken = default)
    {
        var roles = await _unitOfWork.Roles.GetAsync(
            filter: r => r.Name == name && (excludeId == null || r.Id != excludeId),
            cancellationToken: cancellationToken);
        return roles.Any();
    }

    public async Task<RoleStatisticsDto> GetRoleStatisticsAsync(int id, CancellationToken cancellationToken = default)
    {
        var role = await GetByIdAsync(id, cancellationToken);
        if (role == null)
        {
            throw new ArgumentException($"Role with ID {id} not found.");
        }

        // Get users assigned to this role
        var users = await _unitOfWork.Users.GetAsync(
            filter: u => u.RoleId == id,
            includeProperties: "Company",
            cancellationToken: cancellationToken);

        var activeUsers = users.Count(u => u.IsActive);
        var inactiveUsers = users.Count(u => !u.IsActive);

        var usersByCompany = users.GroupBy(u => new { u.CompanyId, u.Company.Name })
            .Select(g => new UsersByCompanyDto
            {
                CompanyId = g.Key.CompanyId,
                CompanyName = g.Key.Name,
                UsersCount = g.Count(),
                ActiveUsersCount = g.Count(u => u.IsActive)
            }).ToList();

        var recentActivities = users.Where(u => u.LastLoginDate.HasValue)
            .OrderByDescending(u => u.LastLoginDate)
            .Take(10)
            .Select(u => new RecentUserActivityDto
            {
                UserId = u.Id,
                UserName = u.UserName,
                FullName = u.FullName,
                LastLoginDate = u.LastLoginDate,
                CompanyName = u.Company.Name,
                IsActive = u.IsActive
            }).ToList();

        return new RoleStatisticsDto
        {
            Role = role,
            ActiveUsersCount = activeUsers,
            InactiveUsersCount = inactiveUsers,
            UsersByCompany = usersByCompany,
            RecentActivities = recentActivities
        };
    }

    public async Task<IEnumerable<object>> GetRoleUsersAsync(int id, CancellationToken cancellationToken = default)
    {
        var users = await _unitOfWork.Users.GetAsync(
            filter: u => u.RoleId == id,
            includeProperties: "Company",
            orderBy: q => q.OrderBy(u => u.FullName),
            cancellationToken: cancellationToken);

        return users.Select(u => new
        {
            u.Id,
            u.UserName,
            u.FullName,
            u.Email,
            u.IsActive,
            CompanyName = u.Company.Name,
            u.LastLoginDate,
            u.CreatedAt
        });
    }

    public async Task<bool> AssignRoleToUserAsync(int roleId, int userId, CancellationToken cancellationToken = default)
    {
        var role = await _unitOfWork.Roles.GetByIdAsync(roleId, cancellationToken);
        var user = await _unitOfWork.Users.GetByIdAsync(userId, cancellationToken);

        if (role == null || user == null)
        {
            return false;
        }

        if (!role.IsActive)
        {
            throw new InvalidOperationException("Cannot assign inactive role to user.");
        }

        user.RoleId = roleId;
        _unitOfWork.Users.Update(user);
        await _unitOfWork.SaveChangesAsync(cancellationToken);

        _logger.LogInformation("Role {RoleId} assigned to user {UserId}", roleId, userId);
        return true;
    }

    public async Task<bool> RemoveRoleFromUserAsync(int roleId, int userId, CancellationToken cancellationToken = default)
    {
        var user = await _unitOfWork.Users.GetByIdAsync(userId, cancellationToken);
        if (user == null || user.RoleId != roleId)
        {
            return false;
        }

        user.RoleId = null;
        _unitOfWork.Users.Update(user);
        await _unitOfWork.SaveChangesAsync(cancellationToken);

        _logger.LogInformation("Role {RoleId} removed from user {UserId}", roleId, userId);
        return true;
    }

    public async Task<bool> BulkUpdatePermissionsAsync(BulkRolePermissionUpdateDto bulkUpdateDto, CancellationToken cancellationToken = default)
    {
        var role = await _unitOfWork.Roles.GetByIdAsync(bulkUpdateDto.RoleId, cancellationToken);
        if (role == null)
        {
            throw new ArgumentException("Role not found.");
        }

        await _unitOfWork.BeginTransactionAsync(cancellationToken);
        try
        {
            if (bulkUpdateDto.ReplaceExisting)
            {
                // Delete existing permissions
                var existingPermissions = await _unitOfWork.RolePermissions.GetAsync(
                    filter: p => p.RoleId == bulkUpdateDto.RoleId, cancellationToken: cancellationToken);
                foreach (var permission in existingPermissions)
                {
                    _unitOfWork.RolePermissions.Delete(permission);
                }
            }

            // Add new permissions
            foreach (var permissionDto in bulkUpdateDto.Permissions)
            {
                var permission = new RolePermission
                {
                    RoleId = bulkUpdateDto.RoleId,
                    PageName = permissionDto.PageName,
                    CanAdd = permissionDto.CanAdd,
                    CanEdit = permissionDto.CanEdit,
                    CanDelete = permissionDto.CanDelete,
                    CanView = permissionDto.CanView
                };
                await _unitOfWork.RolePermissions.AddAsync(permission, cancellationToken);
            }

            await _unitOfWork.SaveChangesAsync(cancellationToken);
            await _unitOfWork.CommitTransactionAsync(cancellationToken);

            _logger.LogInformation("Bulk permissions updated for role {RoleId}", bulkUpdateDto.RoleId);
            return true;
        }
        catch
        {
            await _unitOfWork.RollbackTransactionAsync(cancellationToken);
            throw;
        }
    }

    public async Task<RoleDto> CloneRoleAsync(int sourceRoleId, string newRoleName, string? description = null, CancellationToken cancellationToken = default)
    {
        var sourceRole = await _unitOfWork.Roles.GetByIdAsync(sourceRoleId, cancellationToken);
        if (sourceRole == null)
        {
            throw new ArgumentException("Source role not found.");
        }

        // Check if new role name already exists
        if (await IsRoleNameExistsAsync(newRoleName, cancellationToken: cancellationToken))
        {
            throw new ArgumentException("Role name already exists.");
        }

        await _unitOfWork.BeginTransactionAsync(cancellationToken);
        try
        {
            // Create new role
            var newRole = new Role
            {
                Name = newRoleName,
                Description = description ?? $"Cloned from {sourceRole.Name}",
                IsSystemRole = false,
                IsActive = true
            };

            await _unitOfWork.Roles.AddAsync(newRole, cancellationToken);
            await _unitOfWork.SaveChangesAsync(cancellationToken);

            // Copy permissions
            var sourcePermissions = await _unitOfWork.RolePermissions.GetAsync(
                filter: p => p.RoleId == sourceRoleId, cancellationToken: cancellationToken);

            foreach (var sourcePermission in sourcePermissions)
            {
                var newPermission = new RolePermission
                {
                    RoleId = newRole.Id,
                    PageName = sourcePermission.PageName,
                    CanAdd = sourcePermission.CanAdd,
                    CanEdit = sourcePermission.CanEdit,
                    CanDelete = sourcePermission.CanDelete,
                    CanView = sourcePermission.CanView
                };
                await _unitOfWork.RolePermissions.AddAsync(newPermission, cancellationToken);
            }

            await _unitOfWork.SaveChangesAsync(cancellationToken);
            await _unitOfWork.CommitTransactionAsync(cancellationToken);

            _logger.LogInformation("Role cloned successfully from {SourceRoleId} to {NewRoleId}", sourceRoleId, newRole.Id);
            return _mapper.Map<RoleDto>(newRole);
        }
        catch
        {
            await _unitOfWork.RollbackTransactionAsync(cancellationToken);
            throw;
        }
    }

    public async Task<IEnumerable<RolePermissionSummaryDto>> GetRolePermissionsAsync(int id, CancellationToken cancellationToken = default)
    {
        var permissions = await _unitOfWork.RolePermissions.GetAsync(
            filter: p => p.RoleId == id,
            orderBy: q => q.OrderBy(p => p.PageName),
            cancellationToken: cancellationToken);

        return permissions.Select(p => new RolePermissionSummaryDto
        {
            PageName = p.PageName,
            CanAdd = p.CanAdd,
            CanEdit = p.CanEdit,
            CanDelete = p.CanDelete,
            CanView = p.CanView
        });
    }

    public async Task<bool> CanDeleteRoleAsync(int id, CancellationToken cancellationToken = default)
    {
        var role = await _unitOfWork.Roles.GetByIdAsync(id, cancellationToken);
        if (role == null)
        {
            return false;
        }

        // Cannot delete system roles
        if (role.IsSystemRole)
        {
            return false;
        }

        // Check if role has assigned users
        var users = await _unitOfWork.Users.GetAsync(
            filter: u => u.RoleId == id, cancellationToken: cancellationToken);
        if (users.Any())
        {
            return false;
        }

        return true;
    }

    public async Task<object> GetRoleHierarchyAsync(CancellationToken cancellationToken = default)
    {
        var roles = await GetAllAsync(cancellationToken);
        
        return new
        {
            SystemRoles = roles.Where(r => r.IsSystemRole).OrderBy(r => r.Name),
            CustomRoles = roles.Where(r => !r.IsSystemRole).OrderBy(r => r.Name),
            TotalRoles = roles.Count(),
            ActiveRoles = roles.Count(r => r.IsActive),
            InactiveRoles = roles.Count(r => !r.IsActive)
        };
    }

    public async Task<byte[]> ExportRolesAsync(string format = "csv", CancellationToken cancellationToken = default)
    {
        var roles = await GetAllAsync(cancellationToken);
        
        // This is a simplified implementation - in a real scenario, you'd use a proper CSV/Excel library
        var csv = "Id,Name,Description,IsSystemRole,IsActive,UsersCount,PermissionsCount\n";
        foreach (var role in roles)
        {
            csv += $"{role.Id},{role.Name},{role.Description},{role.IsSystemRole},{role.IsActive},{role.UsersCount},{role.PermissionsCount}\n";
        }
        
        return System.Text.Encoding.UTF8.GetBytes(csv);
    }

    public async Task<IEnumerable<object>> GetRoleAuditTrailAsync(int id, CancellationToken cancellationToken = default)
    {
        // This would typically query an audit log table
        // For now, return basic information
        var role = await GetByIdAsync(id, cancellationToken);
        if (role == null)
        {
            return new List<object>();
        }

        return new List<object>
        {
            new
            {
                Action = "Created",
                Date = role.CreatedAt,
                User = role.CreatedBy,
                Details = $"Role '{role.Name}' was created"
            },
            new
            {
                Action = "Updated",
                Date = role.UpdatedAt,
                User = role.UpdatedBy,
                Details = $"Role '{role.Name}' was last updated"
            }
        };
    }

    private async Task ValidateRoleForCreateAsync(CreateRoleDto createRoleDto, CancellationToken cancellationToken)
    {
        if (await IsRoleNameExistsAsync(createRoleDto.Name, cancellationToken: cancellationToken))
        {
            throw new ArgumentException("Role name already exists.");
        }
    }

    private async Task ValidateRoleForUpdateAsync(int id, UpdateRoleDto updateRoleDto, CancellationToken cancellationToken)
    {
        if (await IsRoleNameExistsAsync(updateRoleDto.Name, id, cancellationToken))
        {
            throw new ArgumentException("Role name already exists.");
        }
    }
}
