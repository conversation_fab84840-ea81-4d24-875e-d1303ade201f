using System.ComponentModel.DataAnnotations;
using BusinessManagementSystem.Domain.Common;

namespace BusinessManagementSystem.Domain.Entities;

/// <summary>
/// Role entity - stores user roles
/// كيان الدور - يخزن أدوار المستخدمين
/// </summary>
public class Role : BaseEntity
{
    /// <summary>
    /// Role name
    /// اسم الدور
    /// </summary>
    [Required]
    [MaxLength(100)]
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// Role description
    /// وصف الدور
    /// </summary>
    [MaxLength(500)]
    public string? Description { get; set; }

    /// <summary>
    /// Whether this is a system-defined role
    /// ما إذا كان هذا دور معرف من النظام
    /// </summary>
    public bool IsSystemRole { get; set; } = false;

    /// <summary>
    /// Whether the role is active
    /// ما إذا كان الدور نشطًا
    /// </summary>
    public bool IsActive { get; set; } = true;

    // Navigation properties
    public virtual ICollection<User> Users { get; set; } = new List<User>();
    public virtual ICollection<RolePermission> RolePermissions { get; set; } = new List<RolePermission>();
}
