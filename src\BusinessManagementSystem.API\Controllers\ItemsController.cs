using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using BusinessManagementSystem.Application.DTOs.Item;
using BusinessManagementSystem.Application.Services.Interfaces;

namespace BusinessManagementSystem.API.Controllers;

/// <summary>
/// Items controller for item management operations
/// وحدة تحكم الأصناف لعمليات إدارة الأصناف
/// </summary>
[Authorize]
public class ItemsController : BaseApiController
{
    private readonly IItemService _itemService;
    

    public ItemsController(IItemService itemService, ILogger<ItemsController> logger) : base(logger)
    {
        _itemService = itemService;
        
    }

    /// <summary>
    /// Get all items with pagination and filtering
    /// الحصول على جميع الأصناف مع التصفح والتصفية
    /// </summary>
    [HttpGet]
    public async Task<IActionResult> GetAll(
        [FromQuery] int page = 1, 
        [FromQuery] int pageSize = 10,
        [FromQuery] string? search = null,
        [FromQuery] string? category = null,
        [FromQuery] bool? isActive = null,
        CancellationToken cancellationToken = default)
    {
        try
        {
            if (!HasPermission("Items", "View"))
            {
                return CreateErrorResponse("You don't have permission to view items.", 403);
            }

            var companyId = GetCurrentCompanyId();
            IEnumerable<ItemDto> items;

            if (!string.IsNullOrEmpty(search))
            {
                items = await _itemService.SearchAsync(companyId, search, cancellationToken);
            }
            else if (!string.IsNullOrEmpty(category))
            {
                items = await _itemService.GetByCategoryAsync(companyId, category, cancellationToken);
            }
            else
            {
                items = await _itemService.GetByCompanyAsync(companyId, cancellationToken);
            }

            // Apply active filter
            if (isActive.HasValue)
            {
                items = items.Where(i => i.IsActive == isActive.Value);
            }

            // Apply pagination
            var totalCount = items.Count();
            var pagedItems = items
                .Skip((page - 1) * pageSize)
                .Take(pageSize)
                .ToList();

            var result = new
            {
                Items = pagedItems,
                TotalCount = totalCount,
                Page = page,
                PageSize = pageSize,
                TotalPages = (int)Math.Ceiling((double)totalCount / pageSize)
            };

            return CreateResponse(result, "Items retrieved successfully.");
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error retrieving items");
            return CreateErrorResponse("An error occurred while retrieving items.", 500);
        }
    }

    /// <summary>
    /// Get item by ID
    /// الحصول على الصنف بواسطة المعرف
    /// </summary>
    [HttpGet("{id}")]
    public async Task<IActionResult> GetById(int id, CancellationToken cancellationToken = default)
    {
        try
        {
            if (!HasPermission("Items", "View"))
            {
                return CreateErrorResponse("You don't have permission to view items.", 403);
            }

            var item = await _itemService.GetByIdAsync(id, cancellationToken);
            if (item == null)
            {
                return CreateErrorResponse("Item not found.", 404);
            }

            // Check if user can access this item (same company)
            var companyId = GetCurrentCompanyId();
            if (item.CompanyId != companyId)
            {
                return CreateErrorResponse("You don't have permission to view this item.", 403);
            }

            return CreateResponse(item, "Item retrieved successfully.");
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error retrieving item with ID: {ItemId}", id);
            return CreateErrorResponse("An error occurred while retrieving the item.", 500);
        }
    }

    /// <summary>
    /// Get item by barcode
    /// الحصول على الصنف بواسطة الباركود
    /// </summary>
    [HttpGet("by-barcode/{barcode}")]
    public async Task<IActionResult> GetByBarcode(string barcode, CancellationToken cancellationToken = default)
    {
        try
        {
            if (!HasPermission("Items", "View"))
            {
                return CreateErrorResponse("You don't have permission to view items.", 403);
            }

            var item = await _itemService.GetByBarcodeAsync(barcode, cancellationToken);
            if (item == null)
            {
                return CreateErrorResponse("Item not found.", 404);
            }

            // Check if user can access this item (same company)
            var companyId = GetCurrentCompanyId();
            if (item.CompanyId != companyId)
            {
                return CreateErrorResponse("You don't have permission to view this item.", 403);
            }

            return CreateResponse(item, "Item retrieved successfully.");
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error retrieving item with barcode: {Barcode}", barcode);
            return CreateErrorResponse("An error occurred while retrieving the item.", 500);
        }
    }

    /// <summary>
    /// Get low stock items
    /// الحصول على الأصناف منخفضة المخزون
    /// </summary>
    [HttpGet("low-stock")]
    public async Task<IActionResult> GetLowStockItems(CancellationToken cancellationToken = default)
    {
        try
        {
            if (!HasPermission("Items", "View"))
            {
                return CreateErrorResponse("You don't have permission to view items.", 403);
            }

            var companyId = GetCurrentCompanyId();
            var items = await _itemService.GetLowStockItemsAsync(companyId, cancellationToken);

            return CreateResponse(items, "Low stock items retrieved successfully.");
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error retrieving low stock items");
            return CreateErrorResponse("An error occurred while retrieving low stock items.", 500);
        }
    }

    /// <summary>
    /// Create new item
    /// إنشاء صنف جديد
    /// </summary>
    [HttpPost]
    public async Task<IActionResult> Create([FromBody] CreateItemDto createItemDto, CancellationToken cancellationToken = default)
    {
        try
        {
            if (!HasPermission("Items", "Add"))
            {
                return CreateErrorResponse("You don't have permission to create items.", 403);
            }

            if (!ModelState.IsValid)
            {
                return CreateErrorResponse("Invalid item data.", 400);
            }

            // Ensure the item is created for the current user's company
            createItemDto.CompanyId = GetCurrentCompanyId();

            var item = await _itemService.CreateAsync(createItemDto, cancellationToken);
            
            Logger.LogInformation("Item created successfully with ID: {ItemId} by user: {UserId}", 
                item.Id, GetCurrentUserId());
            
            return CreatedAtAction(nameof(GetById), new { id = item.Id }, 
                CreateResponse(item, "Item created successfully."));
        }
        catch (ArgumentException ex)
        {
            Logger.LogWarning(ex, "Validation error creating item");
            return CreateErrorResponse(ex.Message, 400);
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error creating item");
            return CreateErrorResponse("An error occurred while creating the item.", 500);
        }
    }

    /// <summary>
    /// Update existing item
    /// تحديث صنف موجود
    /// </summary>
    [HttpPut("{id}")]
    public async Task<IActionResult> Update(int id, [FromBody] UpdateItemDto updateItemDto, CancellationToken cancellationToken = default)
    {
        try
        {
            if (!HasPermission("Items", "Edit"))
            {
                return CreateErrorResponse("You don't have permission to edit items.", 403);
            }

            if (!ModelState.IsValid)
            {
                return CreateErrorResponse("Invalid item data.", 400);
            }

            var item = await _itemService.UpdateAsync(id, updateItemDto, cancellationToken);
            
            Logger.LogInformation("Item updated successfully with ID: {ItemId} by user: {UserId}", 
                id, GetCurrentUserId());
            
            return CreateResponse(item, "Item updated successfully.");
        }
        catch (ArgumentException ex)
        {
            Logger.LogWarning(ex, "Validation error updating item with ID: {ItemId}", id);
            return CreateErrorResponse(ex.Message, 400);
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error updating item with ID: {ItemId}", id);
            return CreateErrorResponse("An error occurred while updating the item.", 500);
        }
    }

    /// <summary>
    /// Upload item image
    /// رفع صورة الصنف
    /// </summary>
    [HttpPost("{id}/image")]
    public async Task<IActionResult> UploadImage(int id, IFormFile image, CancellationToken cancellationToken = default)
    {
        try
        {
            if (!HasPermission("Items", "Edit"))
            {
                return CreateErrorResponse("You don't have permission to edit items.", 403);
            }

            if (image == null || image.Length == 0)
            {
                return CreateErrorResponse("No image file provided.", 400);
            }

            var imageUrl = await _itemService.UploadImageAsync(id, image, cancellationToken);
            
            Logger.LogInformation("Image uploaded successfully for item ID: {ItemId} by user: {UserId}", 
                id, GetCurrentUserId());
            
            return CreateResponse(new { ImageUrl = imageUrl }, "Image uploaded successfully.");
        }
        catch (ArgumentException ex)
        {
            Logger.LogWarning(ex, "Validation error uploading image for item ID: {ItemId}", id);
            return CreateErrorResponse(ex.Message, 400);
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error uploading image for item ID: {ItemId}", id);
            return CreateErrorResponse("An error occurred while uploading the image.", 500);
        }
    }

    /// <summary>
    /// Generate barcode for item
    /// توليد باركود للصنف
    /// </summary>
    [HttpPost("{id}/generate-barcode")]
    public async Task<IActionResult> GenerateBarcode(int id, CancellationToken cancellationToken = default)
    {
        try
        {
            if (!HasPermission("Items", "Edit"))
            {
                return CreateErrorResponse("You don't have permission to edit items.", 403);
            }

            var barcode = await _itemService.GenerateBarcodeAsync(id, cancellationToken);
            
            Logger.LogInformation("Barcode generated successfully for item ID: {ItemId} by user: {UserId}", 
                id, GetCurrentUserId());
            
            return CreateResponse(new { Barcode = barcode }, "Barcode generated successfully.");
        }
        catch (ArgumentException ex)
        {
            Logger.LogWarning(ex, "Validation error generating barcode for item ID: {ItemId}", id);
            return CreateErrorResponse(ex.Message, 400);
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error generating barcode for item ID: {ItemId}", id);
            return CreateErrorResponse("An error occurred while generating the barcode.", 500);
        }
    }

    /// <summary>
    /// Set item active status
    /// تعيين حالة تفعيل الصنف
    /// </summary>
    [HttpPatch("{id}/status")]
    public async Task<IActionResult> SetActiveStatus(int id, [FromBody] bool isActive, CancellationToken cancellationToken = default)
    {
        try
        {
            if (!HasPermission("Items", "Edit"))
            {
                return CreateErrorResponse("You don't have permission to edit items.", 403);
            }

            var result = await _itemService.SetActiveStatusAsync(id, isActive, cancellationToken);
            if (!result)
            {
                return CreateErrorResponse("Item not found.", 404);
            }

            Logger.LogInformation("Item status changed successfully for ID: {ItemId} to {Status} by user: {UserId}", 
                id, isActive ? "Active" : "Inactive", GetCurrentUserId());
            
            return CreateResponse<object?>(null, $"Item {(isActive ? "activated" : "deactivated")} successfully.");
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error changing status for item ID: {ItemId}", id);
            return CreateErrorResponse("An error occurred while changing item status.", 500);
        }
    }

    /// <summary>
    /// Delete item
    /// حذف الصنف
    /// </summary>
    [HttpDelete("{id}")]
    public async Task<IActionResult> Delete(int id, CancellationToken cancellationToken = default)
    {
        try
        {
            if (!HasPermission("Items", "Delete"))
            {
                return CreateErrorResponse("You don't have permission to delete items.", 403);
            }

            var result = await _itemService.DeleteAsync(id, cancellationToken);
            if (!result)
            {
                return CreateErrorResponse("Item not found.", 404);
            }

            Logger.LogInformation("Item deleted successfully with ID: {ItemId} by user: {UserId}", 
                id, GetCurrentUserId());
            
            return CreateResponse<object?>(null, "Item deleted successfully.");
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error deleting item with ID: {ItemId}", id);
            return CreateErrorResponse("An error occurred while deleting the item.", 500);
        }
    }

    /// <summary>
    /// Get item categories
    /// الحصول على فئات الأصناف
    /// </summary>
    [HttpGet("categories")]
    public async Task<IActionResult> GetCategories(CancellationToken cancellationToken = default)
    {
        try
        {
            var companyId = GetCurrentCompanyId();
            var categories = await _itemService.GetCategoriesAsync(companyId, cancellationToken);
            
            return CreateResponse(categories, "Categories retrieved successfully.");
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error retrieving item categories");
            return CreateErrorResponse("An error occurred while retrieving categories.", 500);
        }
    }
}
