namespace BusinessManagementSystem.Application.Services.Interfaces;

/// <summary>
/// Password hashing service interface for secure password management
/// واجهة خدمة تشفير كلمات المرور لإدارة آمنة لكلمات المرور
/// </summary>
public interface IPasswordHashService
{
    /// <summary>
    /// Hash password using secure algorithm
    /// تشفير كلمة المرور باستخدام خوارزمية آمنة
    /// </summary>
    string HashPassword(string password);

    /// <summary>
    /// Verify password against hash
    /// التحقق من كلمة المرور مقابل التشفير
    /// </summary>
    bool VerifyPassword(string password, string hash);

    /// <summary>
    /// Generate random salt
    /// توليد ملح عشوائي
    /// </summary>
    string GenerateSalt();
}
