using System.Linq.Expressions;
using BusinessManagementSystem.Domain.Common;

namespace BusinessManagementSystem.Application.Interfaces.Repositories;

/// <summary>
/// Generic repository interface for common CRUD operations
/// واجهة المستودع العامة للعمليات الأساسية
/// </summary>
/// <typeparam name="T">Entity type that inherits from BaseEntity</typeparam>
public interface IGenericRepository<T> where T : BaseEntity
{
    /// <summary>
    /// Get entity by ID
    /// الحصول على الكيان بواسطة المعرف
    /// </summary>
    Task<T?> GetByIdAsync(int id, CancellationToken cancellationToken = default);

    /// <summary>
    /// Get all entities
    /// الحصول على جميع الكيانات
    /// </summary>
    Task<IEnumerable<T>> GetAllAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// Get entities with filtering, ordering, and including related data
    /// الحصول على الكيانات مع التصفية والترتيب وتضمين البيانات المرتبطة
    /// </summary>
    Task<IEnumerable<T>> GetAsync(
        Expression<Func<T, bool>>? filter = null,
        Func<IQueryable<T>, IOrderedQueryable<T>>? orderBy = null,
        string includeProperties = "",
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Get entities with pagination
    /// الحصول على الكيانات مع التصفح
    /// </summary>
    Task<(IEnumerable<T> Items, int TotalCount)> GetPagedAsync(
        int pageNumber,
        int pageSize,
        Expression<Func<T, bool>>? filter = null,
        Func<IQueryable<T>, IOrderedQueryable<T>>? orderBy = null,
        string includeProperties = "",
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Get first entity matching the filter
    /// الحصول على أول كيان يطابق المرشح
    /// </summary>
    Task<T?> GetFirstOrDefaultAsync(
        Expression<Func<T, bool>> filter,
        string includeProperties = "",
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Check if any entity matches the filter
    /// التحقق من وجود أي كيان يطابق المرشح
    /// </summary>
    Task<bool> AnyAsync(Expression<Func<T, bool>> filter, CancellationToken cancellationToken = default);

    /// <summary>
    /// Count entities matching the filter
    /// عد الكيانات التي تطابق المرشح
    /// </summary>
    Task<int> CountAsync(Expression<Func<T, bool>>? filter = null, CancellationToken cancellationToken = default);

    /// <summary>
    /// Add new entity
    /// إضافة كيان جديد
    /// </summary>
    Task<T> AddAsync(T entity, CancellationToken cancellationToken = default);

    /// <summary>
    /// Add multiple entities
    /// إضافة عدة كيانات
    /// </summary>
    Task AddRangeAsync(IEnumerable<T> entities, CancellationToken cancellationToken = default);

    /// <summary>
    /// Update existing entity
    /// تحديث كيان موجود
    /// </summary>
    void Update(T entity);

    /// <summary>
    /// Update multiple entities
    /// تحديث عدة كيانات
    /// </summary>
    void UpdateRange(IEnumerable<T> entities);

    /// <summary>
    /// Delete entity
    /// حذف كيان
    /// </summary>
    void Delete(T entity);

    /// <summary>
    /// Delete entity by ID
    /// حذف كيان بواسطة المعرف
    /// </summary>
    Task DeleteByIdAsync(int id, CancellationToken cancellationToken = default);

    /// <summary>
    /// Delete multiple entities
    /// حذف عدة كيانات
    /// </summary>
    void DeleteRange(IEnumerable<T> entities);

    /// <summary>
    /// Execute raw SQL query
    /// تنفيذ استعلام SQL خام
    /// </summary>
    Task<IEnumerable<T>> FromSqlRawAsync(string sql, params object[] parameters);
}
