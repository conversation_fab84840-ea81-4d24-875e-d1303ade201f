using BusinessManagementSystem.Application.DTOs.Company;

namespace BusinessManagementSystem.Application.Services.Interfaces;

/// <summary>
/// Company service interface for business logic operations
/// واجهة خدمة الشركة لعمليات منطق الأعمال
/// </summary>
public interface ICompanyService
{
    /// <summary>
    /// Get all companies
    /// الحصول على جميع الشركات
    /// </summary>
    Task<IEnumerable<CompanyDto>> GetAllAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// Get company by ID
    /// الحصول على الشركة بواسطة المعرف
    /// </summary>
    Task<CompanyDto?> GetByIdAsync(int id, CancellationToken cancellationToken = default);

    /// <summary>
    /// Get company by name
    /// الحصول على الشركة بواسطة الاسم
    /// </summary>
    Task<CompanyDto?> GetByNameAsync(string name, CancellationToken cancellationToken = default);

    /// <summary>
    /// Create new company
    /// إنشاء شركة جديدة
    /// </summary>
    Task<CompanyDto> CreateAsync(CreateCompanyDto createCompanyDto, CancellationToken cancellationToken = default);

    /// <summary>
    /// Update existing company
    /// تحديث شركة موجودة
    /// </summary>
    Task<CompanyDto> UpdateAsync(int id, UpdateCompanyDto updateCompanyDto, CancellationToken cancellationToken = default);

    /// <summary>
    /// Delete company
    /// حذف الشركة
    /// </summary>
    Task<bool> DeleteAsync(int id, CancellationToken cancellationToken = default);

    /// <summary>
    /// Check if company name exists
    /// التحقق من وجود اسم الشركة
    /// </summary>
    Task<bool> IsNameExistsAsync(string name, int? excludeId = null, CancellationToken cancellationToken = default);

    /// <summary>
    /// Check if commercial registration number exists
    /// التحقق من وجود رقم السجل التجاري
    /// </summary>
    Task<bool> IsCommercialRegistrationNumberExistsAsync(string registrationNumber, int? excludeId = null, CancellationToken cancellationToken = default);

    /// <summary>
    /// Check if tax number exists
    /// التحقق من وجود الرقم الضريبي
    /// </summary>
    Task<bool> IsTaxNumberExistsAsync(string taxNumber, int? excludeId = null, CancellationToken cancellationToken = default);
}
