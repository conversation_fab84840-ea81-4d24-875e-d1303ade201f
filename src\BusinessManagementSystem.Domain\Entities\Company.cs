using System.ComponentModel.DataAnnotations;
using BusinessManagementSystem.Domain.Common;

namespace BusinessManagementSystem.Domain.Entities;

/// <summary>
/// Company entity - stores company information
/// كيان الشركة - يخزن معلومات الشركة
/// </summary>
public class Company : BaseEntity
{
    /// <summary>
    /// Company name
    /// اسم الشركة
    /// </summary>
    [Required]
    [MaxLength(200)]
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// Company description
    /// وصف الشركة
    /// </summary>
    [MaxLength(1000)]
    public string? Description { get; set; }

    /// <summary>
    /// Company address
    /// عنوان الشركة
    /// </summary>
    [MaxLength(500)]
    public string? Address { get; set; }

    /// <summary>
    /// Logo file path
    /// مسار ملف الشعار
    /// </summary>
    [MaxLength(500)]
    public string? LogoPath { get; set; }

    /// <summary>
    /// Commercial registration number
    /// رقم السجل التجاري
    /// </summary>
    [MaxLength(50)]
    public string? CommercialRegistrationNumber { get; set; }

    /// <summary>
    /// Tax identification number
    /// الرقم الضريبي
    /// </summary>
    [MaxLength(50)]
    public string? TaxNumber { get; set; }

    /// <summary>
    /// Insurance number
    /// رقم التأمين
    /// </summary>
    [MaxLength(50)]
    public string? InsuranceNumber { get; set; }

    /// <summary>
    /// Phone number
    /// رقم الهاتف
    /// </summary>
    [MaxLength(20)]
    public string? Phone { get; set; }

    // Navigation properties
    public virtual ICollection<Department> Departments { get; set; } = new List<Department>();
    public virtual ICollection<Partner> Partners { get; set; } = new List<Partner>();
    public virtual MainCash? MainCash { get; set; }
    public virtual ICollection<FinancialTransaction> FinancialTransactions { get; set; } = new List<FinancialTransaction>();
    public virtual ICollection<Warehouse> Warehouses { get; set; } = new List<Warehouse>();
    public virtual ICollection<Item> Items { get; set; } = new List<Item>();
    public virtual ICollection<InventoryTransaction> InventoryTransactions { get; set; } = new List<InventoryTransaction>();
    public virtual ICollection<Invoice> Invoices { get; set; } = new List<Invoice>();
    public virtual ICollection<Person> Persons { get; set; } = new List<Person>();
    public virtual ICollection<User> Users { get; set; } = new List<User>();
}
