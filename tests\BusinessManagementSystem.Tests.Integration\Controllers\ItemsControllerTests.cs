using FluentAssertions;
using Microsoft.AspNetCore.Mvc.Testing;
using Newtonsoft.Json;
using System.Net;
using System.Text;
using BusinessManagementSystem.Application.DTOs.Item;
using BusinessManagementSystem.API;

namespace BusinessManagementSystem.Tests.Integration.Controllers;

/// <summary>
/// Integration tests for ItemsController
/// اختبارات التكامل لتحكم الأصناف
/// </summary>
public class ItemsControllerTests : IClassFixture<TestWebApplicationFactory<Program>>
{
    private readonly HttpClient _client;
    private readonly TestWebApplicationFactory<Program> _factory;

    public ItemsControllerTests(TestWebApplicationFactory<Program> factory)
    {
        _factory = factory;
        _client = _factory.CreateClient();
    }

    [Fact]
    public async Task GetAll_ShouldReturnItems_WhenItemsExist()
    {
        // Act
        var response = await _client.GetAsync("/api/Items");

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.OK);
        
        var content = await response.Content.ReadAsStringAsync();
        var items = JsonConvert.DeserializeObject<List<ItemDto>>(content);
        
        items.Should().NotBeNull();
        items.Should().NotBeEmpty();
        items.Should().Contain(i => i.Name == "Test Item");
    }

    [Fact]
    public async Task GetById_ShouldReturnItem_WhenItemExists()
    {
        // Arrange
        var itemId = 1;

        // Act
        var response = await _client.GetAsync($"/api/Items/{itemId}");

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.OK);
        
        var content = await response.Content.ReadAsStringAsync();
        var item = JsonConvert.DeserializeObject<ItemDto>(content);
        
        item.Should().NotBeNull();
        item!.Id.Should().Be(itemId);
        item.Name.Should().Be("Test Item");
    }

    [Fact]
    public async Task GetById_ShouldReturnNotFound_WhenItemDoesNotExist()
    {
        // Arrange
        var itemId = 999;

        // Act
        var response = await _client.GetAsync($"/api/Items/{itemId}");

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.NotFound);
    }

    [Fact]
    public async Task Create_ShouldCreateItem_WhenValidDataProvided()
    {
        // Arrange
        var createItemDto = new CreateItemDto
        {
            Name = "New Test Item",
            Description = "New Test Description",
            UnitPrice = 150.00m,
            CostPrice = 120.00m,
            StockQuantity = 25,
            MinimumStockLevel = 5,
            MaximumStockLevel = 50,
            ReorderPoint = 10,
            Category = "Test Category",
            CompanyId = 1
        };

        var json = JsonConvert.SerializeObject(createItemDto);
        var content = new StringContent(json, Encoding.UTF8, "application/json");

        // Act
        var response = await _client.PostAsync("/api/Items", content);

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.Created);
        
        var responseContent = await response.Content.ReadAsStringAsync();
        var createdItem = JsonConvert.DeserializeObject<ItemDto>(responseContent);
        
        createdItem.Should().NotBeNull();
        createdItem!.Name.Should().Be(createItemDto.Name);
        createdItem.Description.Should().Be(createItemDto.Description);
        createdItem.UnitPrice.Should().Be(createItemDto.UnitPrice);
        createdItem.CostPrice.Should().Be(createItemDto.CostPrice);
        createdItem.CompanyId.Should().Be(createItemDto.CompanyId);
    }

    [Fact]
    public async Task Create_ShouldReturnBadRequest_WhenInvalidDataProvided()
    {
        // Arrange
        var createItemDto = new CreateItemDto
        {
            Name = "", // Invalid: empty name
            UnitPrice = -10, // Invalid: negative price
            CompanyId = 1
        };

        var json = JsonConvert.SerializeObject(createItemDto);
        var content = new StringContent(json, Encoding.UTF8, "application/json");

        // Act
        var response = await _client.PostAsync("/api/Items", content);

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.BadRequest);
    }

    [Fact]
    public async Task Update_ShouldUpdateItem_WhenValidDataProvided()
    {
        // Arrange
        var itemId = 1;
        var updateItemDto = new UpdateItemDto
        {
            Name = "Updated Test Item",
            Description = "Updated Description",
            UnitPrice = 200.00m,
            CostPrice = 160.00m,
            StockQuantity = 75,
            MinimumStockLevel = 15,
            MaximumStockLevel = 150,
            ReorderPoint = 20,
            Category = "Updated Category"
        };

        var json = JsonConvert.SerializeObject(updateItemDto);
        var content = new StringContent(json, Encoding.UTF8, "application/json");

        // Act
        var response = await _client.PutAsync($"/api/Items/{itemId}", content);

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.OK);
        
        var responseContent = await response.Content.ReadAsStringAsync();
        var updatedItem = JsonConvert.DeserializeObject<ItemDto>(responseContent);
        
        updatedItem.Should().NotBeNull();
        updatedItem!.Id.Should().Be(itemId);
        updatedItem.Name.Should().Be(updateItemDto.Name);
        updatedItem.Description.Should().Be(updateItemDto.Description);
        updatedItem.UnitPrice.Should().Be(updateItemDto.UnitPrice);
    }

    [Fact]
    public async Task Update_ShouldReturnNotFound_WhenItemDoesNotExist()
    {
        // Arrange
        var itemId = 999;
        var updateItemDto = new UpdateItemDto
        {
            Name = "Updated Item",
            UnitPrice = 100.00m
        };

        var json = JsonConvert.SerializeObject(updateItemDto);
        var content = new StringContent(json, Encoding.UTF8, "application/json");

        // Act
        var response = await _client.PutAsync($"/api/Items/{itemId}", content);

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.NotFound);
    }

    [Fact]
    public async Task GetByCompany_ShouldReturnItemsForCompany()
    {
        // Arrange
        var companyId = 1;

        // Act
        var response = await _client.GetAsync($"/api/Items/company/{companyId}");

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.OK);
        
        var content = await response.Content.ReadAsStringAsync();
        var items = JsonConvert.DeserializeObject<List<ItemDto>>(content);
        
        items.Should().NotBeNull();
        items.Should().NotBeEmpty();
        items.Should().OnlyContain(i => i.CompanyId == companyId);
    }

    [Fact]
    public async Task Search_ShouldReturnMatchingItems_WhenSearchTermProvided()
    {
        // Arrange
        var companyId = 1;
        var searchTerm = "Test";

        // Act
        var response = await _client.GetAsync($"/api/Items/search?companyId={companyId}&searchTerm={searchTerm}");

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.OK);
        
        var content = await response.Content.ReadAsStringAsync();
        var items = JsonConvert.DeserializeObject<List<ItemDto>>(content);
        
        items.Should().NotBeNull();
        items.Should().NotBeEmpty();
        items.Should().OnlyContain(i => i.Name.Contains(searchTerm) || 
                                       (i.Description != null && i.Description.Contains(searchTerm)));
    }

    [Fact]
    public async Task Search_ShouldReturnBadRequest_WhenSearchTermIsEmpty()
    {
        // Arrange
        var companyId = 1;
        var searchTerm = "";

        // Act
        var response = await _client.GetAsync($"/api/Items/search?companyId={companyId}&searchTerm={searchTerm}");

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.BadRequest);
    }

    [Fact]
    public async Task GenerateBarcode_ShouldGenerateBarcode_WhenItemExists()
    {
        // Arrange
        var itemId = 1;

        // Act
        var response = await _client.PostAsync($"/api/Items/{itemId}/generate-barcode", null);

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.OK);
        
        var content = await response.Content.ReadAsStringAsync();
        var result = JsonConvert.DeserializeObject<dynamic>(content);
        
        string barcode = result.barcode;
        barcode.Should().NotBeNullOrEmpty();
        barcode.Should().HaveLength(13); // EAN-13 format
        barcode.Should().MatchRegex(@"^\d{13}$"); // Should be all digits
    }

    [Fact]
    public async Task GenerateBarcode_ShouldReturnNotFound_WhenItemDoesNotExist()
    {
        // Arrange
        var itemId = 999;

        // Act
        var response = await _client.PostAsync($"/api/Items/{itemId}/generate-barcode", null);

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.NotFound);
    }

    [Fact]
    public async Task SetActiveStatus_ShouldUpdateStatus_WhenItemExists()
    {
        // Arrange
        var itemId = 1;
        var isActive = false;

        var json = JsonConvert.SerializeObject(isActive);
        var content = new StringContent(json, Encoding.UTF8, "application/json");

        // Act
        var response = await _client.PatchAsync($"/api/Items/{itemId}/status", content);

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.NoContent);

        // Verify the status was updated
        var getResponse = await _client.GetAsync($"/api/Items/{itemId}");
        var getContent = await getResponse.Content.ReadAsStringAsync();
        var item = JsonConvert.DeserializeObject<ItemDto>(getContent);
        
        item.Should().NotBeNull();
        item!.IsActive.Should().Be(isActive);
    }

    [Fact]
    public async Task Delete_ShouldReturnNotFound_WhenItemDoesNotExist()
    {
        // Arrange
        var itemId = 999;

        // Act
        var response = await _client.DeleteAsync($"/api/Items/{itemId}");

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.NotFound);
    }

    [Fact]
    public async Task GetLowStockItems_ShouldReturnLowStockItems()
    {
        // Arrange
        var companyId = 1;

        // Act
        var response = await _client.GetAsync($"/api/Items/low-stock?companyId={companyId}");

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.OK);
        
        var content = await response.Content.ReadAsStringAsync();
        var items = JsonConvert.DeserializeObject<List<ItemDto>>(content);
        
        items.Should().NotBeNull();
        // Items should have stock quantity <= minimum stock level
        items.Should().OnlyContain(i => i.StockQuantity <= i.MinimumStockLevel);
    }

    [Fact]
    public async Task GetCategories_ShouldReturnDistinctCategories()
    {
        // Arrange
        var companyId = 1;

        // Act
        var response = await _client.GetAsync($"/api/Items/categories?companyId={companyId}");

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.OK);
        
        var content = await response.Content.ReadAsStringAsync();
        var categories = JsonConvert.DeserializeObject<List<string>>(content);
        
        categories.Should().NotBeNull();
        categories.Should().NotBeEmpty();
        categories.Should().Contain("Test Category");
        categories.Should().OnlyHaveUniqueItems();
    }

    [Fact]
    public async Task GetCurrentStockValue_ShouldReturnStockValue()
    {
        // Arrange
        var companyId = 1;

        // Act
        var response = await _client.GetAsync($"/api/Items/stock-value?companyId={companyId}");

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.OK);
        
        var content = await response.Content.ReadAsStringAsync();
        var stockValue = JsonConvert.DeserializeObject<decimal>(content);
        
        stockValue.Should().BeGreaterOrEqualTo(0);
    }

    [Fact]
    public async Task GetItemsRequiringReorder_ShouldReturnItemsNeedingReorder()
    {
        // Arrange
        var companyId = 1;

        // Act
        var response = await _client.GetAsync($"/api/Items/reorder-required?companyId={companyId}");

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.OK);
        
        var content = await response.Content.ReadAsStringAsync();
        var items = JsonConvert.DeserializeObject<List<ItemDto>>(content);
        
        items.Should().NotBeNull();
        // Items should have stock quantity <= reorder point
        items.Should().OnlyContain(i => i.StockQuantity <= i.ReorderPoint);
    }
}
