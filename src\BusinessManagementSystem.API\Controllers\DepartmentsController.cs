using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using BusinessManagementSystem.Application.DTOs.Department;
using BusinessManagementSystem.Application.Services.Interfaces;

namespace BusinessManagementSystem.API.Controllers;

/// <summary>
/// Departments controller for department management operations
/// وحدة تحكم الأقسام لعمليات إدارة الأقسام
/// </summary>
[Authorize]
public class DepartmentsController : BaseApiController
{
    private readonly IDepartmentService _departmentService;
    private readonly ILogger<DepartmentsController> _logger;

    public DepartmentsController(IDepartmentService departmentService, ILogger<DepartmentsController> logger)
    {
        _departmentService = departmentService;
        _logger = logger;
    }

    /// <summary>
    /// Get all departments with pagination
    /// الحصول على جميع الأقسام مع التصفح
    /// </summary>
    [HttpGet]
    public async Task<IActionResult> GetAll(
        [FromQuery] int page = 1, 
        [FromQuery] int pageSize = 10,
        [FromQuery] bool? isActive = null,
        CancellationToken cancellationToken = default)
    {
        try
        {
            if (!HasPermission("Departments", "View"))
            {
                return CreateErrorResponse("You don't have permission to view departments.", 403);
            }

            var companyId = GetCurrentCompanyId();
            IEnumerable<DepartmentDto> departments;

            if (isActive.HasValue)
            {
                departments = await _departmentService.GetActiveByCompanyAsync(companyId, cancellationToken);
                if (!isActive.Value)
                {
                    var allDepartments = await _departmentService.GetByCompanyAsync(companyId, cancellationToken);
                    departments = allDepartments.Where(d => !d.IsActive);
                }
            }
            else
            {
                departments = await _departmentService.GetByCompanyAsync(companyId, cancellationToken);
            }

            // Apply pagination
            var totalCount = departments.Count();
            var pagedDepartments = departments
                .Skip((page - 1) * pageSize)
                .Take(pageSize)
                .ToList();

            var result = new
            {
                Items = pagedDepartments,
                TotalCount = totalCount,
                Page = page,
                PageSize = pageSize,
                TotalPages = (int)Math.Ceiling((double)totalCount / pageSize)
            };

            return CreateResponse(result, "Departments retrieved successfully.");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving departments");
            return CreateErrorResponse("An error occurred while retrieving departments.", 500);
        }
    }

    /// <summary>
    /// Get department by ID
    /// الحصول على القسم بواسطة المعرف
    /// </summary>
    [HttpGet("{id}")]
    public async Task<IActionResult> GetById(int id, CancellationToken cancellationToken = default)
    {
        try
        {
            if (!HasPermission("Departments", "View"))
            {
                return CreateErrorResponse("You don't have permission to view departments.", 403);
            }

            var department = await _departmentService.GetByIdAsync(id, cancellationToken);
            if (department == null)
            {
                return CreateErrorResponse("Department not found.", 404);
            }

            // Check if user can access this department (same company)
            var companyId = GetCurrentCompanyId();
            if (department.CompanyId != companyId)
            {
                return CreateErrorResponse("You don't have permission to view this department.", 403);
            }

            return CreateResponse(department, "Department retrieved successfully.");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving department with ID: {DepartmentId}", id);
            return CreateErrorResponse("An error occurred while retrieving the department.", 500);
        }
    }

    /// <summary>
    /// Get departments by company
    /// الحصول على الأقسام بواسطة الشركة
    /// </summary>
    [HttpGet("company/{companyId}")]
    public async Task<IActionResult> GetByCompany(int companyId, CancellationToken cancellationToken = default)
    {
        try
        {
            if (!HasPermission("Departments", "View"))
            {
                return CreateErrorResponse("You don't have permission to view departments.", 403);
            }

            // Users can only view departments from their own company unless they're administrators
            var currentCompanyId = GetCurrentCompanyId();
            var currentRole = GetCurrentUserRole();
            
            if (currentRole != "Administrator" && companyId != currentCompanyId)
            {
                return CreateErrorResponse("You can only view departments from your own company.", 403);
            }

            var departments = await _departmentService.GetByCompanyAsync(companyId, cancellationToken);
            return CreateResponse(departments, "Departments retrieved successfully.");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving departments for company: {CompanyId}", companyId);
            return CreateErrorResponse("An error occurred while retrieving departments.", 500);
        }
    }

    /// <summary>
    /// Create new department
    /// إنشاء قسم جديد
    /// </summary>
    [HttpPost]
    public async Task<IActionResult> Create([FromBody] CreateDepartmentDto createDepartmentDto, CancellationToken cancellationToken = default)
    {
        try
        {
            if (!HasPermission("Departments", "Add"))
            {
                return CreateErrorResponse("You don't have permission to create departments.", 403);
            }

            if (!ModelState.IsValid)
            {
                return CreateErrorResponse("Invalid department data.", 400);
            }

            // Ensure the department is created for the current user's company
            createDepartmentDto.CompanyId = GetCurrentCompanyId();

            var department = await _departmentService.CreateAsync(createDepartmentDto, cancellationToken);
            
            _logger.LogInformation("Department created successfully with ID: {DepartmentId} by user: {UserId}", 
                department.Id, GetCurrentUserId());
            
            return CreatedAtAction(nameof(GetById), new { id = department.Id }, 
                CreateResponse(department, "Department created successfully."));
        }
        catch (ArgumentException ex)
        {
            _logger.LogWarning(ex, "Validation error creating department");
            return CreateErrorResponse(ex.Message, 400);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating department");
            return CreateErrorResponse("An error occurred while creating the department.", 500);
        }
    }

    /// <summary>
    /// Update existing department
    /// تحديث قسم موجود
    /// </summary>
    [HttpPut("{id}")]
    public async Task<IActionResult> Update(int id, [FromBody] UpdateDepartmentDto updateDepartmentDto, CancellationToken cancellationToken = default)
    {
        try
        {
            if (!HasPermission("Departments", "Edit"))
            {
                return CreateErrorResponse("You don't have permission to edit departments.", 403);
            }

            if (!ModelState.IsValid)
            {
                return CreateErrorResponse("Invalid department data.", 400);
            }

            var department = await _departmentService.UpdateAsync(id, updateDepartmentDto, cancellationToken);
            
            _logger.LogInformation("Department updated successfully with ID: {DepartmentId} by user: {UserId}", 
                id, GetCurrentUserId());
            
            return CreateResponse(department, "Department updated successfully.");
        }
        catch (ArgumentException ex)
        {
            _logger.LogWarning(ex, "Validation error updating department with ID: {DepartmentId}", id);
            return CreateErrorResponse(ex.Message, 400);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating department with ID: {DepartmentId}", id);
            return CreateErrorResponse("An error occurred while updating the department.", 500);
        }
    }

    /// <summary>
    /// Set department active status
    /// تعيين حالة تفعيل القسم
    /// </summary>
    [HttpPatch("{id}/status")]
    public async Task<IActionResult> SetActiveStatus(int id, [FromBody] bool isActive, CancellationToken cancellationToken = default)
    {
        try
        {
            if (!HasPermission("Departments", "Edit"))
            {
                return CreateErrorResponse("You don't have permission to change department status.", 403);
            }

            var result = await _departmentService.SetActiveStatusAsync(id, isActive, cancellationToken);
            if (!result)
            {
                return CreateErrorResponse("Department not found.", 404);
            }

            _logger.LogInformation("Department status changed successfully for ID: {DepartmentId} to {Status} by user: {UserId}", 
                id, isActive ? "Active" : "Inactive", GetCurrentUserId());
            
            return CreateResponse<object?>(null, $"Department {(isActive ? "activated" : "deactivated")} successfully.");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error changing status for department ID: {DepartmentId}", id);
            return CreateErrorResponse("An error occurred while changing department status.", 500);
        }
    }

    /// <summary>
    /// Delete department
    /// حذف القسم
    /// </summary>
    [HttpDelete("{id}")]
    public async Task<IActionResult> Delete(int id, CancellationToken cancellationToken = default)
    {
        try
        {
            if (!HasPermission("Departments", "Delete"))
            {
                return CreateErrorResponse("You don't have permission to delete departments.", 403);
            }

            var result = await _departmentService.DeleteAsync(id, cancellationToken);
            if (!result)
            {
                return CreateErrorResponse("Department not found.", 404);
            }

            _logger.LogInformation("Department deleted successfully with ID: {DepartmentId} by user: {UserId}", 
                id, GetCurrentUserId());
            
            return CreateResponse<object?>(null, "Department deleted successfully.");
        }
        catch (InvalidOperationException ex)
        {
            _logger.LogWarning(ex, "Cannot delete department with ID: {DepartmentId}", id);
            return CreateErrorResponse(ex.Message, 400);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deleting department with ID: {DepartmentId}", id);
            return CreateErrorResponse("An error occurred while deleting the department.", 500);
        }
    }

    /// <summary>
    /// Get department statistics
    /// الحصول على إحصائيات القسم
    /// </summary>
    [HttpGet("{id}/statistics")]
    public async Task<IActionResult> GetStatistics(int id, CancellationToken cancellationToken = default)
    {
        try
        {
            if (!HasPermission("Departments", "View"))
            {
                return CreateErrorResponse("You don't have permission to view department statistics.", 403);
            }

            var statistics = await _departmentService.GetDepartmentStatisticsAsync(id, cancellationToken);
            return CreateResponse(statistics, "Department statistics retrieved successfully.");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving statistics for department ID: {DepartmentId}", id);
            return CreateErrorResponse("An error occurred while retrieving department statistics.", 500);
        }
    }

    /// <summary>
    /// Get users in department
    /// الحصول على المستخدمين في القسم
    /// </summary>
    [HttpGet("{id}/users")]
    public async Task<IActionResult> GetUsers(int id, CancellationToken cancellationToken = default)
    {
        try
        {
            if (!HasPermission("Departments", "View"))
            {
                return CreateErrorResponse("You don't have permission to view department users.", 403);
            }

            var users = await _departmentService.GetUsersByDepartmentAsync(id, cancellationToken);
            return CreateResponse(users, "Department users retrieved successfully.");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving users for department ID: {DepartmentId}", id);
            return CreateErrorResponse("An error occurred while retrieving department users.", 500);
        }
    }
}
