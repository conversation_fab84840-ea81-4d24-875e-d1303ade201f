using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using BusinessManagementSystem.Application.DTOs.FinancialTransaction;
using BusinessManagementSystem.Application.Services.Interfaces;

namespace BusinessManagementSystem.API.Controllers;

/// <summary>
/// Financial Transactions controller for financial transaction management
/// وحدة تحكم المعاملات المالية لإدارة المعاملات المالية
/// </summary>
[Authorize]
public class FinancialTransactionsController : BaseApiController
{
    private readonly IFinancialTransactionService _financialTransactionService;
    

    public FinancialTransactionsController(
        IFinancialTransactionService financialTransactionService,
        ILogger<FinancialTransactionsController> logger) : base(logger)
    {
        _financialTransactionService = financialTransactionService;
    }

    /// <summary>
    /// Get all financial transactions with pagination and filtering
    /// الحصول على جميع المعاملات المالية مع التصفح والتصفية
    /// </summary>
    [HttpGet]
    public async Task<IActionResult> GetAll(
        [FromQuery] int page = 1, 
        [FromQuery] int pageSize = 10,
        [FromQuery] string? transactionType = null,
        [FromQuery] DateTime? startDate = null,
        [FromQuery] DateTime? endDate = null,
        [FromQuery] int? personId = null,
        CancellationToken cancellationToken = default)
    {
        try
        {
            if (!HasPermission("FinancialTransactions", "View"))
            {
                return CreateErrorResponse("You don't have permission to view financial transactions.", 403);
            }

            var companyId = GetCurrentCompanyId();
            IEnumerable<FinancialTransactionDto> transactions;

            if (startDate.HasValue && endDate.HasValue)
            {
                transactions = await _financialTransactionService.GetByDateRangeAsync(
                    companyId, startDate.Value, endDate.Value, cancellationToken);
            }
            else if (!string.IsNullOrEmpty(transactionType))
            {
                transactions = await _financialTransactionService.GetByTypeAsync(
                    companyId, transactionType, cancellationToken);
            }
            else if (personId.HasValue)
            {
                transactions = await _financialTransactionService.GetByPersonAsync(
                    personId.Value, cancellationToken);
            }
            else
            {
                transactions = await _financialTransactionService.GetByCompanyAsync(companyId, cancellationToken);
            }

            // Apply pagination
            var totalCount = transactions.Count();
            var pagedTransactions = transactions
                .OrderByDescending(t => t.TransactionDate)
                .Skip((page - 1) * pageSize)
                .Take(pageSize)
                .ToList();

            var result = new
            {
                Items = pagedTransactions,
                TotalCount = totalCount,
                Page = page,
                PageSize = pageSize,
                TotalPages = (int)Math.Ceiling((double)totalCount / pageSize)
            };

            return CreateResponse(result, "Financial transactions retrieved successfully.");
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error retrieving financial transactions");
            return CreateErrorResponse("An error occurred while retrieving financial transactions.", 500);
        }
    }

    /// <summary>
    /// Get financial transaction by ID
    /// الحصول على المعاملة المالية بواسطة المعرف
    /// </summary>
    [HttpGet("{id}")]
    public async Task<IActionResult> GetById(int id, CancellationToken cancellationToken = default)
    {
        try
        {
            if (!HasPermission("FinancialTransactions", "View"))
            {
                return CreateErrorResponse("You don't have permission to view financial transactions.", 403);
            }

            var transaction = await _financialTransactionService.GetByIdAsync(id, cancellationToken);
            if (transaction == null)
            {
                return CreateErrorResponse("Financial transaction not found.", 404);
            }

            // Check if user can access this transaction (same company)
            var companyId = GetCurrentCompanyId();
            if (transaction.CompanyId != companyId)
            {
                return CreateErrorResponse("You don't have permission to view this transaction.", 403);
            }

            return CreateResponse(transaction, "Financial transaction retrieved successfully.");
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error retrieving financial transaction with ID: {TransactionId}", id);
            return CreateErrorResponse("An error occurred while retrieving the financial transaction.", 500);
        }
    }

    /// <summary>
    /// Create new financial transaction
    /// إنشاء معاملة مالية جديدة
    /// </summary>
    [HttpPost]
    public async Task<IActionResult> Create([FromBody] CreateFinancialTransactionDto createTransactionDto, CancellationToken cancellationToken = default)
    {
        try
        {
            if (!HasPermission("FinancialTransactions", "Add"))
            {
                return CreateErrorResponse("You don't have permission to create financial transactions.", 403);
            }

            if (!ModelState.IsValid)
            {
                return CreateErrorResponse("Invalid transaction data.", 400);
            }

            // Ensure the transaction is created for the current user's company
            createTransactionDto.CompanyId = GetCurrentCompanyId();

            var transaction = await _financialTransactionService.CreateAsync(createTransactionDto, cancellationToken);
            
            Logger.LogInformation("Financial transaction created successfully with ID: {TransactionId} by user: {UserId}", 
                transaction.Id, GetCurrentUserId());
            
            return CreatedAtAction(nameof(GetById), new { id = transaction.Id }, 
                CreateResponse(transaction, "Financial transaction created successfully."));
        }
        catch (ArgumentException ex)
        {
            Logger.LogWarning(ex, "Validation error creating financial transaction");
            return CreateErrorResponse(ex.Message, 400);
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error creating financial transaction");
            return CreateErrorResponse("An error occurred while creating the financial transaction.", 500);
        }
    }

    /// <summary>
    /// Update existing financial transaction
    /// تحديث معاملة مالية موجودة
    /// </summary>
    [HttpPut("{id}")]
    public async Task<IActionResult> Update(int id, [FromBody] UpdateFinancialTransactionDto updateTransactionDto, CancellationToken cancellationToken = default)
    {
        try
        {
            if (!HasPermission("FinancialTransactions", "Edit"))
            {
                return CreateErrorResponse("You don't have permission to edit financial transactions.", 403);
            }

            if (!ModelState.IsValid)
            {
                return CreateErrorResponse("Invalid transaction data.", 400);
            }

            var transaction = await _financialTransactionService.UpdateAsync(id, updateTransactionDto, cancellationToken);
            
            Logger.LogInformation("Financial transaction updated successfully with ID: {TransactionId} by user: {UserId}", 
                id, GetCurrentUserId());
            
            return CreateResponse(transaction, "Financial transaction updated successfully.");
        }
        catch (ArgumentException ex)
        {
            Logger.LogWarning(ex, "Validation error updating financial transaction with ID: {TransactionId}", id);
            return CreateErrorResponse(ex.Message, 400);
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error updating financial transaction with ID: {TransactionId}", id);
            return CreateErrorResponse("An error occurred while updating the financial transaction.", 500);
        }
    }

    /// <summary>
    /// Delete financial transaction
    /// حذف المعاملة المالية
    /// </summary>
    [HttpDelete("{id}")]
    public async Task<IActionResult> Delete(int id, CancellationToken cancellationToken = default)
    {
        try
        {
            if (!HasPermission("FinancialTransactions", "Delete"))
            {
                return CreateErrorResponse("You don't have permission to delete financial transactions.", 403);
            }

            var result = await _financialTransactionService.DeleteAsync(id, cancellationToken);
            if (!result)
            {
                return CreateErrorResponse("Financial transaction not found.", 404);
            }

            Logger.LogInformation("Financial transaction deleted successfully with ID: {TransactionId} by user: {UserId}", 
                id, GetCurrentUserId());
            
            return CreateResponse<object?>(null, "Financial transaction deleted successfully.");
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error deleting financial transaction with ID: {TransactionId}", id);
            return CreateErrorResponse("An error occurred while deleting the financial transaction.", 500);
        }
    }

    /// <summary>
    /// Get cash flow report
    /// الحصول على تقرير التدفق النقدي
    /// </summary>
    [HttpGet("cash-flow")]
    public async Task<IActionResult> GetCashFlowReport(
        [FromQuery] DateTime startDate,
        [FromQuery] DateTime endDate,
        CancellationToken cancellationToken = default)
    {
        try
        {
            if (!HasPermission("FinancialTransactions", "View"))
            {
                return CreateErrorResponse("You don't have permission to view financial reports.", 403);
            }

            var companyId = GetCurrentCompanyId();
            var cashFlowReport = await _financialTransactionService.GetCashFlowReportAsync(
                companyId, startDate, endDate, cancellationToken);
            
            return CreateResponse(cashFlowReport, "Cash flow report retrieved successfully.");
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error retrieving cash flow report");
            return CreateErrorResponse("An error occurred while retrieving the cash flow report.", 500);
        }
    }

    /// <summary>
    /// Get balance summary
    /// الحصول على ملخص الرصيد
    /// </summary>
    [HttpGet("balance-summary")]
    public async Task<IActionResult> GetBalanceSummary(CancellationToken cancellationToken = default)
    {
        try
        {
            if (!HasPermission("FinancialTransactions", "View"))
            {
                return CreateErrorResponse("You don't have permission to view financial reports.", 403);
            }

            var companyId = GetCurrentCompanyId();
            var balanceSummary = await _financialTransactionService.GetBalanceSummaryAsync(companyId, cancellationToken);
            
            return CreateResponse(balanceSummary, "Balance summary retrieved successfully.");
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error retrieving balance summary");
            return CreateErrorResponse("An error occurred while retrieving the balance summary.", 500);
        }
    }

    /// <summary>
    /// Get profit and loss report
    /// الحصول على تقرير الأرباح والخسائر
    /// </summary>
    [HttpGet("profit-loss")]
    public async Task<IActionResult> GetProfitLossReport(
        [FromQuery] DateTime startDate,
        [FromQuery] DateTime endDate,
        CancellationToken cancellationToken = default)
    {
        try
        {
            if (!HasPermission("FinancialTransactions", "View"))
            {
                return CreateErrorResponse("You don't have permission to view financial reports.", 403);
            }

            var companyId = GetCurrentCompanyId();
            var profitLossReport = await _financialTransactionService.GetProfitLossReportAsync(
                companyId, startDate, endDate, cancellationToken);
            
            return CreateResponse(profitLossReport, "Profit and loss report retrieved successfully.");
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error retrieving profit and loss report");
            return CreateErrorResponse("An error occurred while retrieving the profit and loss report.", 500);
        }
    }

    /// <summary>
    /// Get transaction types
    /// الحصول على أنواع المعاملات
    /// </summary>
    [HttpGet("types")]
    public async Task<IActionResult> GetTransactionTypes(CancellationToken cancellationToken = default)
    {
        try
        {
            var transactionTypes = await _financialTransactionService.GetTransactionTypesAsync(cancellationToken);
            
            return CreateResponse(transactionTypes, "Transaction types retrieved successfully.");
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error retrieving transaction types");
            return CreateErrorResponse("An error occurred while retrieving transaction types.", 500);
        }
    }
}
