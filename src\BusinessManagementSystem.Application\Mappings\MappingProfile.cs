using AutoMapper;
using BusinessManagementSystem.Application.DTOs.Auth;
using BusinessManagementSystem.Application.DTOs.Company;
using BusinessManagementSystem.Application.DTOs.Invoice;
using BusinessManagementSystem.Application.DTOs.User;
using BusinessManagementSystem.Domain.Entities;

namespace BusinessManagementSystem.Application.Mappings;

/// <summary>
/// AutoMapper profile for entity to DTO mappings
/// ملف تعريف AutoMapper لتعيين الكيانات إلى DTOs
/// </summary>
public class MappingProfile : Profile
{
    public MappingProfile()
    {
        CreateCompanyMappings();
        CreateUserMappings();
        CreateAuthMappings();
        CreateInvoiceMappings();
        CreateCommonMappings();
    }

    /// <summary>
    /// Create company-related mappings
    /// إنشاء تعيينات متعلقة بالشركة
    /// </summary>
    private void CreateCompanyMappings()
    {
        CreateMap<Company, CompanyDto>();
        CreateMap<CreateCompanyDto, Company>()
            .ForMember(dest => dest.Id, opt => opt.Ignore())
            .ForMember(dest => dest.CreatedAt, opt => opt.Ignore())
            .ForMember(dest => dest.UpdatedAt, opt => opt.Ignore());
        CreateMap<UpdateCompanyDto, Company>()
            .ForMember(dest => dest.Id, opt => opt.Ignore())
            .ForMember(dest => dest.CreatedAt, opt => opt.Ignore())
            .ForMember(dest => dest.UpdatedAt, opt => opt.Ignore());
    }

    /// <summary>
    /// Create user-related mappings
    /// إنشاء تعيينات متعلقة بالمستخدم
    /// </summary>
    private void CreateUserMappings()
    {
        CreateMap<User, UserDto>()
            .ForMember(dest => dest.RoleName, opt => opt.MapFrom(src => src.Role.Name))
            .ForMember(dest => dest.CompanyName, opt => opt.MapFrom(src => src.Company.Name));

        CreateMap<CreateUserDto, User>()
            .ForMember(dest => dest.Id, opt => opt.Ignore())
            .ForMember(dest => dest.PasswordHash, opt => opt.Ignore()) // Will be set by service
            .ForMember(dest => dest.CreatedAt, opt => opt.Ignore())
            .ForMember(dest => dest.UpdatedAt, opt => opt.Ignore())
            .ForMember(dest => dest.RefreshToken, opt => opt.Ignore())
            .ForMember(dest => dest.RefreshTokenExpiryTime, opt => opt.Ignore())
            .ForMember(dest => dest.LastLoginDate, opt => opt.Ignore());

        CreateMap<UpdateUserDto, User>()
            .ForMember(dest => dest.Id, opt => opt.Ignore())
            .ForMember(dest => dest.UserName, opt => opt.Ignore())
            .ForMember(dest => dest.PasswordHash, opt => opt.Ignore())
            .ForMember(dest => dest.CompanyId, opt => opt.Ignore())
            .ForMember(dest => dest.CreatedAt, opt => opt.Ignore())
            .ForMember(dest => dest.UpdatedAt, opt => opt.Ignore())
            .ForMember(dest => dest.RefreshToken, opt => opt.Ignore())
            .ForMember(dest => dest.RefreshTokenExpiryTime, opt => opt.Ignore())
            .ForMember(dest => dest.LastLoginDate, opt => opt.Ignore());
    }

    /// <summary>
    /// Create authentication-related mappings
    /// إنشاء تعيينات متعلقة بالمصادقة
    /// </summary>
    private void CreateAuthMappings()
    {
        CreateMap<User, UserInfoDto>()
            .ForMember(dest => dest.Role, opt => opt.MapFrom(src => src.Role.Name))
            .ForMember(dest => dest.CompanyName, opt => opt.MapFrom(src => src.Company.Name))
            .ForMember(dest => dest.Permissions, opt => opt.MapFrom(src => src.Role.RolePermissions));

        CreateMap<RolePermission, PermissionDto>();
    }

    /// <summary>
    /// Create invoice-related mappings
    /// إنشاء تعيينات متعلقة بالفاتورة
    /// </summary>
    private void CreateInvoiceMappings()
    {
        CreateMap<Invoice, InvoiceDto>()
            .ForMember(dest => dest.PersonName, opt => opt.MapFrom(src => src.Person.Name));

        CreateMap<InvoiceDetail, InvoiceDetailDto>()
            .ForMember(dest => dest.ItemName, opt => opt.MapFrom(src => src.Item.Name))
            .ForMember(dest => dest.ItemUnit, opt => opt.MapFrom(src => src.Item.UnitOfMeasurement));

        CreateMap<CreateInvoiceDto, Invoice>()
            .ForMember(dest => dest.Id, opt => opt.Ignore())
            .ForMember(dest => dest.InvoiceNumber, opt => opt.Ignore()) // Will be generated by service
            .ForMember(dest => dest.TotalAmount, opt => opt.Ignore()) // Will be calculated
            .ForMember(dest => dest.Status, opt => opt.MapFrom(src => "Draft"))
            .ForMember(dest => dest.CreatedAt, opt => opt.Ignore())
            .ForMember(dest => dest.UpdatedAt, opt => opt.Ignore())
            .ForMember(dest => dest.InvoiceDetails, opt => opt.MapFrom(src => src.InvoiceDetails));

        CreateMap<CreateInvoiceDetailDto, InvoiceDetail>()
            .ForMember(dest => dest.Id, opt => opt.Ignore())
            .ForMember(dest => dest.InvoiceId, opt => opt.Ignore())
            .ForMember(dest => dest.LineTotal, opt => opt.Ignore()) // Will be calculated
            .ForMember(dest => dest.CreatedAt, opt => opt.Ignore())
            .ForMember(dest => dest.UpdatedAt, opt => opt.Ignore());
    }

    /// <summary>
    /// Create common entity mappings
    /// إنشاء تعيينات الكيانات المشتركة
    /// </summary>
    private void CreateCommonMappings()
    {
        // Department mappings
        CreateMap<Department, DTOs.Common.BaseDto>();
        CreateMap<DTOs.Common.BaseDto, Department>()
            .ForMember(dest => dest.CreatedAt, opt => opt.Ignore())
            .ForMember(dest => dest.UpdatedAt, opt => opt.Ignore());

        // Partner mappings
        CreateMap<Partner, DTOs.Common.BaseDto>();
        CreateMap<DTOs.Common.BaseDto, Partner>()
            .ForMember(dest => dest.CreatedAt, opt => opt.Ignore())
            .ForMember(dest => dest.UpdatedAt, opt => opt.Ignore());

        // MainCash mappings
        CreateMap<MainCash, DTOs.Common.BaseDto>();
        CreateMap<DTOs.Common.BaseDto, MainCash>()
            .ForMember(dest => dest.CreatedAt, opt => opt.Ignore())
            .ForMember(dest => dest.UpdatedAt, opt => opt.Ignore());

        // FinancialTransaction mappings
        CreateMap<FinancialTransaction, DTOs.Common.BaseDto>();
        CreateMap<DTOs.Common.BaseDto, FinancialTransaction>()
            .ForMember(dest => dest.CreatedAt, opt => opt.Ignore())
            .ForMember(dest => dest.UpdatedAt, opt => opt.Ignore());

        // Warehouse mappings
        CreateMap<Warehouse, DTOs.Common.BaseDto>();
        CreateMap<DTOs.Common.BaseDto, Warehouse>()
            .ForMember(dest => dest.CreatedAt, opt => opt.Ignore())
            .ForMember(dest => dest.UpdatedAt, opt => opt.Ignore());

        // Item mappings
        CreateMap<Item, DTOs.Common.BaseDto>();
        CreateMap<DTOs.Common.BaseDto, Item>()
            .ForMember(dest => dest.CreatedAt, opt => opt.Ignore())
            .ForMember(dest => dest.UpdatedAt, opt => opt.Ignore());

        // InventoryTransaction mappings
        CreateMap<InventoryTransaction, DTOs.Common.BaseDto>();
        CreateMap<DTOs.Common.BaseDto, InventoryTransaction>()
            .ForMember(dest => dest.CreatedAt, opt => opt.Ignore())
            .ForMember(dest => dest.UpdatedAt, opt => opt.Ignore());

        // Person mappings
        CreateMap<Person, DTOs.Common.BaseDto>();
        CreateMap<DTOs.Common.BaseDto, Person>()
            .ForMember(dest => dest.CreatedAt, opt => opt.Ignore())
            .ForMember(dest => dest.UpdatedAt, opt => opt.Ignore());

        // Role mappings
        CreateMap<Role, DTOs.Common.BaseDto>();
        CreateMap<DTOs.Common.BaseDto, Role>()
            .ForMember(dest => dest.CreatedAt, opt => opt.Ignore())
            .ForMember(dest => dest.UpdatedAt, opt => opt.Ignore());

        // RolePermission mappings
        CreateMap<RolePermission, DTOs.Common.BaseDto>();
        CreateMap<DTOs.Common.BaseDto, RolePermission>()
            .ForMember(dest => dest.CreatedAt, opt => opt.Ignore())
            .ForMember(dest => dest.UpdatedAt, opt => opt.Ignore());
    }
}
