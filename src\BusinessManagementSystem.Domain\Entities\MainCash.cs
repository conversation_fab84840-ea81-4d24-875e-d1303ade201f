using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using BusinessManagementSystem.Domain.Common;

namespace BusinessManagementSystem.Domain.Entities;

/// <summary>
/// Main cash entity - stores company's main cash balance
/// كيان الخزينة الرئيسية - يخزن رصيد الخزينة الرئيسية للشركة
/// </summary>
public class MainCash : BaseEntity
{
    /// <summary>
    /// Current cash balance
    /// الرصيد النقدي الحالي
    /// </summary>
    [Column(TypeName = "decimal(18,2)")]
    public decimal Balance { get; set; }

    /// <summary>
    /// Currency code (e.g., USD, SAR, EGP)
    /// رمز العملة (مثل: دولار أمريكي، ريال سعودي، جنيه مصري)
    /// </summary>
    [MaxLength(3)]
    public string Currency { get; set; } = "USD";

    /// <summary>
    /// Last update timestamp
    /// طابع زمني لآخر تحديث
    /// </summary>
    public DateTime LastUpdated { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// Company identifier (Foreign Key)
    /// معرف الشركة (مفتاح خارجي)
    /// </summary>
    [Required]
    public int CompanyId { get; set; }

    // Navigation properties
    [ForeignKey(nameof(CompanyId))]
    public virtual Company Company { get; set; } = null!;
    
    public virtual ICollection<FinancialTransaction> FinancialTransactions { get; set; } = new List<FinancialTransaction>();
}
