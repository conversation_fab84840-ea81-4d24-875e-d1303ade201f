using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using BusinessManagementSystem.Application.DTOs.User;
using BusinessManagementSystem.Application.Services.Interfaces;

namespace BusinessManagementSystem.API.Controllers;

/// <summary>
/// Users controller for user management operations
/// وحدة تحكم المستخدمين لعمليات إدارة المستخدمين
/// </summary>
[Authorize]
public class UsersController : BaseApiController
{
    private readonly IUserService _userService;
    private readonly ILogger<UsersController> _logger;

    public UsersController(IUserService userService, ILogger<UsersController> logger)
    {
        _userService = userService;
        _logger = logger;
    }

    /// <summary>
    /// Get all users
    /// الحصول على جميع المستخدمين
    /// </summary>
    [HttpGet]
    public async Task<IActionResult> GetAll(CancellationToken cancellationToken = default)
    {
        try
        {
            if (!HasPermission("Users", "View"))
            {
                return CreateErrorResponse("You don't have permission to view users.", 403);
            }

            var users = await _userService.GetAllAsync(cancellationToken);
            return CreateResponse(users, "Users retrieved successfully.");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving users");
            return CreateErrorResponse("An error occurred while retrieving users.", 500);
        }
    }

    /// <summary>
    /// Get user by ID
    /// الحصول على المستخدم بواسطة المعرف
    /// </summary>
    [HttpGet("{id}")]
    public async Task<IActionResult> GetById(int id, CancellationToken cancellationToken = default)
    {
        try
        {
            if (!HasPermission("Users", "View"))
            {
                return CreateErrorResponse("You don't have permission to view users.", 403);
            }

            var user = await _userService.GetByIdAsync(id, cancellationToken);
            if (user == null)
            {
                return CreateErrorResponse("User not found.", 404);
            }

            return CreateResponse(user, "User retrieved successfully.");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving user with ID: {UserId}", id);
            return CreateErrorResponse("An error occurred while retrieving the user.", 500);
        }
    }

    /// <summary>
    /// Get users by company
    /// الحصول على المستخدمين بواسطة الشركة
    /// </summary>
    [HttpGet("company/{companyId}")]
    public async Task<IActionResult> GetByCompany(int companyId, CancellationToken cancellationToken = default)
    {
        try
        {
            if (!HasPermission("Users", "View"))
            {
                return CreateErrorResponse("You don't have permission to view users.", 403);
            }

            // Users can only view users from their own company unless they're administrators
            var currentCompanyId = GetCurrentCompanyId();
            var currentRole = GetCurrentUserRole();
            
            if (currentRole != "Administrator" && companyId != currentCompanyId)
            {
                return CreateErrorResponse("You can only view users from your own company.", 403);
            }

            var users = await _userService.GetByCompanyAsync(companyId, cancellationToken);
            return CreateResponse(users, "Users retrieved successfully.");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving users for company: {CompanyId}", companyId);
            return CreateErrorResponse("An error occurred while retrieving users.", 500);
        }
    }

    /// <summary>
    /// Create new user
    /// إنشاء مستخدم جديد
    /// </summary>
    [HttpPost]
    public async Task<IActionResult> Create([FromBody] CreateUserDto createUserDto, CancellationToken cancellationToken = default)
    {
        try
        {
            if (!HasPermission("Users", "Add"))
            {
                return CreateErrorResponse("You don't have permission to create users.", 403);
            }

            if (!ModelState.IsValid)
            {
                return CreateErrorResponse("Invalid user data.", 400);
            }

            var user = await _userService.CreateAsync(createUserDto, cancellationToken);
            
            _logger.LogInformation("User created successfully with ID: {UserId} by user: {CreatedBy}", 
                user.Id, GetCurrentUserId());
            
            return CreatedAtAction(nameof(GetById), new { id = user.Id }, 
                CreateResponse(user, "User created successfully."));
        }
        catch (ArgumentException ex)
        {
            _logger.LogWarning(ex, "Validation error creating user");
            return CreateErrorResponse(ex.Message, 400);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating user");
            return CreateErrorResponse("An error occurred while creating the user.", 500);
        }
    }

    /// <summary>
    /// Update existing user
    /// تحديث مستخدم موجود
    /// </summary>
    [HttpPut("{id}")]
    public async Task<IActionResult> Update(int id, [FromBody] UpdateUserDto updateUserDto, CancellationToken cancellationToken = default)
    {
        try
        {
            if (!HasPermission("Users", "Edit"))
            {
                return CreateErrorResponse("You don't have permission to edit users.", 403);
            }

            if (!ModelState.IsValid)
            {
                return CreateErrorResponse("Invalid user data.", 400);
            }

            var user = await _userService.UpdateAsync(id, updateUserDto, cancellationToken);
            
            _logger.LogInformation("User updated successfully with ID: {UserId} by user: {UpdatedBy}", 
                id, GetCurrentUserId());
            
            return CreateResponse(user, "User updated successfully.");
        }
        catch (ArgumentException ex)
        {
            _logger.LogWarning(ex, "Validation error updating user with ID: {UserId}", id);
            return CreateErrorResponse(ex.Message, 400);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating user with ID: {UserId}", id);
            return CreateErrorResponse("An error occurred while updating the user.", 500);
        }
    }

    /// <summary>
    /// Change user password
    /// تغيير كلمة مرور المستخدم
    /// </summary>
    [HttpPost("{id}/change-password")]
    public async Task<IActionResult> ChangePassword(int id, [FromBody] ChangePasswordDto changePasswordDto, CancellationToken cancellationToken = default)
    {
        try
        {
            var currentUserId = GetCurrentUserId();
            
            // Users can only change their own password unless they're administrators
            if (id != currentUserId && !HasPermission("Users", "Edit"))
            {
                return CreateErrorResponse("You can only change your own password.", 403);
            }

            if (!ModelState.IsValid)
            {
                return CreateErrorResponse("Invalid password data.", 400);
            }

            var result = await _userService.ChangePasswordAsync(id, changePasswordDto, cancellationToken);
            if (!result)
            {
                return CreateErrorResponse("Failed to change password.", 400);
            }

            _logger.LogInformation("Password changed successfully for user ID: {UserId}", id);
            return CreateResponse<object?>(null, "Password changed successfully.");
        }
        catch (ArgumentException ex)
        {
            _logger.LogWarning(ex, "Validation error changing password for user ID: {UserId}", id);
            return CreateErrorResponse(ex.Message, 400);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error changing password for user ID: {UserId}", id);
            return CreateErrorResponse("An error occurred while changing the password.", 500);
        }
    }

    /// <summary>
    /// Activate or deactivate user
    /// تفعيل أو إلغاء تفعيل المستخدم
    /// </summary>
    [HttpPatch("{id}/status")]
    public async Task<IActionResult> SetActiveStatus(int id, [FromBody] bool isActive, CancellationToken cancellationToken = default)
    {
        try
        {
            if (!HasPermission("Users", "Edit"))
            {
                return CreateErrorResponse("You don't have permission to change user status.", 403);
            }

            var result = await _userService.SetActiveStatusAsync(id, isActive, cancellationToken);
            if (!result)
            {
                return CreateErrorResponse("User not found.", 404);
            }

            _logger.LogInformation("User status changed successfully for ID: {UserId} to {Status} by user: {UpdatedBy}", 
                id, isActive ? "Active" : "Inactive", GetCurrentUserId());
            
            return CreateResponse<object?>(null, $"User {(isActive ? "activated" : "deactivated")} successfully.");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error changing status for user ID: {UserId}", id);
            return CreateErrorResponse("An error occurred while changing user status.", 500);
        }
    }

    /// <summary>
    /// Delete user
    /// حذف المستخدم
    /// </summary>
    [HttpDelete("{id}")]
    public async Task<IActionResult> Delete(int id, CancellationToken cancellationToken = default)
    {
        try
        {
            if (!HasPermission("Users", "Delete"))
            {
                return CreateErrorResponse("You don't have permission to delete users.", 403);
            }

            var currentUserId = GetCurrentUserId();
            if (id == currentUserId)
            {
                return CreateErrorResponse("You cannot delete your own account.", 400);
            }

            var result = await _userService.DeleteAsync(id, cancellationToken);
            if (!result)
            {
                return CreateErrorResponse("User not found.", 404);
            }

            _logger.LogInformation("User deleted successfully with ID: {UserId} by user: {DeletedBy}", 
                id, currentUserId);
            
            return CreateResponse<object?>(null, "User deleted successfully.");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deleting user with ID: {UserId}", id);
            return CreateErrorResponse("An error occurred while deleting the user.", 500);
        }
    }

    /// <summary>
    /// Check if username exists
    /// التحقق من وجود اسم المستخدم
    /// </summary>
    [HttpGet("check-username/{username}")]
    public async Task<IActionResult> CheckUsernameExists(string username, [FromQuery] int? excludeId = null, CancellationToken cancellationToken = default)
    {
        try
        {
            var exists = await _userService.IsUsernameExistsAsync(username, excludeId, cancellationToken);
            return CreateResponse(new { Exists = exists }, "Username availability checked.");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error checking username existence: {Username}", username);
            return CreateErrorResponse("An error occurred while checking username availability.", 500);
        }
    }

    /// <summary>
    /// Check if email exists
    /// التحقق من وجود البريد الإلكتروني
    /// </summary>
    [HttpGet("check-email/{email}")]
    public async Task<IActionResult> CheckEmailExists(string email, [FromQuery] int? excludeId = null, CancellationToken cancellationToken = default)
    {
        try
        {
            var exists = await _userService.IsEmailExistsAsync(email, excludeId, cancellationToken);
            return CreateResponse(new { Exists = exists }, "Email availability checked.");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error checking email existence: {Email}", email);
            return CreateErrorResponse("An error occurred while checking email availability.", 500);
        }
    }
}
