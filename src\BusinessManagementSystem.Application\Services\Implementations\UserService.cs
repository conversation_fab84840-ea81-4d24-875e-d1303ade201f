using AutoMapper;
using BusinessManagementSystem.Application.DTOs.User;
using BusinessManagementSystem.Application.Interfaces;
using BusinessManagementSystem.Application.Services.Interfaces;
using BusinessManagementSystem.Domain.Entities;

namespace BusinessManagementSystem.Application.Services.Implementations;

/// <summary>
/// User service implementation for business logic operations
/// تنفيذ خدمة المستخدم لعمليات منطق الأعمال
/// </summary>
public class UserService : IUserService
{
    private readonly IUnitOfWork _unitOfWork;
    private readonly IMapper _mapper;
    private readonly IPasswordHashService _passwordHashService;

    public UserService(IUnitOfWork unitOfWork, IMapper mapper, IPasswordHashService passwordHashService)
    {
        _unitOfWork = unitOfWork;
        _mapper = mapper;
        _passwordHashService = passwordHashService;
    }

    public async Task<IEnumerable<UserDto>> GetAllAsync(CancellationToken cancellationToken = default)
    {
        var users = await _unitOfWork.Users.GetAsync(
            includeProperties: "Role,Company",
            cancellationToken: cancellationToken);
        return _mapper.Map<IEnumerable<UserDto>>(users);
    }

    public async Task<UserDto?> GetByIdAsync(int id, CancellationToken cancellationToken = default)
    {
        var user = await _unitOfWork.Users.GetWithRoleAsync(id, cancellationToken);
        return user != null ? _mapper.Map<UserDto>(user) : null;
    }

    public async Task<UserDto?> GetByUsernameAsync(string username, CancellationToken cancellationToken = default)
    {
        var user = await _unitOfWork.Users.GetByUsernameAsync(username, cancellationToken);
        return user != null ? _mapper.Map<UserDto>(user) : null;
    }

    public async Task<IEnumerable<UserDto>> GetByCompanyAsync(int companyId, CancellationToken cancellationToken = default)
    {
        var users = await _unitOfWork.Users.GetByCompanyAsync(companyId, cancellationToken);
        return _mapper.Map<IEnumerable<UserDto>>(users);
    }

    public async Task<IEnumerable<UserDto>> GetActiveByCompanyAsync(int companyId, CancellationToken cancellationToken = default)
    {
        var users = await _unitOfWork.Users.GetActiveByCompanyAsync(companyId, cancellationToken);
        return _mapper.Map<IEnumerable<UserDto>>(users);
    }

    public async Task<UserDto> CreateAsync(CreateUserDto createUserDto, CancellationToken cancellationToken = default)
    {
        // Validate business rules
        await ValidateUserForCreateAsync(createUserDto, cancellationToken);

        var user = _mapper.Map<User>(createUserDto);
        user.PasswordHash = _passwordHashService.HashPassword(createUserDto.Password);

        await _unitOfWork.Users.AddAsync(user, cancellationToken);
        await _unitOfWork.SaveChangesAsync(cancellationToken);

        return _mapper.Map<UserDto>(user);
    }

    public async Task<UserDto> UpdateAsync(int id, UpdateUserDto updateUserDto, CancellationToken cancellationToken = default)
    {
        var existingUser = await _unitOfWork.Users.GetByIdAsync(id, cancellationToken);
        if (existingUser == null)
        {
            throw new ArgumentException($"User with ID {id} not found.", nameof(id));
        }

        // Validate business rules
        await ValidateUserForUpdateAsync(id, updateUserDto, cancellationToken);

        _mapper.Map(updateUserDto, existingUser);
        _unitOfWork.Users.Update(existingUser);
        await _unitOfWork.SaveChangesAsync(cancellationToken);

        return _mapper.Map<UserDto>(existingUser);
    }

    public async Task<bool> ChangePasswordAsync(int userId, ChangePasswordDto changePasswordDto, CancellationToken cancellationToken = default)
    {
        var user = await _unitOfWork.Users.GetByIdAsync(userId, cancellationToken);
        if (user == null)
        {
            return false;
        }

        // Verify current password
        if (!_passwordHashService.VerifyPassword(changePasswordDto.CurrentPassword, user.PasswordHash))
        {
            throw new ArgumentException("Current password is incorrect.");
        }

        // Validate new password confirmation
        if (changePasswordDto.NewPassword != changePasswordDto.ConfirmNewPassword)
        {
            throw new ArgumentException("New password and confirmation do not match.");
        }

        // Update password
        user.PasswordHash = _passwordHashService.HashPassword(changePasswordDto.NewPassword);
        _unitOfWork.Users.Update(user);
        await _unitOfWork.SaveChangesAsync(cancellationToken);

        return true;
    }

    public async Task<bool> SetActiveStatusAsync(int id, bool isActive, CancellationToken cancellationToken = default)
    {
        var user = await _unitOfWork.Users.GetByIdAsync(id, cancellationToken);
        if (user == null)
        {
            return false;
        }

        user.IsActive = isActive;
        _unitOfWork.Users.Update(user);
        await _unitOfWork.SaveChangesAsync(cancellationToken);

        return true;
    }

    public async Task<bool> DeleteAsync(int id, CancellationToken cancellationToken = default)
    {
        var user = await _unitOfWork.Users.GetByIdAsync(id, cancellationToken);
        if (user == null)
        {
            return false;
        }

        // Check if user has related data that prevents deletion
        // Add business logic here if needed

        _unitOfWork.Users.Delete(user);
        await _unitOfWork.SaveChangesAsync(cancellationToken);
        return true;
    }

    public async Task<bool> IsUsernameExistsAsync(string username, int? excludeId = null, CancellationToken cancellationToken = default)
    {
        var user = await _unitOfWork.Users.GetByUsernameAsync(username, cancellationToken);
        return user != null && (excludeId == null || user.Id != excludeId);
    }

    public async Task<bool> IsEmailExistsAsync(string email, int? excludeId = null, CancellationToken cancellationToken = default)
    {
        if (string.IsNullOrWhiteSpace(email))
            return false;

        var user = await _unitOfWork.Users.GetByEmailAsync(email, cancellationToken);
        return user != null && (excludeId == null || user.Id != excludeId);
    }

    public async Task<bool> VerifyPasswordAsync(int userId, string password, CancellationToken cancellationToken = default)
    {
        var user = await _unitOfWork.Users.GetByIdAsync(userId, cancellationToken);
        if (user == null)
        {
            return false;
        }

        return _passwordHashService.VerifyPassword(password, user.PasswordHash);
    }

    public async Task UpdateLastLoginAsync(int userId, CancellationToken cancellationToken = default)
    {
        await _unitOfWork.Users.UpdateLastLoginAsync(userId, cancellationToken);
        await _unitOfWork.SaveChangesAsync(cancellationToken);
    }

    private async Task ValidateUserForCreateAsync(CreateUserDto createUserDto, CancellationToken cancellationToken)
    {
        if (await IsUsernameExistsAsync(createUserDto.Username, cancellationToken: cancellationToken))
        {
            throw new ArgumentException("Username already exists.", nameof(createUserDto.Username));
        }

        if (!string.IsNullOrWhiteSpace(createUserDto.Email) &&
            await IsEmailExistsAsync(createUserDto.Email, cancellationToken: cancellationToken))
        {
            throw new ArgumentException("Email already exists.", nameof(createUserDto.Email));
        }

        // Validate role exists
        var role = await _unitOfWork.Roles.GetByIdAsync(createUserDto.RoleId, cancellationToken);
        if (role == null)
        {
            throw new ArgumentException("Invalid role ID.", nameof(createUserDto.RoleId));
        }

        // Validate company exists
        var company = await _unitOfWork.Companies.GetByIdAsync(createUserDto.CompanyId, cancellationToken);
        if (company == null)
        {
            throw new ArgumentException("Invalid company ID.", nameof(createUserDto.CompanyId));
        }
    }

    private async Task ValidateUserForUpdateAsync(int id, UpdateUserDto updateUserDto, CancellationToken cancellationToken)
    {
        if (!string.IsNullOrWhiteSpace(updateUserDto.Email) &&
            await IsEmailExistsAsync(updateUserDto.Email, id, cancellationToken))
        {
            throw new ArgumentException("Email already exists.", nameof(updateUserDto.Email));
        }

        // Validate role exists
        var role = await _unitOfWork.Roles.GetByIdAsync(updateUserDto.RoleId, cancellationToken);
        if (role == null)
        {
            throw new ArgumentException("Invalid role ID.", nameof(updateUserDto.RoleId));
        }
    }
}
