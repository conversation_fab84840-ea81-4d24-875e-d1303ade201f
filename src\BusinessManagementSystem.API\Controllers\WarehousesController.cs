using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using BusinessManagementSystem.Application.DTOs.Warehouse;
using BusinessManagementSystem.Application.Services.Interfaces;
using Microsoft.Extensions.Logging;

namespace BusinessManagementSystem.API.Controllers;

/// <summary>
/// Warehouses controller for warehouse management operations
/// تحكم المستودعات لعمليات إدارة المستودعات
/// </summary>
[ApiController]
[Route("api/[controller]")]
[Authorize]
public class WarehousesController : BaseApiController
{
    private readonly IWarehouseService _warehouseService;

    public WarehousesController(IWarehouseService warehouseService, ILogger<WarehousesController> logger) : base(logger)
    {
        _warehouseService = warehouseService;
    }

    /// <summary>
    /// Get all warehouses
    /// الحصول على جميع المستودعات
    /// </summary>
    [HttpGet]
    [Authorize(Roles = "Administrator,Manager,Employee")]
    public async Task<ActionResult<IEnumerable<WarehouseDto>>> GetAll(CancellationToken cancellationToken = default)
    {
        try
        {
            var warehouses = await _warehouseService.GetAllAsync(cancellationToken);
            return Ok(warehouses);
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error occurred while getting all warehouses");
            return StatusCode(500, "An error occurred while processing your request.");
        }
    }

    /// <summary>
    /// Get warehouse by ID
    /// الحصول على المستودع بواسطة المعرف
    /// </summary>
    [HttpGet("{id}")]
    [Authorize(Roles = "Administrator,Manager,Employee")]
    public async Task<ActionResult<WarehouseDto>> GetById(int id, CancellationToken cancellationToken = default)
    {
        try
        {
            var warehouse = await _warehouseService.GetByIdAsync(id, cancellationToken);
            if (warehouse == null)
            {
                return NotFound($"Warehouse with ID {id} not found.");
            }

            return Ok(warehouse);
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error occurred while getting warehouse with ID: {WarehouseId}", id);
            return StatusCode(500, "An error occurred while processing your request.");
        }
    }

    /// <summary>
    /// Get warehouses by company
    /// الحصول على المستودعات بواسطة الشركة
    /// </summary>
    [HttpGet("company/{companyId}")]
    [Authorize(Roles = "Administrator,Manager,Employee")]
    public async Task<ActionResult<IEnumerable<WarehouseDto>>> GetByCompany(int companyId, CancellationToken cancellationToken = default)
    {
        try
        {
            // Check if user has access to this company
            if (!await HasCompanyAccessAsync(companyId))
            {
                return Forbid("You don't have access to this company's data.");
            }

            var warehouses = await _warehouseService.GetByCompanyAsync(companyId, cancellationToken);
            return Ok(warehouses);
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error occurred while getting warehouses for company: {CompanyId}", companyId);
            return StatusCode(500, "An error occurred while processing your request.");
        }
    }

    /// <summary>
    /// Get active warehouses by company
    /// الحصول على المستودعات النشطة بواسطة الشركة
    /// </summary>
    [HttpGet("company/{companyId}/active")]
    [Authorize(Roles = "Administrator,Manager,Employee")]
    public async Task<ActionResult<IEnumerable<WarehouseDto>>> GetActiveByCompany(int companyId, CancellationToken cancellationToken = default)
    {
        try
        {
            if (!await HasCompanyAccessAsync(companyId))
            {
                return Forbid("You don't have access to this company's data.");
            }

            var warehouses = await _warehouseService.GetActiveByCompanyAsync(companyId, cancellationToken);
            return Ok(warehouses);
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error occurred while getting active warehouses for company: {CompanyId}", companyId);
            return StatusCode(500, "An error occurred while processing your request.");
        }
    }

    /// <summary>
    /// Search warehouses
    /// البحث في المستودعات
    /// </summary>
    [HttpGet("search")]
    [Authorize(Roles = "Administrator,Manager,Employee")]
    public async Task<ActionResult<IEnumerable<WarehouseDto>>> Search([FromQuery] int companyId, [FromQuery] string searchTerm, CancellationToken cancellationToken = default)
    {
        try
        {
            if (!await HasCompanyAccessAsync(companyId))
            {
                return Forbid("You don't have access to this company's data.");
            }

            if (string.IsNullOrWhiteSpace(searchTerm))
            {
                return BadRequest("Search term is required.");
            }

            var warehouses = await _warehouseService.SearchAsync(companyId, searchTerm, cancellationToken);
            return Ok(warehouses);
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error occurred while searching warehouses for company: {CompanyId}, term: {SearchTerm}", companyId, searchTerm);
            return StatusCode(500, "An error occurred while processing your request.");
        }
    }

    /// <summary>
    /// Get warehouses with low capacity
    /// الحصول على المستودعات ذات السعة المنخفضة
    /// </summary>
    [HttpGet("low-capacity")]
    [Authorize(Roles = "Administrator,Manager")]
    public async Task<ActionResult<IEnumerable<WarehouseDto>>> GetLowCapacity([FromQuery] int companyId, [FromQuery] decimal threshold = 90, CancellationToken cancellationToken = default)
    {
        try
        {
            if (!await HasCompanyAccessAsync(companyId))
            {
                return Forbid("You don't have access to this company's data.");
            }

            var warehouses = await _warehouseService.GetLowCapacityWarehousesAsync(companyId, threshold, cancellationToken);
            return Ok(warehouses);
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error occurred while getting low capacity warehouses for company: {CompanyId}", companyId);
            return StatusCode(500, "An error occurred while processing your request.");
        }
    }

    /// <summary>
    /// Create new warehouse
    /// إنشاء مستودع جديد
    /// </summary>
    [HttpPost]
    [Authorize(Roles = "Administrator,Manager")]
    public async Task<ActionResult<WarehouseDto>> Create([FromBody] CreateWarehouseDto createWarehouseDto, CancellationToken cancellationToken = default)
    {
        try
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ModelState);
            }

            if (!await HasCompanyAccessAsync(createWarehouseDto.CompanyId))
            {
                return Forbid("You don't have access to this company's data.");
            }

            var warehouse = await _warehouseService.CreateAsync(createWarehouseDto, cancellationToken);
            
            Logger.LogInformation("Warehouse created successfully with ID: {WarehouseId} by user: {UserId}", warehouse.Id, GetCurrentUserId());
            return CreatedAtAction(nameof(GetById), new { id = warehouse.Id }, warehouse);
        }
        catch (ArgumentException ex)
        {
            Logger.LogWarning(ex, "Invalid argument while creating warehouse");
            return BadRequest(ex.Message);
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error occurred while creating warehouse");
            return StatusCode(500, "An error occurred while processing your request.");
        }
    }

    /// <summary>
    /// Update existing warehouse
    /// تحديث مستودع موجود
    /// </summary>
    [HttpPut("{id}")]
    [Authorize(Roles = "Administrator,Manager")]
    public async Task<ActionResult<WarehouseDto>> Update(int id, [FromBody] UpdateWarehouseDto updateWarehouseDto, CancellationToken cancellationToken = default)
    {
        try
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ModelState);
            }

            var existingWarehouse = await _warehouseService.GetByIdAsync(id, cancellationToken);
            if (existingWarehouse == null)
            {
                return NotFound($"Warehouse with ID {id} not found.");
            }

            if (!await HasCompanyAccessAsync(existingWarehouse.CompanyId))
            {
                return Forbid("You don't have access to this company's data.");
            }

            var warehouse = await _warehouseService.UpdateAsync(id, updateWarehouseDto, cancellationToken);
            
            Logger.LogInformation("Warehouse updated successfully with ID: {WarehouseId} by user: {UserId}", id, GetCurrentUserId());
            return Ok(warehouse);
        }
        catch (ArgumentException ex)
        {
            Logger.LogWarning(ex, "Invalid argument while updating warehouse with ID: {WarehouseId}", id);
            return BadRequest(ex.Message);
        }
        catch (InvalidOperationException ex)
        {
            Logger.LogWarning(ex, "Invalid operation while updating warehouse with ID: {WarehouseId}", id);
            return BadRequest(ex.Message);
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error occurred while updating warehouse with ID: {WarehouseId}", id);
            return StatusCode(500, "An error occurred while processing your request.");
        }
    }

    /// <summary>
    /// Set warehouse active status
    /// تعيين حالة تفعيل المستودع
    /// </summary>
    [HttpPatch("{id}/status")]
    [Authorize(Roles = "Administrator,Manager")]
    public async Task<ActionResult> SetActiveStatus(int id, [FromBody] bool isActive, CancellationToken cancellationToken = default)
    {
        try
        {
            var existingWarehouse = await _warehouseService.GetByIdAsync(id, cancellationToken);
            if (existingWarehouse == null)
            {
                return NotFound($"Warehouse with ID {id} not found.");
            }

            if (!await HasCompanyAccessAsync(existingWarehouse.CompanyId))
            {
                return Forbid("You don't have access to this company's data.");
            }

            var result = await _warehouseService.SetActiveStatusAsync(id, isActive, cancellationToken);
            if (!result)
            {
                return NotFound($"Warehouse with ID {id} not found.");
            }

            Logger.LogInformation("Warehouse status changed to {Status} for ID: {WarehouseId} by user: {UserId}", 
                isActive ? "Active" : "Inactive", id, GetCurrentUserId());
            return NoContent();
        }
        catch (InvalidOperationException ex)
        {
            Logger.LogWarning(ex, "Invalid operation while changing warehouse status for ID: {WarehouseId}", id);
            return BadRequest(ex.Message);
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error occurred while changing warehouse status for ID: {WarehouseId}", id);
            return StatusCode(500, "An error occurred while processing your request.");
        }
    }

    /// <summary>
    /// Delete warehouse
    /// حذف المستودع
    /// </summary>
    [HttpDelete("{id}")]
    [Authorize(Roles = "Administrator")]
    public async Task<ActionResult> Delete(int id, CancellationToken cancellationToken = default)
    {
        try
        {
            var existingWarehouse = await _warehouseService.GetByIdAsync(id, cancellationToken);
            if (existingWarehouse == null)
            {
                return NotFound($"Warehouse with ID {id} not found.");
            }

            if (!await HasCompanyAccessAsync(existingWarehouse.CompanyId))
            {
                return Forbid("You don't have access to this company's data.");
            }

            var result = await _warehouseService.DeleteAsync(id, cancellationToken);
            if (!result)
            {
                return NotFound($"Warehouse with ID {id} not found.");
            }

            Logger.LogInformation("Warehouse deleted successfully with ID: {WarehouseId} by user: {UserId}", id, GetCurrentUserId());
            return NoContent();
        }
        catch (InvalidOperationException ex)
        {
            Logger.LogWarning(ex, "Invalid operation while deleting warehouse with ID: {WarehouseId}", id);
            return BadRequest(ex.Message);
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error occurred while deleting warehouse with ID: {WarehouseId}", id);
            return StatusCode(500, "An error occurred while processing your request.");
        }
    }

    /// <summary>
    /// Transfer items between warehouses
    /// نقل الأصناف بين المستودعات
    /// </summary>
    [HttpPost("transfer")]
    [Authorize(Roles = "Administrator,Manager")]
    public async Task<ActionResult> TransferItems([FromBody] WarehouseTransferDto transferDto, CancellationToken cancellationToken = default)
    {
        try
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ModelState);
            }

            var result = await _warehouseService.TransferItemsAsync(transferDto, cancellationToken);
            if (!result)
            {
                return BadRequest("Transfer failed. Please check the transfer details.");
            }

            Logger.LogInformation("Items transferred successfully from warehouse {FromId} to {ToId} by user: {UserId}", 
                transferDto.FromWarehouseId, transferDto.ToWarehouseId, GetCurrentUserId());
            return Ok(new { Message = "Items transferred successfully." });
        }
        catch (ArgumentException ex)
        {
            Logger.LogWarning(ex, "Invalid argument while transferring items");
            return BadRequest(ex.Message);
        }
        catch (InvalidOperationException ex)
        {
            Logger.LogWarning(ex, "Invalid operation while transferring items");
            return BadRequest(ex.Message);
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error occurred while transferring items");
            return StatusCode(500, "An error occurred while processing your request.");
        }
    }

    /// <summary>
    /// Get warehouse capacity report
    /// الحصول على تقرير سعة المستودع
    /// </summary>
    [HttpGet("{id}/capacity-report")]
    [Authorize(Roles = "Administrator,Manager")]
    public async Task<ActionResult<WarehouseCapacityReportDto>> GetCapacityReport(int id, CancellationToken cancellationToken = default)
    {
        try
        {
            var existingWarehouse = await _warehouseService.GetByIdAsync(id, cancellationToken);
            if (existingWarehouse == null)
            {
                return NotFound($"Warehouse with ID {id} not found.");
            }

            if (!await HasCompanyAccessAsync(existingWarehouse.CompanyId))
            {
                return Forbid("You don't have access to this company's data.");
            }

            var report = await _warehouseService.GetCapacityReportAsync(id, cancellationToken);
            return Ok(report);
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error occurred while getting capacity report for warehouse: {WarehouseId}", id);
            return StatusCode(500, "An error occurred while processing your request.");
        }
    }

    /// <summary>
    /// Get warehouse utilization summary
    /// الحصول على ملخص استخدام المستودع
    /// </summary>
    [HttpGet("utilization-summary")]
    [Authorize(Roles = "Administrator,Manager")]
    public async Task<ActionResult<object>> GetUtilizationSummary([FromQuery] int companyId, CancellationToken cancellationToken = default)
    {
        try
        {
            if (!await HasCompanyAccessAsync(companyId))
            {
                return Forbid("You don't have access to this company's data.");
            }

            var summary = await _warehouseService.GetUtilizationSummaryAsync(companyId, cancellationToken);
            return Ok(summary);
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error occurred while getting utilization summary for company: {CompanyId}", companyId);
            return StatusCode(500, "An error occurred while processing your request.");
        }
    }

    /// <summary>
    /// Get warehouse statistics
    /// الحصول على إحصائيات المستودع
    /// </summary>
    [HttpGet("{id}/statistics")]
    [Authorize(Roles = "Administrator,Manager")]
    public async Task<ActionResult<object>> GetStatistics(int id, CancellationToken cancellationToken = default)
    {
        try
        {
            var existingWarehouse = await _warehouseService.GetByIdAsync(id, cancellationToken);
            if (existingWarehouse == null)
            {
                return NotFound($"Warehouse with ID {id} not found.");
            }

            if (!await HasCompanyAccessAsync(existingWarehouse.CompanyId))
            {
                return Forbid("You don't have access to this company's data.");
            }

            var statistics = await _warehouseService.GetWarehouseStatisticsAsync(id, cancellationToken);
            return Ok(statistics);
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error occurred while getting statistics for warehouse: {WarehouseId}", id);
            return StatusCode(500, "An error occurred while processing your request.");
        }
    }
}
