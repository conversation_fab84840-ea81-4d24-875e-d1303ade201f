using AutoMapper;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using BusinessManagementSystem.Application.DTOs.Item;
using BusinessManagementSystem.Application.Interfaces;
using BusinessManagementSystem.Application.Services.Interfaces;
using BusinessManagementSystem.Domain.Entities;

namespace BusinessManagementSystem.Application.Services.Implementations;

/// <summary>
/// Item service implementation for business logic operations
/// تنفيذ خدمة الصنف لعمليات منطق الأعمال
/// </summary>
public class ItemService : IItemService
{
    private readonly IUnitOfWork _unitOfWork;
    private readonly IMapper _mapper;
    private readonly ILogger<ItemService> _logger;
    private readonly string _imageUploadPath;

    public ItemService(IUnitOfWork unitOfWork, IMapper mapper, ILogger<ItemService> logger)
    {
        _unitOfWork = unitOfWork;
        _mapper = mapper;
        _logger = logger;
        _imageUploadPath = Path.Combine(Directory.GetCurrentDirectory(), "wwwroot", "images", "items");
        
        // Ensure directory exists
        if (!Directory.Exists(_imageUploadPath))
        {
            Directory.CreateDirectory(_imageUploadPath);
        }
    }

    public async Task<IEnumerable<ItemDto>> GetAllAsync(CancellationToken cancellationToken = default)
    {
        var items = await _unitOfWork.Items.GetAsync(
            includeProperties: "Company",
            cancellationToken: cancellationToken);
        return _mapper.Map<IEnumerable<ItemDto>>(items);
    }

    public async Task<ItemDto?> GetByIdAsync(int id, CancellationToken cancellationToken = default)
    {
        var item = await _unitOfWork.Items.GetByIdAsync(id, cancellationToken);
        return item != null ? _mapper.Map<ItemDto>(item) : null;
    }

    public async Task<ItemDto?> GetByBarcodeAsync(string barcode, CancellationToken cancellationToken = default)
    {
        var items = await _unitOfWork.Items.GetAsync(
            filter: i => i.Barcode == barcode,
            cancellationToken: cancellationToken);
        var item = items.FirstOrDefault();
        return item != null ? _mapper.Map<ItemDto>(item) : null;
    }

    public async Task<IEnumerable<ItemDto>> GetByCompanyAsync(int companyId, CancellationToken cancellationToken = default)
    {
        var items = await _unitOfWork.Items.GetAsync(
            filter: i => i.CompanyId == companyId,
            includeProperties: "Company",
            cancellationToken: cancellationToken);
        return _mapper.Map<IEnumerable<ItemDto>>(items);
    }

    public async Task<IEnumerable<ItemDto>> GetByCategoryAsync(int companyId, string category, CancellationToken cancellationToken = default)
    {
        var items = await _unitOfWork.Items.GetAsync(
            filter: i => i.CompanyId == companyId && i.Category == category,
            includeProperties: "Company",
            cancellationToken: cancellationToken);
        return _mapper.Map<IEnumerable<ItemDto>>(items);
    }

    public async Task<IEnumerable<ItemDto>> SearchAsync(int companyId, string searchTerm, CancellationToken cancellationToken = default)
    {
        var items = await _unitOfWork.Items.GetAsync(
            filter: i => i.CompanyId == companyId &&
                        (i.Name.Contains(searchTerm) ||
                         (i.Description != null && i.Description.Contains(searchTerm)) ||
                         (i.Barcode != null && i.Barcode.Contains(searchTerm))),
            includeProperties: "Company",
            cancellationToken: cancellationToken);
        return _mapper.Map<IEnumerable<ItemDto>>(items);
    }

    public async Task<IEnumerable<ItemDto>> GetLowStockItemsAsync(int companyId, CancellationToken cancellationToken = default)
    {
        var items = await _unitOfWork.Items.GetAsync(
            filter: i => i.CompanyId == companyId && i.StockQuantity <= i.MinimumStockLevel,
            includeProperties: "Company",
            cancellationToken: cancellationToken);
        return _mapper.Map<IEnumerable<ItemDto>>(items);
    }

    public async Task<IEnumerable<ItemDto>> GetActiveByCompanyAsync(int companyId, CancellationToken cancellationToken = default)
    {
        var items = await _unitOfWork.Items.GetAsync(
            filter: i => i.CompanyId == companyId && i.IsActive,
            includeProperties: "Company",
            cancellationToken: cancellationToken);
        return _mapper.Map<IEnumerable<ItemDto>>(items);
    }

    public async Task<ItemDto> CreateAsync(CreateItemDto createItemDto, CancellationToken cancellationToken = default)
    {
        // Validate business rules
        await ValidateItemForCreateAsync(createItemDto, cancellationToken);

        var item = _mapper.Map<Item>(createItemDto);
        
        // Generate barcode if not provided
        if (string.IsNullOrEmpty(item.Barcode))
        {
            item.Barcode = await GenerateUniqueBarcode(cancellationToken);
        }

        await _unitOfWork.Items.AddAsync(item, cancellationToken);
        await _unitOfWork.SaveChangesAsync(cancellationToken);

        _logger.LogInformation("Item created successfully with ID: {ItemId}", item.Id);
        return _mapper.Map<ItemDto>(item);
    }

    public async Task<ItemDto> UpdateAsync(int id, UpdateItemDto updateItemDto, CancellationToken cancellationToken = default)
    {
        var existingItem = await _unitOfWork.Items.GetByIdAsync(id, cancellationToken);
        if (existingItem == null)
        {
            throw new ArgumentException($"Item with ID {id} not found.", nameof(id));
        }

        // Validate business rules
        await ValidateItemForUpdateAsync(id, updateItemDto, cancellationToken);

        _mapper.Map(updateItemDto, existingItem);
        _unitOfWork.Items.Update(existingItem);
        await _unitOfWork.SaveChangesAsync(cancellationToken);

        _logger.LogInformation("Item updated successfully with ID: {ItemId}", id);
        return _mapper.Map<ItemDto>(existingItem);
    }

    public async Task<string> UploadImageAsync(int id, IFormFile image, CancellationToken cancellationToken = default)
    {
        var item = await _unitOfWork.Items.GetByIdAsync(id, cancellationToken);
        if (item == null)
        {
            throw new ArgumentException($"Item with ID {id} not found.", nameof(id));
        }

        // Validate image
        ValidateImageFile(image);

        // Generate unique filename
        var fileExtension = Path.GetExtension(image.FileName);
        var fileName = $"{id}_{Guid.NewGuid()}{fileExtension}";
        var filePath = Path.Combine(_imageUploadPath, fileName);

        // Save image file
        using (var stream = new FileStream(filePath, FileMode.Create))
        {
            await image.CopyToAsync(stream, cancellationToken);
        }

        // Update item with image URL
        var imageUrl = $"/images/items/{fileName}";
        item.ImageUrl = imageUrl;
        _unitOfWork.Items.Update(item);
        await _unitOfWork.SaveChangesAsync(cancellationToken);

        _logger.LogInformation("Image uploaded successfully for item ID: {ItemId}", id);
        return imageUrl;
    }

    public async Task<string> GenerateBarcodeAsync(int id, CancellationToken cancellationToken = default)
    {
        var item = await _unitOfWork.Items.GetByIdAsync(id, cancellationToken);
        if (item == null)
        {
            throw new ArgumentException($"Item with ID {id} not found.", nameof(id));
        }

        var barcode = await GenerateUniqueBarcode(cancellationToken);
        item.Barcode = barcode;
        _unitOfWork.Items.Update(item);
        await _unitOfWork.SaveChangesAsync(cancellationToken);

        _logger.LogInformation("Barcode generated successfully for item ID: {ItemId}", id);
        return barcode;
    }

    public async Task<bool> SetActiveStatusAsync(int id, bool isActive, CancellationToken cancellationToken = default)
    {
        var item = await _unitOfWork.Items.GetByIdAsync(id, cancellationToken);
        if (item == null)
        {
            return false;
        }

        item.IsActive = isActive;
        _unitOfWork.Items.Update(item);
        await _unitOfWork.SaveChangesAsync(cancellationToken);

        _logger.LogInformation("Item status changed to {Status} for ID: {ItemId}", isActive ? "Active" : "Inactive", id);
        return true;
    }

    public async Task<bool> DeleteAsync(int id, CancellationToken cancellationToken = default)
    {
        var item = await _unitOfWork.Items.GetByIdAsync(id, cancellationToken);
        if (item == null)
        {
            return false;
        }

        // Check if item is used in any transactions or invoices
        var transactions = await _unitOfWork.InventoryTransactions.GetAsync(
            filter: t => t.ItemId == id, cancellationToken: cancellationToken);
        var invoiceDetails = await _unitOfWork.InvoiceDetails.GetAsync(
            filter: d => d.ItemId == id, cancellationToken: cancellationToken);

        var hasTransactions = transactions.Any();
        var hasInvoiceDetails = invoiceDetails.Any();

        if (hasTransactions || hasInvoiceDetails)
        {
            throw new InvalidOperationException("Cannot delete item that has associated transactions or invoice details.");
        }

        // Delete image file if exists
        if (!string.IsNullOrEmpty(item.ImageUrl))
        {
            var fileName = Path.GetFileName(item.ImageUrl);
            var filePath = Path.Combine(_imageUploadPath, fileName);
            if (File.Exists(filePath))
            {
                File.Delete(filePath);
            }
        }

        _unitOfWork.Items.Delete(item);
        await _unitOfWork.SaveChangesAsync(cancellationToken);

        _logger.LogInformation("Item deleted successfully with ID: {ItemId}", id);
        return true;
    }

    public async Task<bool> UpdateStockQuantityAsync(int id, decimal quantity, string operation, CancellationToken cancellationToken = default)
    {
        var item = await _unitOfWork.Items.GetByIdAsync(id, cancellationToken);
        if (item == null)
        {
            return false;
        }

        switch (operation.ToLower())
        {
            case "add":
                item.StockQuantity += quantity;
                break;
            case "subtract":
                if (item.StockQuantity < quantity)
                {
                    throw new InvalidOperationException("Insufficient stock quantity.");
                }
                item.StockQuantity -= quantity;
                break;
            case "set":
                item.StockQuantity = quantity;
                break;
            default:
                throw new ArgumentException("Invalid operation. Use 'add', 'subtract', or 'set'.", nameof(operation));
        }

        _unitOfWork.Items.Update(item);
        await _unitOfWork.SaveChangesAsync(cancellationToken);

        _logger.LogInformation("Stock quantity updated for item ID: {ItemId}, Operation: {Operation}, Quantity: {Quantity}", 
            id, operation, quantity);
        return true;
    }

    public async Task<bool> IsBarcodeExistsAsync(string barcode, int? excludeId = null, CancellationToken cancellationToken = default)
    {
        var items = await _unitOfWork.Items.GetAsync(
            filter: i => i.Barcode == barcode,
            cancellationToken: cancellationToken);
        var item = items.FirstOrDefault();
        return item != null && (excludeId == null || item.Id != excludeId);
    }

    public async Task<IEnumerable<string>> GetCategoriesAsync(int companyId, CancellationToken cancellationToken = default)
    {
        var items = await _unitOfWork.Items.GetAsync(
            filter: i => i.CompanyId == companyId && !string.IsNullOrEmpty(i.Category),
            cancellationToken: cancellationToken);
        return items.Select(i => i.Category!).Distinct().ToList();
    }

    public async Task<decimal> GetCurrentStockValueAsync(int companyId, CancellationToken cancellationToken = default)
    {
        var items = await _unitOfWork.Items.GetAsync(
            filter: i => i.CompanyId == companyId,
            cancellationToken: cancellationToken);
        return items.Sum(i => i.StockQuantity * i.CostPrice);
    }

    public async Task<IEnumerable<ItemDto>> GetItemsRequiringReorderAsync(int companyId, CancellationToken cancellationToken = default)
    {
        var items = await _unitOfWork.Items.GetAsync(
            filter: i => i.CompanyId == companyId && i.StockQuantity <= i.ReorderPoint,
            includeProperties: "Company",
            cancellationToken: cancellationToken);
        return _mapper.Map<IEnumerable<ItemDto>>(items);
    }

    private async Task ValidateItemForCreateAsync(CreateItemDto createItemDto, CancellationToken cancellationToken)
    {
        // Validate company exists
        var company = await _unitOfWork.Companies.GetByIdAsync(createItemDto.CompanyId, cancellationToken);
        if (company == null)
        {
            throw new ArgumentException("Invalid company ID.", nameof(createItemDto.CompanyId));
        }

        // Validate barcode uniqueness if provided
        if (!string.IsNullOrEmpty(createItemDto.Barcode) &&
            await IsBarcodeExistsAsync(createItemDto.Barcode, cancellationToken: cancellationToken))
        {
            throw new ArgumentException("Barcode already exists.", nameof(createItemDto.Barcode));
        }
    }

    private async Task ValidateItemForUpdateAsync(int id, UpdateItemDto updateItemDto, CancellationToken cancellationToken)
    {
        // Additional validation logic for updates can be added here
        await Task.CompletedTask;
    }

    private void ValidateImageFile(IFormFile image)
    {
        var allowedExtensions = new[] { ".jpg", ".jpeg", ".png", ".gif", ".bmp" };
        var fileExtension = Path.GetExtension(image.FileName).ToLowerInvariant();

        if (!allowedExtensions.Contains(fileExtension))
        {
            throw new ArgumentException("Invalid image format. Allowed formats: JPG, JPEG, PNG, GIF, BMP.");
        }

        if (image.Length > 5 * 1024 * 1024) // 5MB limit
        {
            throw new ArgumentException("Image size cannot exceed 5MB.");
        }
    }

    private async Task<string> GenerateUniqueBarcode(CancellationToken cancellationToken)
    {
        string barcode;
        do
        {
            // Generate 13-digit barcode (EAN-13 format)
            var random = new Random();
            barcode = random.Next(100000000, 999999999).ToString() + 
                     random.Next(1000, 9999).ToString();
        }
        while (await IsBarcodeExistsAsync(barcode, cancellationToken: cancellationToken));

        return barcode;
    }
}
