using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using BusinessManagementSystem.Domain.Entities;

namespace BusinessManagementSystem.Infrastructure.Data.Configurations;

/// <summary>
/// Entity configuration for Item
/// تكوين الكيان للصنف
/// </summary>
public class ItemConfiguration : IEntityTypeConfiguration<Item>
{
    public void Configure(EntityTypeBuilder<Item> builder)
    {
        builder.ToTable("Items");

        builder.HasKey(i => i.Id);

        builder.Property(i => i.Name)
            .IsRequired()
            .HasMaxLength(200);

        builder.Property(i => i.Barcode)
            .HasMaxLength(50);

        builder.Property(i => i.Description)
            .HasMaxLength(1000);

        builder.Property(i => i.ItemType)
            .IsRequired()
            .HasMaxLength(20);

        builder.Property(i => i.ImagePath)
            .HasMaxLength(500);

        builder.Property(i => i.UnitOfMeasurement)
            .IsRequired()
            .HasMaxLength(20);

        builder.Property(i => i.UnitPrice)
            .HasColumnType("decimal(18,4)");

        builder.Property(i => i.CostPrice)
            .HasColumnType("decimal(18,4)");

        builder.Property(i => i.StockQuantity)
            .HasColumnType("decimal(18,4)");

        builder.Property(i => i.MinimumStockLevel)
            .HasColumnType("decimal(18,4)");

        builder.Property(i => i.MaximumStockLevel)
            .HasColumnType("decimal(18,4)");

        builder.Property(i => i.ReorderPoint)
            .HasColumnType("decimal(18,4)");

        builder.Property(i => i.Category)
            .HasMaxLength(100);

        builder.Property(i => i.ImageUrl)
            .HasMaxLength(500);

        // Indexes
        builder.HasIndex(i => i.Barcode)
            .IsUnique()
            .HasFilter("[Barcode] IS NOT NULL");

        builder.HasIndex(i => new { i.CompanyId, i.Name });

        builder.HasIndex(i => new { i.CompanyId, i.ItemType });

        builder.HasIndex(i => new { i.CompanyId, i.Category });

        builder.HasIndex(i => new { i.CompanyId, i.IsActive });

        builder.HasIndex(i => i.StockQuantity);

        builder.HasIndex(i => i.ReorderPoint);

        // Relationships
        builder.HasOne(i => i.Company)
            .WithMany(c => c.Items)
            .HasForeignKey(i => i.CompanyId)
            .OnDelete(DeleteBehavior.Cascade);

        builder.HasMany(i => i.InventoryTransactions)
            .WithOne(it => it.Item)
            .HasForeignKey(it => it.ItemId)
            .OnDelete(DeleteBehavior.Restrict);

        builder.HasMany(i => i.InvoiceDetails)
            .WithOne(id => id.Item)
            .HasForeignKey(id => id.ItemId)
            .OnDelete(DeleteBehavior.Restrict);
    }
}
