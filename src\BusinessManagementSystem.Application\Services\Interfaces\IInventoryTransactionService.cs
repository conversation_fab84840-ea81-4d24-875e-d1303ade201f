using BusinessManagementSystem.Application.DTOs.InventoryTransaction;

namespace BusinessManagementSystem.Application.Services.Interfaces;

/// <summary>
/// Inventory Transaction service interface for business logic operations
/// واجهة خدمة معاملة المخزون لعمليات منطق الأعمال
/// </summary>
public interface IInventoryTransactionService
{
    /// <summary>
    /// Get all inventory transactions
    /// الحصول على جميع معاملات المخزون
    /// </summary>
    Task<IEnumerable<InventoryTransactionDto>> GetAllAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// Get inventory transaction by ID
    /// الحصول على معاملة المخزون بواسطة المعرف
    /// </summary>
    Task<InventoryTransactionDto?> GetByIdAsync(int id, CancellationToken cancellationToken = default);

    /// <summary>
    /// Get inventory transactions by company
    /// الحصول على معاملات المخزون بواسطة الشركة
    /// </summary>
    Task<IEnumerable<InventoryTransactionDto>> GetByCompanyAsync(int companyId, CancellationToken cancellationToken = default);

    /// <summary>
    /// Get inventory transactions by warehouse
    /// الحصول على معاملات المخزون بواسطة المستودع
    /// </summary>
    Task<IEnumerable<InventoryTransactionDto>> GetByWarehouseAsync(int warehouseId, CancellationToken cancellationToken = default);

    /// <summary>
    /// Get inventory transactions by item
    /// الحصول على معاملات المخزون بواسطة الصنف
    /// </summary>
    Task<IEnumerable<InventoryTransactionDto>> GetByItemAsync(int itemId, CancellationToken cancellationToken = default);

    /// <summary>
    /// Get inventory transactions by type
    /// الحصول على معاملات المخزون بواسطة النوع
    /// </summary>
    Task<IEnumerable<InventoryTransactionDto>> GetByTypeAsync(int companyId, string transactionType, CancellationToken cancellationToken = default);

    /// <summary>
    /// Get inventory transactions by date range
    /// الحصول على معاملات المخزون بواسطة نطاق التاريخ
    /// </summary>
    Task<IEnumerable<InventoryTransactionDto>> GetByDateRangeAsync(int companyId, DateTime startDate, DateTime endDate, CancellationToken cancellationToken = default);

    /// <summary>
    /// Get inventory transactions by invoice
    /// الحصول على معاملات المخزون بواسطة الفاتورة
    /// </summary>
    Task<IEnumerable<InventoryTransactionDto>> GetByInvoiceAsync(int invoiceId, CancellationToken cancellationToken = default);

    /// <summary>
    /// Search inventory transactions
    /// البحث في معاملات المخزون
    /// </summary>
    Task<IEnumerable<InventoryTransactionDto>> SearchAsync(int companyId, string searchTerm, CancellationToken cancellationToken = default);

    /// <summary>
    /// Create new inventory transaction
    /// إنشاء معاملة مخزون جديدة
    /// </summary>
    Task<InventoryTransactionDto> CreateAsync(CreateInventoryTransactionDto createInventoryTransactionDto, CancellationToken cancellationToken = default);

    /// <summary>
    /// Update existing inventory transaction
    /// تحديث معاملة مخزون موجودة
    /// </summary>
    Task<InventoryTransactionDto> UpdateAsync(int id, UpdateInventoryTransactionDto updateInventoryTransactionDto, CancellationToken cancellationToken = default);

    /// <summary>
    /// Delete inventory transaction
    /// حذف معاملة المخزون
    /// </summary>
    Task<bool> DeleteAsync(int id, CancellationToken cancellationToken = default);

    /// <summary>
    /// Process stock in transaction
    /// معالجة معاملة دخول المخزون
    /// </summary>
    Task<InventoryTransactionDto> ProcessStockInAsync(int itemId, int warehouseId, decimal quantity, decimal unitCost, string? notes = null, int? invoiceId = null, CancellationToken cancellationToken = default);

    /// <summary>
    /// Process stock out transaction
    /// معالجة معاملة خروج المخزون
    /// </summary>
    Task<InventoryTransactionDto> ProcessStockOutAsync(int itemId, int warehouseId, decimal quantity, string? notes = null, int? invoiceId = null, CancellationToken cancellationToken = default);

    /// <summary>
    /// Process stock adjustment transaction
    /// معالجة معاملة تعديل المخزون
    /// </summary>
    Task<InventoryTransactionDto> ProcessStockAdjustmentAsync(int itemId, int warehouseId, decimal adjustmentQuantity, string reason, CancellationToken cancellationToken = default);

    /// <summary>
    /// Process stock transfer between warehouses
    /// معالجة نقل المخزون بين المستودعات
    /// </summary>
    Task<(InventoryTransactionDto OutTransaction, InventoryTransactionDto InTransaction)> ProcessStockTransferAsync(int itemId, int fromWarehouseId, int toWarehouseId, decimal quantity, string? notes = null, CancellationToken cancellationToken = default);

    /// <summary>
    /// Get inventory movement report
    /// الحصول على تقرير حركة المخزون
    /// </summary>
    Task<InventoryMovementReportDto> GetMovementReportAsync(int companyId, DateTime startDate, DateTime endDate, CancellationToken cancellationToken = default);

    /// <summary>
    /// Get item transaction history
    /// الحصول على تاريخ معاملات الصنف
    /// </summary>
    Task<IEnumerable<InventoryTransactionDto>> GetItemTransactionHistoryAsync(int itemId, int? warehouseId = null, CancellationToken cancellationToken = default);

    /// <summary>
    /// Get warehouse transaction summary
    /// الحصول على ملخص معاملات المستودع
    /// </summary>
    Task<object> GetWarehouseTransactionSummaryAsync(int warehouseId, DateTime? startDate = null, DateTime? endDate = null, CancellationToken cancellationToken = default);

    /// <summary>
    /// Calculate current stock for item in warehouse
    /// حساب المخزون الحالي للصنف في المستودع
    /// </summary>
    Task<decimal> CalculateCurrentStockAsync(int itemId, int warehouseId, CancellationToken cancellationToken = default);

    /// <summary>
    /// Get stock valuation report
    /// الحصول على تقرير تقييم المخزون
    /// </summary>
    Task<object> GetStockValuationReportAsync(int companyId, DateTime? asOfDate = null, CancellationToken cancellationToken = default);

    /// <summary>
    /// Validate transaction before processing
    /// التحقق من صحة المعاملة قبل المعالجة
    /// </summary>
    Task<bool> ValidateTransactionAsync(CreateInventoryTransactionDto transactionDto, CancellationToken cancellationToken = default);

    /// <summary>
    /// Get transaction statistics
    /// الحصول على إحصائيات المعاملات
    /// </summary>
    Task<object> GetTransactionStatisticsAsync(int companyId, DateTime? startDate = null, DateTime? endDate = null, CancellationToken cancellationToken = default);

    /// <summary>
    /// Generate reference number for transaction
    /// إنشاء رقم مرجعي للمعاملة
    /// </summary>
    Task<string> GenerateReferenceNumberAsync(string transactionType, int companyId, CancellationToken cancellationToken = default);

    /// <summary>
    /// Reverse transaction (create opposite transaction)
    /// عكس المعاملة (إنشاء معاملة معاكسة)
    /// </summary>
    Task<InventoryTransactionDto> ReverseTransactionAsync(int transactionId, string reason, CancellationToken cancellationToken = default);
}
