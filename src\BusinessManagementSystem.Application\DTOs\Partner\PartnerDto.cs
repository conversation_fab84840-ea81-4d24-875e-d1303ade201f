using BusinessManagementSystem.Application.DTOs.Common;

namespace BusinessManagementSystem.Application.DTOs.Partner;

/// <summary>
/// Partner DTO for data transfer
/// DTO الشريك لنقل البيانات
/// </summary>
public class PartnerDto : BaseDto
{
    /// <summary>
    /// Partner name
    /// اسم الشريك
    /// </summary>
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// Partner email
    /// بريد الشريك الإلكتروني
    /// </summary>
    public string? Email { get; set; }

    /// <summary>
    /// Partner phone
    /// هاتف الشريك
    /// </summary>
    public string? Phone { get; set; }

    /// <summary>
    /// Partner address
    /// عنوان الشريك
    /// </summary>
    public string? Address { get; set; }

    /// <summary>
    /// Capital amount
    /// مبلغ رأس المال
    /// </summary>
    public decimal CapitalAmount { get; set; }

    /// <summary>
    /// Ownership percentage
    /// نسبة الملكية
    /// </summary>
    public decimal OwnershipPercentage { get; set; }

    /// <summary>
    /// Whether the partner is active
    /// ما إذا كان الشريك نشطًا
    /// </summary>
    public bool IsActive { get; set; }

    /// <summary>
    /// Company ID
    /// معرف الشركة
    /// </summary>
    public int CompanyId { get; set; }

    /// <summary>
    /// Company name
    /// اسم الشركة
    /// </summary>
    public string CompanyName { get; set; } = string.Empty;
}

/// <summary>
/// Create Partner DTO
/// DTO إنشاء الشريك
/// </summary>
public class CreatePartnerDto
{
    /// <summary>
    /// Partner name
    /// اسم الشريك
    /// </summary>
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// Partner email
    /// بريد الشريك الإلكتروني
    /// </summary>
    public string? Email { get; set; }

    /// <summary>
    /// Partner phone
    /// هاتف الشريك
    /// </summary>
    public string? Phone { get; set; }

    /// <summary>
    /// Partner address
    /// عنوان الشريك
    /// </summary>
    public string? Address { get; set; }

    /// <summary>
    /// Capital amount
    /// مبلغ رأس المال
    /// </summary>
    public decimal CapitalAmount { get; set; }

    /// <summary>
    /// Ownership percentage
    /// نسبة الملكية
    /// </summary>
    public decimal OwnershipPercentage { get; set; }

    /// <summary>
    /// Whether the partner is active
    /// ما إذا كان الشريك نشطًا
    /// </summary>
    public bool IsActive { get; set; } = true;

    /// <summary>
    /// Company ID
    /// معرف الشركة
    /// </summary>
    public int CompanyId { get; set; }
}

/// <summary>
/// Update Partner DTO
/// DTO تحديث الشريك
/// </summary>
public class UpdatePartnerDto
{
    /// <summary>
    /// Partner name
    /// اسم الشريك
    /// </summary>
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// Partner email
    /// بريد الشريك الإلكتروني
    /// </summary>
    public string? Email { get; set; }

    /// <summary>
    /// Partner phone
    /// هاتف الشريك
    /// </summary>
    public string? Phone { get; set; }

    /// <summary>
    /// Partner address
    /// عنوان الشريك
    /// </summary>
    public string? Address { get; set; }

    /// <summary>
    /// Capital amount
    /// مبلغ رأس المال
    /// </summary>
    public decimal CapitalAmount { get; set; }

    /// <summary>
    /// Ownership percentage
    /// نسبة الملكية
    /// </summary>
    public decimal OwnershipPercentage { get; set; }

    /// <summary>
    /// Whether the partner is active
    /// ما إذا كان الشريك نشطًا
    /// </summary>
    public bool IsActive { get; set; }
}

/// <summary>
/// Capital Contribution DTO
/// DTO مساهمة رأس المال
/// </summary>
public class CapitalContributionDto
{
    /// <summary>
    /// Contribution amount
    /// مبلغ المساهمة
    /// </summary>
    public decimal Amount { get; set; }

    /// <summary>
    /// Contribution description
    /// وصف المساهمة
    /// </summary>
    public string? Description { get; set; }

    /// <summary>
    /// Contribution date
    /// تاريخ المساهمة
    /// </summary>
    public DateTime ContributionDate { get; set; } = DateTime.UtcNow;
}

/// <summary>
/// Capital Withdrawal DTO
/// DTO سحب رأس المال
/// </summary>
public class CapitalWithdrawalDto
{
    /// <summary>
    /// Withdrawal amount
    /// مبلغ السحب
    /// </summary>
    public decimal Amount { get; set; }

    /// <summary>
    /// Withdrawal description
    /// وصف السحب
    /// </summary>
    public string? Description { get; set; }

    /// <summary>
    /// Withdrawal date
    /// تاريخ السحب
    /// </summary>
    public DateTime WithdrawalDate { get; set; } = DateTime.UtcNow;
}
