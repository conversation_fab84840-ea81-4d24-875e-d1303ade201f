using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;

namespace BusinessManagementSystem.API.Controllers;

/// <summary>
/// Base API controller with common functionality
/// وحدة تحكم API الأساسية مع الوظائف المشتركة
/// </summary>
[ApiController]
[Route("api/[controller]")]
[Produces("application/json")]
public abstract class BaseApiController : ControllerBase
{
    /// <summary>
    /// Logger instance for derived controllers
    /// مثيل المسجل للتحكمات المشتقة
    /// </summary>
    protected ILogger Logger { get; }

    /// <summary>
    /// Constructor with logger injection
    /// منشئ مع حقن المسجل
    /// </summary>
    protected BaseApiController(ILogger logger)
    {
        Logger = logger;
    }
    /// <summary>
    /// Get current user ID from JWT claims
    /// الحصول على معرف المستخدم الحالي من مطالبات JWT
    /// </summary>
    protected int GetCurrentUserId()
    {
        var userIdClaim = User.FindFirst("userId")?.Value;
        if (int.TryParse(userIdClaim, out int userId))
        {
            return userId;
        }
        throw new UnauthorizedAccessException("User ID not found in token.");
    }

    /// <summary>
    /// Get current company ID from JWT claims
    /// الحصول على معرف الشركة الحالية من مطالبات JWT
    /// </summary>
    protected int GetCurrentCompanyId()
    {
        var companyIdClaim = User.FindFirst("companyId")?.Value;
        if (int.TryParse(companyIdClaim, out int companyId))
        {
            return companyId;
        }
        throw new UnauthorizedAccessException("Company ID not found in token.");
    }

    /// <summary>
    /// Get current user role from JWT claims
    /// الحصول على دور المستخدم الحالي من مطالبات JWT
    /// </summary>
    protected string GetCurrentUserRole()
    {
        var roleClaim = User.FindFirst("role")?.Value;
        if (!string.IsNullOrEmpty(roleClaim))
        {
            return roleClaim;
        }
        throw new UnauthorizedAccessException("User role not found in token.");
    }

    /// <summary>
    /// Check if current user has specific permission
    /// التحقق من وجود صلاحية محددة للمستخدم الحالي
    /// </summary>
    protected bool HasPermission(string pageName, string action)
    {
        var permissionClaim = User.FindFirst($"permission_{pageName}_{action}")?.Value;
        return bool.TryParse(permissionClaim, out bool hasPermission) && hasPermission;
    }

    /// <summary>
    /// Check if current user has access to the specified company
    /// التحقق من وصول المستخدم الحالي للشركة المحددة
    /// </summary>
    protected async Task<bool> HasCompanyAccessAsync(int companyId)
    {
        try
        {
            var currentCompanyId = GetCurrentCompanyId();
            return currentCompanyId == companyId;
        }
        catch
        {
            return false;
        }
    }

    /// <summary>
    /// Create standardized API response
    /// إنشاء استجابة API موحدة
    /// </summary>
    protected IActionResult CreateResponse<T>(T data, string message = "", bool success = true)
    {
        var response = new ApiResponse<T>
        {
            Success = success,
            Message = message,
            Data = data
        };

        return success ? Ok(response) : BadRequest(response);
    }

    /// <summary>
    /// Create error response
    /// إنشاء استجابة خطأ
    /// </summary>
    protected IActionResult CreateErrorResponse(string message, int statusCode = 400)
    {
        var response = new ApiResponse<object>
        {
            Success = false,
            Message = message,
            Data = null
        };

        return StatusCode(statusCode, response);
    }
}

/// <summary>
/// Standard API response model
/// نموذج استجابة API المعياري
/// </summary>
public class ApiResponse<T>
{
    /// <summary>
    /// Indicates if the operation was successful
    /// يشير إلى ما إذا كانت العملية ناجحة
    /// </summary>
    public bool Success { get; set; }

    /// <summary>
    /// Response message
    /// رسالة الاستجابة
    /// </summary>
    public string Message { get; set; } = string.Empty;

    /// <summary>
    /// Response data
    /// بيانات الاستجابة
    /// </summary>
    public T? Data { get; set; }

    /// <summary>
    /// Timestamp of the response
    /// الطابع الزمني للاستجابة
    /// </summary>
    public DateTime Timestamp { get; set; } = DateTime.UtcNow;
}
