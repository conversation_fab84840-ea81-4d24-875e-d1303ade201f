using BusinessManagementSystem.Application.DTOs.Common;

namespace BusinessManagementSystem.Application.DTOs.FinancialTransaction;

/// <summary>
/// Financial Transaction DTO for data transfer
/// DTO المعاملة المالية لنقل البيانات
/// </summary>
public class FinancialTransactionDto : BaseDto
{
    /// <summary>
    /// Transaction date
    /// تاريخ المعاملة
    /// </summary>
    public DateTime TransactionDate { get; set; }

    /// <summary>
    /// Transaction type (Income, Expense, Transfer)
    /// نوع المعاملة (دخل، مصروف، تحويل)
    /// </summary>
    public string TransactionType { get; set; } = string.Empty;

    /// <summary>
    /// Transaction amount
    /// مبلغ المعاملة
    /// </summary>
    public decimal Amount { get; set; }

    /// <summary>
    /// Transaction description
    /// وصف المعاملة
    /// </summary>
    public string? Description { get; set; }

    /// <summary>
    /// Reference number
    /// الرقم المرجعي
    /// </summary>
    public string? ReferenceNumber { get; set; }

    /// <summary>
    /// Person ID (customer/supplier)
    /// معرف الشخص (عميل/مورد)
    /// </summary>
    public int? PersonId { get; set; }

    /// <summary>
    /// Person name
    /// اسم الشخص
    /// </summary>
    public string? PersonName { get; set; }

    /// <summary>
    /// Company ID
    /// معرف الشركة
    /// </summary>
    public int CompanyId { get; set; }

    /// <summary>
    /// Company name
    /// اسم الشركة
    /// </summary>
    public string CompanyName { get; set; } = string.Empty;

    /// <summary>
    /// Invoice ID (if related to an invoice)
    /// معرف الفاتورة (إذا كانت مرتبطة بفاتورة)
    /// </summary>
    public int? InvoiceId { get; set; }

    /// <summary>
    /// Invoice number
    /// رقم الفاتورة
    /// </summary>
    public string? InvoiceNumber { get; set; }
}

/// <summary>
/// Create Financial Transaction DTO
/// DTO إنشاء المعاملة المالية
/// </summary>
public class CreateFinancialTransactionDto
{
    /// <summary>
    /// Transaction date
    /// تاريخ المعاملة
    /// </summary>
    public DateTime TransactionDate { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// Transaction type (Income, Expense, Transfer)
    /// نوع المعاملة (دخل، مصروف، تحويل)
    /// </summary>
    public string TransactionType { get; set; } = string.Empty;

    /// <summary>
    /// Transaction amount
    /// مبلغ المعاملة
    /// </summary>
    public decimal Amount { get; set; }

    /// <summary>
    /// Transaction description
    /// وصف المعاملة
    /// </summary>
    public string? Description { get; set; }

    /// <summary>
    /// Reference number
    /// الرقم المرجعي
    /// </summary>
    public string? ReferenceNumber { get; set; }

    /// <summary>
    /// Person ID (customer/supplier)
    /// معرف الشخص (عميل/مورد)
    /// </summary>
    public int? PersonId { get; set; }

    /// <summary>
    /// Company ID
    /// معرف الشركة
    /// </summary>
    public int CompanyId { get; set; }

    /// <summary>
    /// Invoice ID (if related to an invoice)
    /// معرف الفاتورة (إذا كانت مرتبطة بفاتورة)
    /// </summary>
    public int? InvoiceId { get; set; }
}

/// <summary>
/// Update Financial Transaction DTO
/// DTO تحديث المعاملة المالية
/// </summary>
public class UpdateFinancialTransactionDto
{
    /// <summary>
    /// Transaction date
    /// تاريخ المعاملة
    /// </summary>
    public DateTime TransactionDate { get; set; }

    /// <summary>
    /// Transaction type (Income, Expense, Transfer)
    /// نوع المعاملة (دخل، مصروف، تحويل)
    /// </summary>
    public string TransactionType { get; set; } = string.Empty;

    /// <summary>
    /// Transaction amount
    /// مبلغ المعاملة
    /// </summary>
    public decimal Amount { get; set; }

    /// <summary>
    /// Transaction description
    /// وصف المعاملة
    /// </summary>
    public string? Description { get; set; }

    /// <summary>
    /// Reference number
    /// الرقم المرجعي
    /// </summary>
    public string? ReferenceNumber { get; set; }

    /// <summary>
    /// Person ID (customer/supplier)
    /// معرف الشخص (عميل/مورد)
    /// </summary>
    public int? PersonId { get; set; }
}

/// <summary>
/// Cash Flow Report DTO
/// DTO تقرير التدفق النقدي
/// </summary>
public class CashFlowReportDto
{
    /// <summary>
    /// Report period start date
    /// تاريخ بداية فترة التقرير
    /// </summary>
    public DateTime StartDate { get; set; }

    /// <summary>
    /// Report period end date
    /// تاريخ نهاية فترة التقرير
    /// </summary>
    public DateTime EndDate { get; set; }

    /// <summary>
    /// Total income
    /// إجمالي الدخل
    /// </summary>
    public decimal TotalIncome { get; set; }

    /// <summary>
    /// Total expenses
    /// إجمالي المصروفات
    /// </summary>
    public decimal TotalExpenses { get; set; }

    /// <summary>
    /// Net cash flow
    /// صافي التدفق النقدي
    /// </summary>
    public decimal NetCashFlow { get; set; }

    /// <summary>
    /// Opening balance
    /// الرصيد الافتتاحي
    /// </summary>
    public decimal OpeningBalance { get; set; }

    /// <summary>
    /// Closing balance
    /// الرصيد الختامي
    /// </summary>
    public decimal ClosingBalance { get; set; }

    /// <summary>
    /// Detailed transactions
    /// المعاملات التفصيلية
    /// </summary>
    public List<FinancialTransactionDto> Transactions { get; set; } = new();
}

/// <summary>
/// Balance Summary DTO
/// DTO ملخص الرصيد
/// </summary>
public class BalanceSummaryDto
{
    /// <summary>
    /// Current cash balance
    /// الرصيد النقدي الحالي
    /// </summary>
    public decimal CurrentBalance { get; set; }

    /// <summary>
    /// Total receivables
    /// إجمالي المستحقات
    /// </summary>
    public decimal TotalReceivables { get; set; }

    /// <summary>
    /// Total payables
    /// إجمالي المدفوعات
    /// </summary>
    public decimal TotalPayables { get; set; }

    /// <summary>
    /// Net worth
    /// صافي الثروة
    /// </summary>
    public decimal NetWorth { get; set; }

    /// <summary>
    /// Last updated
    /// آخر تحديث
    /// </summary>
    public DateTime LastUpdated { get; set; }
}

/// <summary>
/// Profit and Loss Report DTO
/// DTO تقرير الأرباح والخسائر
/// </summary>
public class ProfitLossReportDto
{
    /// <summary>
    /// Report period start date
    /// تاريخ بداية فترة التقرير
    /// </summary>
    public DateTime StartDate { get; set; }

    /// <summary>
    /// Report period end date
    /// تاريخ نهاية فترة التقرير
    /// </summary>
    public DateTime EndDate { get; set; }

    /// <summary>
    /// Total revenue
    /// إجمالي الإيرادات
    /// </summary>
    public decimal TotalRevenue { get; set; }

    /// <summary>
    /// Cost of goods sold
    /// تكلفة البضائع المباعة
    /// </summary>
    public decimal CostOfGoodsSold { get; set; }

    /// <summary>
    /// Gross profit
    /// إجمالي الربح
    /// </summary>
    public decimal GrossProfit { get; set; }

    /// <summary>
    /// Operating expenses
    /// المصروفات التشغيلية
    /// </summary>
    public decimal OperatingExpenses { get; set; }

    /// <summary>
    /// Net profit
    /// صافي الربح
    /// </summary>
    public decimal NetProfit { get; set; }

    /// <summary>
    /// Profit margin percentage
    /// نسبة هامش الربح
    /// </summary>
    public decimal ProfitMarginPercentage { get; set; }
}
