using AutoMapper;
using Microsoft.Extensions.Logging;
using BusinessManagementSystem.Application.DTOs.FinancialTransaction;
using BusinessManagementSystem.Application.Interfaces;
using BusinessManagementSystem.Application.Services.Interfaces;
using BusinessManagementSystem.Domain.Entities;

namespace BusinessManagementSystem.Application.Services.Implementations;

/// <summary>
/// Financial Transaction service implementation for business logic operations
/// تنفيذ خدمة المعاملة المالية لعمليات منطق الأعمال
/// </summary>
public class FinancialTransactionService : IFinancialTransactionService
{
    private readonly IUnitOfWork _unitOfWork;
    private readonly IMapper _mapper;
    private readonly ILogger<FinancialTransactionService> _logger;

    public FinancialTransactionService(IUnitOfWork unitOfWork, IMapper mapper, ILogger<FinancialTransactionService> logger)
    {
        _unitOfWork = unitOfWork;
        _mapper = mapper;
        _logger = logger;
    }

    public async Task<IEnumerable<FinancialTransactionDto>> GetAllAsync(CancellationToken cancellationToken = default)
    {
        var transactions = await _unitOfWork.FinancialTransactions.GetAsync(
            includeProperties: "Person,Company,Invoice",
            cancellationToken: cancellationToken);
        return _mapper.Map<IEnumerable<FinancialTransactionDto>>(transactions);
    }

    public async Task<FinancialTransactionDto?> GetByIdAsync(int id, CancellationToken cancellationToken = default)
    {
        var transaction = await _unitOfWork.FinancialTransactions.GetByIdAsync(id, cancellationToken);
        return transaction != null ? _mapper.Map<FinancialTransactionDto>(transaction) : null;
    }

    public async Task<IEnumerable<FinancialTransactionDto>> GetByCompanyAsync(int companyId, CancellationToken cancellationToken = default)
    {
        var transactions = await _unitOfWork.FinancialTransactions.GetAsync(
            filter: t => t.CompanyId == companyId,
            includeProperties: "Person,Company,Invoice",
            cancellationToken: cancellationToken);
        return _mapper.Map<IEnumerable<FinancialTransactionDto>>(transactions);
    }

    public async Task<IEnumerable<FinancialTransactionDto>> GetByTypeAsync(int companyId, string transactionType, CancellationToken cancellationToken = default)
    {
        var transactions = await _unitOfWork.FinancialTransactions.GetAsync(
            filter: t => t.CompanyId == companyId && t.TransactionType == transactionType,
            includeProperties: "Person,Company,Invoice",
            cancellationToken: cancellationToken);
        return _mapper.Map<IEnumerable<FinancialTransactionDto>>(transactions);
    }

    public async Task<IEnumerable<FinancialTransactionDto>> GetByPersonAsync(int personId, CancellationToken cancellationToken = default)
    {
        var transactions = await _unitOfWork.FinancialTransactions.GetAsync(
            filter: t => t.PersonId == personId,
            includeProperties: "Person,Company,Invoice",
            cancellationToken: cancellationToken);
        return _mapper.Map<IEnumerable<FinancialTransactionDto>>(transactions);
    }

    public async Task<IEnumerable<FinancialTransactionDto>> GetByDateRangeAsync(int companyId, DateTime startDate, DateTime endDate, CancellationToken cancellationToken = default)
    {
        var transactions = await _unitOfWork.FinancialTransactions.GetAsync(
            filter: t => t.CompanyId == companyId && t.TransactionDate >= startDate && t.TransactionDate <= endDate,
            includeProperties: "Person,Company,Invoice",
            cancellationToken: cancellationToken);
        return _mapper.Map<IEnumerable<FinancialTransactionDto>>(transactions);
    }

    public async Task<IEnumerable<FinancialTransactionDto>> GetByInvoiceAsync(int invoiceId, CancellationToken cancellationToken = default)
    {
        var transactions = await _unitOfWork.FinancialTransactions.GetAsync(
            filter: t => t.InvoiceId == invoiceId,
            includeProperties: "Person,Company,Invoice",
            cancellationToken: cancellationToken);
        return _mapper.Map<IEnumerable<FinancialTransactionDto>>(transactions);
    }

    public async Task<FinancialTransactionDto> CreateAsync(CreateFinancialTransactionDto createTransactionDto, CancellationToken cancellationToken = default)
    {
        // Validate business rules
        await ValidateTransactionAsync(createTransactionDto, cancellationToken);

        await _unitOfWork.BeginTransactionAsync(cancellationToken);
        try
        {
            var transaction = _mapper.Map<FinancialTransaction>(createTransactionDto);
            
            // Generate reference number if not provided
            if (string.IsNullOrEmpty(transaction.ReferenceNumber))
            {
                transaction.ReferenceNumber = await GenerateReferenceNumber(createTransactionDto.CompanyId, cancellationToken);
            }

            await _unitOfWork.FinancialTransactions.AddAsync(transaction, cancellationToken);
            await _unitOfWork.SaveChangesAsync(cancellationToken);

            // Update main cash balance
            await UpdateMainCashBalance(createTransactionDto.CompanyId, createTransactionDto.Amount, 
                createTransactionDto.TransactionType, cancellationToken);

            await _unitOfWork.CommitTransactionAsync(cancellationToken);

            _logger.LogInformation("Financial transaction created successfully with ID: {TransactionId}", transaction.Id);
            return _mapper.Map<FinancialTransactionDto>(transaction);
        }
        catch
        {
            await _unitOfWork.RollbackTransactionAsync(cancellationToken);
            throw;
        }
    }

    public async Task<FinancialTransactionDto> UpdateAsync(int id, UpdateFinancialTransactionDto updateTransactionDto, CancellationToken cancellationToken = default)
    {
        var existingTransaction = await _unitOfWork.FinancialTransactions.GetByIdAsync(id, cancellationToken);
        if (existingTransaction == null)
        {
            throw new ArgumentException($"Financial transaction with ID {id} not found.", nameof(id));
        }

        await _unitOfWork.BeginTransactionAsync(cancellationToken);
        try
        {
            // Reverse the previous transaction effect on main cash
            await UpdateMainCashBalance(existingTransaction.CompanyId, existingTransaction.Amount, 
                GetReverseTransactionType(existingTransaction.TransactionType), cancellationToken);

            // Update transaction
            var originalAmount = existingTransaction.Amount;
            var originalType = existingTransaction.TransactionType;
            
            _mapper.Map(updateTransactionDto, existingTransaction);
            _unitOfWork.FinancialTransactions.Update(existingTransaction);
            await _unitOfWork.SaveChangesAsync(cancellationToken);

            // Apply the new transaction effect on main cash
            await UpdateMainCashBalance(existingTransaction.CompanyId, updateTransactionDto.Amount, 
                updateTransactionDto.TransactionType, cancellationToken);

            await _unitOfWork.CommitTransactionAsync(cancellationToken);

            _logger.LogInformation("Financial transaction updated successfully with ID: {TransactionId}", id);
            return _mapper.Map<FinancialTransactionDto>(existingTransaction);
        }
        catch
        {
            await _unitOfWork.RollbackTransactionAsync(cancellationToken);
            throw;
        }
    }

    public async Task<bool> DeleteAsync(int id, CancellationToken cancellationToken = default)
    {
        var transaction = await _unitOfWork.FinancialTransactions.GetByIdAsync(id, cancellationToken);
        if (transaction == null)
        {
            return false;
        }

        await _unitOfWork.BeginTransactionAsync(cancellationToken);
        try
        {
            // Reverse the transaction effect on main cash
            await UpdateMainCashBalance(transaction.CompanyId, transaction.Amount, 
                GetReverseTransactionType(transaction.TransactionType), cancellationToken);

            _unitOfWork.FinancialTransactions.Delete(transaction);
            await _unitOfWork.SaveChangesAsync(cancellationToken);

            await _unitOfWork.CommitTransactionAsync(cancellationToken);

            _logger.LogInformation("Financial transaction deleted successfully with ID: {TransactionId}", id);
            return true;
        }
        catch
        {
            await _unitOfWork.RollbackTransactionAsync(cancellationToken);
            throw;
        }
    }

    public async Task<FinancialTransactionDto> CreateInvoicePaymentAsync(int invoiceId, decimal amount, string paymentMethod, CancellationToken cancellationToken = default)
    {
        var invoice = await _unitOfWork.Invoices.GetByIdAsync(invoiceId, cancellationToken);
        if (invoice == null)
        {
            throw new ArgumentException($"Invoice with ID {invoiceId} not found.", nameof(invoiceId));
        }

        var transactionType = invoice.InvoiceType == "Sales" ? "Income" : "Expense";
        
        var createTransactionDto = new CreateFinancialTransactionDto
        {
            TransactionDate = DateTime.UtcNow,
            TransactionType = transactionType,
            Amount = amount,
            Description = $"Payment for {invoice.InvoiceType} Invoice #{invoice.InvoiceNumber}",
            ReferenceNumber = $"PAY-{invoice.InvoiceNumber}",
            PersonId = invoice.PersonId,
            CompanyId = invoice.CompanyId,
            InvoiceId = invoiceId
        };

        return await CreateAsync(createTransactionDto, cancellationToken);
    }

    public async Task<FinancialTransactionDto> CreatePartnerCapitalTransactionAsync(int partnerId, decimal amount, string transactionType, string description, CancellationToken cancellationToken = default)
    {
        var partner = await _unitOfWork.Partners.GetByIdAsync(partnerId, cancellationToken);
        if (partner == null)
        {
            throw new ArgumentException($"Partner with ID {partnerId} not found.", nameof(partnerId));
        }

        var createTransactionDto = new CreateFinancialTransactionDto
        {
            TransactionDate = DateTime.UtcNow,
            TransactionType = transactionType,
            Amount = amount,
            Description = description,
            ReferenceNumber = $"PARTNER-{partner.Id}-{DateTime.UtcNow:yyyyMMddHHmmss}",
            CompanyId = partner.CompanyId
        };

        return await CreateAsync(createTransactionDto, cancellationToken);
    }

    public async Task<CashFlowReportDto> GetCashFlowReportAsync(int companyId, DateTime startDate, DateTime endDate, CancellationToken cancellationToken = default)
    {
        var transactions = await GetByDateRangeAsync(companyId, startDate, endDate, cancellationToken);
        
        var totalIncome = transactions.Where(t => t.TransactionType == "Income").Sum(t => t.Amount);
        var totalExpenses = transactions.Where(t => t.TransactionType == "Expense").Sum(t => t.Amount);
        
        var openingBalance = await GetBalanceAtDate(companyId, startDate.AddDays(-1), cancellationToken);
        var closingBalance = openingBalance + totalIncome - totalExpenses;

        return new CashFlowReportDto
        {
            StartDate = startDate,
            EndDate = endDate,
            TotalIncome = totalIncome,
            TotalExpenses = totalExpenses,
            NetCashFlow = totalIncome - totalExpenses,
            OpeningBalance = openingBalance,
            ClosingBalance = closingBalance,
            Transactions = transactions.ToList()
        };
    }

    public async Task<BalanceSummaryDto> GetBalanceSummaryAsync(int companyId, CancellationToken cancellationToken = default)
    {
        var currentBalance = await GetCurrentBalanceAsync(companyId, cancellationToken);

        // Calculate receivables and payables from invoices
        var salesInvoices = await _unitOfWork.Invoices.GetAsync(
            filter: i => i.CompanyId == companyId && i.InvoiceType == "Sales" && i.Status == "Confirmed",
            cancellationToken: cancellationToken);
        var purchaseInvoices = await _unitOfWork.Invoices.GetAsync(
            filter: i => i.CompanyId == companyId && i.InvoiceType == "Purchase" && i.Status == "Confirmed",
            cancellationToken: cancellationToken);

        var totalReceivables = salesInvoices.Sum(i => i.TotalAmount);
        var totalPayables = purchaseInvoices.Sum(i => i.TotalAmount);

        return new BalanceSummaryDto
        {
            CurrentBalance = currentBalance,
            TotalReceivables = totalReceivables,
            TotalPayables = totalPayables,
            NetWorth = currentBalance + totalReceivables - totalPayables,
            LastUpdated = DateTime.UtcNow
        };
    }

    public async Task<ProfitLossReportDto> GetProfitLossReportAsync(int companyId, DateTime startDate, DateTime endDate, CancellationToken cancellationToken = default)
    {
        // Calculate revenue from sales invoices
        var salesInvoices = await _unitOfWork.Invoices.GetAsync(
            filter: i => i.CompanyId == companyId && i.InvoiceType == "Sales" &&
                        i.InvoiceDate >= startDate && i.InvoiceDate <= endDate && i.Status == "Confirmed",
            cancellationToken: cancellationToken);
        var totalRevenue = salesInvoices.Sum(i => i.TotalAmount);

        var totalExpenses = await GetTotalExpensesByDateRangeAsync(companyId, startDate, endDate, cancellationToken);

        // Calculate cost of goods sold (simplified calculation)
        var purchaseInvoices = await _unitOfWork.Invoices.GetAsync(
            filter: i => i.CompanyId == companyId && i.InvoiceType == "Purchase" &&
                        i.InvoiceDate >= startDate && i.InvoiceDate <= endDate && i.Status == "Confirmed",
            cancellationToken: cancellationToken);
        var costOfGoodsSold = purchaseInvoices.Sum(i => i.TotalAmount);

        var grossProfit = totalRevenue - costOfGoodsSold;
        var netProfit = grossProfit - totalExpenses;
        var profitMarginPercentage = totalRevenue > 0 ? (netProfit / totalRevenue) * 100 : 0;

        return new ProfitLossReportDto
        {
            StartDate = startDate,
            EndDate = endDate,
            TotalRevenue = totalRevenue,
            CostOfGoodsSold = costOfGoodsSold,
            GrossProfit = grossProfit,
            OperatingExpenses = totalExpenses,
            NetProfit = netProfit,
            ProfitMarginPercentage = profitMarginPercentage
        };
    }

    public async Task<decimal> GetTotalIncomeByDateRangeAsync(int companyId, DateTime startDate, DateTime endDate, CancellationToken cancellationToken = default)
    {
        var transactions = await _unitOfWork.FinancialTransactions.GetAsync(
            filter: t => t.CompanyId == companyId && t.TransactionType == "Income" &&
                        t.TransactionDate >= startDate && t.TransactionDate <= endDate,
            cancellationToken: cancellationToken);
        return transactions.Sum(t => t.Amount);
    }

    public async Task<decimal> GetTotalExpensesByDateRangeAsync(int companyId, DateTime startDate, DateTime endDate, CancellationToken cancellationToken = default)
    {
        var transactions = await _unitOfWork.FinancialTransactions.GetAsync(
            filter: t => t.CompanyId == companyId && t.TransactionType == "Expense" &&
                        t.TransactionDate >= startDate && t.TransactionDate <= endDate,
            cancellationToken: cancellationToken);
        return transactions.Sum(t => t.Amount);
    }

    public async Task<decimal> GetCurrentBalanceAsync(int companyId, CancellationToken cancellationToken = default)
    {
        var mainCashes = await _unitOfWork.MainCashes.GetAsync(
            filter: m => m.CompanyId == companyId,
            cancellationToken: cancellationToken);
        var mainCash = mainCashes.FirstOrDefault();
        return mainCash?.Balance ?? 0;
    }

    public async Task<IEnumerable<string>> GetTransactionTypesAsync(CancellationToken cancellationToken = default)
    {
        return new[] { "Income", "Expense", "Transfer" };
    }

    public async Task<bool> ValidateTransactionAsync(CreateFinancialTransactionDto transactionDto, CancellationToken cancellationToken = default)
    {
        // Validate company exists
        var company = await _unitOfWork.Companies.GetByIdAsync(transactionDto.CompanyId, cancellationToken);
        if (company == null)
        {
            throw new ArgumentException("Invalid company ID.", nameof(transactionDto.CompanyId));
        }

        // Validate person exists if provided
        if (transactionDto.PersonId.HasValue)
        {
            var person = await _unitOfWork.Persons.GetByIdAsync(transactionDto.PersonId.Value, cancellationToken);
            if (person == null)
            {
                throw new ArgumentException("Invalid person ID.", nameof(transactionDto.PersonId));
            }
        }

        // Validate invoice exists if provided
        if (transactionDto.InvoiceId.HasValue)
        {
            var invoice = await _unitOfWork.Invoices.GetByIdAsync(transactionDto.InvoiceId.Value, cancellationToken);
            if (invoice == null)
            {
                throw new ArgumentException("Invalid invoice ID.", nameof(transactionDto.InvoiceId));
            }
        }

        // Validate transaction type
        var validTypes = new[] { "Income", "Expense", "Transfer" };
        if (!validTypes.Contains(transactionDto.TransactionType))
        {
            throw new ArgumentException("Invalid transaction type.", nameof(transactionDto.TransactionType));
        }

        // Validate amount
        if (transactionDto.Amount <= 0)
        {
            throw new ArgumentException("Transaction amount must be greater than zero.", nameof(transactionDto.Amount));
        }

        return true;
    }

    public async Task<IEnumerable<object>> GetMonthlySummaryAsync(int companyId, int year, CancellationToken cancellationToken = default)
    {
        var monthlySummary = new List<object>();

        for (int month = 1; month <= 12; month++)
        {
            var startDate = new DateTime(year, month, 1);
            var endDate = startDate.AddMonths(1).AddDays(-1);

            var income = await GetTotalIncomeByDateRangeAsync(companyId, startDate, endDate, cancellationToken);
            var expenses = await GetTotalExpensesByDateRangeAsync(companyId, startDate, endDate, cancellationToken);

            monthlySummary.Add(new
            {
                Month = month,
                MonthName = startDate.ToString("MMMM"),
                Income = income,
                Expenses = expenses,
                NetFlow = income - expenses
            });
        }

        return monthlySummary;
    }

    private async Task UpdateMainCashBalance(int companyId, decimal amount, string transactionType, CancellationToken cancellationToken)
    {
        var mainCashes = await _unitOfWork.MainCashes.GetAsync(
            filter: m => m.CompanyId == companyId,
            cancellationToken: cancellationToken);
        var mainCash = mainCashes.FirstOrDefault();

        if (mainCash == null)
        {
            // Create main cash if it doesn't exist
            mainCash = new MainCash
            {
                CompanyId = companyId,
                Balance = 0,
                Currency = "USD",
                LastUpdated = DateTime.UtcNow
            };
            await _unitOfWork.MainCashes.AddAsync(mainCash, cancellationToken);
        }

        switch (transactionType)
        {
            case "Income":
                mainCash.Balance += amount;
                break;
            case "Expense":
                mainCash.Balance -= amount;
                break;
            case "Transfer":
                // Transfer logic would depend on specific requirements
                break;
        }

        mainCash.LastUpdated = DateTime.UtcNow;
        _unitOfWork.MainCashes.Update(mainCash);
        await _unitOfWork.SaveChangesAsync(cancellationToken);
    }

    private string GetReverseTransactionType(string transactionType)
    {
        return transactionType switch
        {
            "Income" => "Expense",
            "Expense" => "Income",
            _ => transactionType
        };
    }

    private async Task<string> GenerateReferenceNumber(int companyId, CancellationToken cancellationToken)
    {
        var transactions = await _unitOfWork.FinancialTransactions.GetAsync(
            filter: t => t.CompanyId == companyId,
            cancellationToken: cancellationToken);
        var count = transactions.Count();
        return $"TXN-{companyId:D4}-{DateTime.UtcNow:yyyyMMdd}-{(count + 1):D6}";
    }

    private async Task<decimal> GetBalanceAtDate(int companyId, DateTime date, CancellationToken cancellationToken)
    {
        var transactions = await _unitOfWork.FinancialTransactions.GetAsync(
            filter: t => t.CompanyId == companyId && t.TransactionDate <= date,
            cancellationToken: cancellationToken);

        var income = transactions.Where(t => t.TransactionType == "Income").Sum(t => t.Amount);
        var expenses = transactions.Where(t => t.TransactionType == "Expense").Sum(t => t.Amount);

        return income - expenses;
    }
}
