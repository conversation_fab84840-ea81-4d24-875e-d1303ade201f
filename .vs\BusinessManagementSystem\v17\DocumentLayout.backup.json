{"Version": 1, "WorkspaceRootPath": "D:\\AIProjectTest\\Sourcs\\DoorAPP\\", "Documents": [{"AbsoluteMoniker": "D:0:0:{80535FF0-7AB6-4798-A6E6-65753255696B}|src\\BusinessManagementSystem.API\\BusinessManagementSystem.API.csproj|d:\\aiprojecttest\\sourcs\\doorapp\\src\\businessmanagementsystem.api\\controllers\\warehousescontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{80535FF0-7AB6-4798-A6E6-65753255696B}|src\\BusinessManagementSystem.API\\BusinessManagementSystem.API.csproj|solutionrelative:src\\businessmanagementsystem.api\\controllers\\warehousescontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{80535FF0-7AB6-4798-A6E6-65753255696B}|src\\BusinessManagementSystem.API\\BusinessManagementSystem.API.csproj|d:\\aiprojecttest\\sourcs\\doorapp\\src\\businessmanagementsystem.api\\controllers\\inventorytransactionscontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{80535FF0-7AB6-4798-A6E6-65753255696B}|src\\BusinessManagementSystem.API\\BusinessManagementSystem.API.csproj|solutionrelative:src\\businessmanagementsystem.api\\controllers\\inventorytransactionscontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{80535FF0-7AB6-4798-A6E6-65753255696B}|src\\BusinessManagementSystem.API\\BusinessManagementSystem.API.csproj|d:\\aiprojecttest\\sourcs\\doorapp\\src\\businessmanagementsystem.api\\controllers\\rolepermissionscontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{80535FF0-7AB6-4798-A6E6-65753255696B}|src\\BusinessManagementSystem.API\\BusinessManagementSystem.API.csproj|solutionrelative:src\\businessmanagementsystem.api\\controllers\\rolepermissionscontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{80535FF0-7AB6-4798-A6E6-65753255696B}|src\\BusinessManagementSystem.API\\BusinessManagementSystem.API.csproj|d:\\aiprojecttest\\sourcs\\doorapp\\src\\businessmanagementsystem.api\\controllers\\partnerscontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{80535FF0-7AB6-4798-A6E6-65753255696B}|src\\BusinessManagementSystem.API\\BusinessManagementSystem.API.csproj|solutionrelative:src\\businessmanagementsystem.api\\controllers\\partnerscontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}], "DocumentGroupContainers": [{"Orientation": 0, "VerticalTabListWidth": 256, "DocumentGroups": [{"DockedWidth": 200, "SelectedChildIndex": 2, "Children": [{"$type": "Document", "DocumentIndex": 2, "Title": "RolePermissionsController.cs", "DocumentMoniker": "D:\\AIProjectTest\\Sourcs\\DoorAPP\\src\\BusinessManagementSystem.API\\Controllers\\RolePermissionsController.cs", "RelativeDocumentMoniker": "src\\BusinessManagementSystem.API\\Controllers\\RolePermissionsController.cs", "ToolTip": "D:\\AIProjectTest\\Sourcs\\DoorAPP\\src\\BusinessManagementSystem.API\\Controllers\\RolePermissionsController.cs*", "RelativeToolTip": "src\\BusinessManagementSystem.API\\Controllers\\RolePermissionsController.cs*", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAQAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-26T08:33:19.122Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 1, "Title": "InventoryTransactionsController.cs", "DocumentMoniker": "D:\\AIProjectTest\\Sourcs\\DoorAPP\\src\\BusinessManagementSystem.API\\Controllers\\InventoryTransactionsController.cs", "RelativeDocumentMoniker": "src\\BusinessManagementSystem.API\\Controllers\\InventoryTransactionsController.cs", "ToolTip": "D:\\AIProjectTest\\Sourcs\\DoorAPP\\src\\BusinessManagementSystem.API\\Controllers\\InventoryTransactionsController.cs", "RelativeToolTip": "src\\BusinessManagementSystem.API\\Controllers\\InventoryTransactionsController.cs", "ViewState": "AgIAAE8AAAAAAAAAAAAvwFgAAAAMAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-26T08:33:13.896Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 0, "Title": "WarehousesController.cs", "DocumentMoniker": "D:\\AIProjectTest\\Sourcs\\DoorAPP\\src\\BusinessManagementSystem.API\\Controllers\\WarehousesController.cs", "RelativeDocumentMoniker": "src\\BusinessManagementSystem.API\\Controllers\\WarehousesController.cs", "ToolTip": "D:\\AIProjectTest\\Sourcs\\DoorAPP\\src\\BusinessManagementSystem.API\\Controllers\\WarehousesController.cs", "RelativeToolTip": "src\\BusinessManagementSystem.API\\Controllers\\WarehousesController.cs", "ViewState": "AgIAAMEBAAAAAAAAAAAvwMoBAAAXAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-26T08:32:57.792Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 3, "Title": "PartnersController.cs", "DocumentMoniker": "D:\\AIProjectTest\\Sourcs\\DoorAPP\\src\\BusinessManagementSystem.API\\Controllers\\PartnersController.cs", "RelativeDocumentMoniker": "src\\BusinessManagementSystem.API\\Controllers\\PartnersController.cs", "ToolTip": "D:\\AIProjectTest\\Sourcs\\DoorAPP\\src\\BusinessManagementSystem.API\\Controllers\\PartnersController.cs", "RelativeToolTip": "src\\BusinessManagementSystem.API\\Controllers\\PartnersController.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAwAAAAzAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-26T08:32:48.675Z", "EditorCaption": ""}]}]}]}