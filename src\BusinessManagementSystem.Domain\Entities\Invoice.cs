using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using BusinessManagementSystem.Domain.Common;

namespace BusinessManagementSystem.Domain.Entities;

/// <summary>
/// Invoice entity - stores invoice headers
/// كيان الفاتورة - يخزن رؤوس الفواتير
/// </summary>
public class Invoice : BaseEntity
{
    /// <summary>
    /// Invoice number
    /// رقم الفاتورة
    /// </summary>
    [Required]
    [MaxLength(50)]
    public string InvoiceNumber { get; set; } = string.Empty;

    /// <summary>
    /// Invoice date
    /// تاريخ الفاتورة
    /// </summary>
    [Required]
    public DateTime InvoiceDate { get; set; }

    /// <summary>
    /// Invoice type (Sales/Purchase/Return)
    /// نوع الفاتورة (مبيعات/مشتريات/مرتجع)
    /// </summary>
    [Required]
    [MaxLength(20)]
    public string InvoiceType { get; set; } = string.Empty;

    /// <summary>
    /// Total amount before tax and discount
    /// المبلغ الإجمالي قبل الضريبة والخصم
    /// </summary>
    [Column(TypeName = "decimal(18,2)")]
    public decimal TotalAmount { get; set; }

    /// <summary>
    /// Tax amount
    /// مبلغ الضريبة
    /// </summary>
    [Column(TypeName = "decimal(18,2)")]
    public decimal TaxAmount { get; set; }

    /// <summary>
    /// Discount amount
    /// مبلغ الخصم
    /// </summary>
    [Column(TypeName = "decimal(18,2)")]
    public decimal DiscountAmount { get; set; }

    /// <summary>
    /// Invoice status (Draft/Confirmed/Cancelled)
    /// حالة الفاتورة (مسودة/مؤكدة/ملغاة)
    /// </summary>
    [MaxLength(20)]
    public string Status { get; set; } = "Draft";

    /// <summary>
    /// Additional notes
    /// ملاحظات إضافية
    /// </summary>
    [MaxLength(1000)]
    public string? Notes { get; set; }

    /// <summary>
    /// Person identifier (Customer/Supplier Foreign Key)
    /// معرف الشخص (مفتاح خارجي للعميل/المورد)
    /// </summary>
    [Required]
    public int PersonId { get; set; }

    /// <summary>
    /// Company identifier (Foreign Key)
    /// معرف الشركة (مفتاح خارجي)
    /// </summary>
    [Required]
    public int CompanyId { get; set; }

    // Navigation properties
    [ForeignKey(nameof(PersonId))]
    public virtual Person Person { get; set; } = null!;

    [ForeignKey(nameof(CompanyId))]
    public virtual Company Company { get; set; } = null!;
    
    public virtual ICollection<InvoiceDetail> InvoiceDetails { get; set; } = new List<InvoiceDetail>();
    public virtual ICollection<InventoryTransaction> InventoryTransactions { get; set; } = new List<InventoryTransaction>();
    public virtual ICollection<FinancialTransaction> FinancialTransactions { get; set; } = new List<FinancialTransaction>();
}
