using System.Security.Cryptography;
using System.Text;
using BusinessManagementSystem.Application.Services.Interfaces;

namespace BusinessManagementSystem.Infrastructure.Services;

/// <summary>
/// Password hashing service implementation using BCrypt
/// تنفيذ خدمة تشفير كلمات المرور باستخدام BCrypt
/// </summary>
public class PasswordHashService : IPasswordHashService
{
    private const int WorkFactor = 12; // BCrypt work factor for security

    public string HashPassword(string password)
    {
        if (string.IsNullOrWhiteSpace(password))
            throw new ArgumentException("Password cannot be null or empty.", nameof(password));

        return BCrypt.Net.BCrypt.HashPassword(password, WorkFactor);
    }

    public bool VerifyPassword(string password, string hash)
    {
        if (string.IsNullOrWhiteSpace(password))
            return false;

        if (string.IsNullOrWhiteSpace(hash))
            return false;

        try
        {
            return BCrypt.Net.BCrypt.Verify(password, hash);
        }
        catch
        {
            return false;
        }
    }

    public string GenerateSalt()
    {
        return BCrypt.Net.BCrypt.GenerateSalt(WorkFactor);
    }
}
