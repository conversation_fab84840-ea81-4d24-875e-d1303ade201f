using BusinessManagementSystem.Application.DTOs.Partner;

namespace BusinessManagementSystem.Application.Services.Interfaces;

/// <summary>
/// Partner service interface for business logic operations
/// واجهة خدمة الشريك لعمليات منطق الأعمال
/// </summary>
public interface IPartnerService
{
    /// <summary>
    /// Get all partners
    /// الحصول على جميع الشركاء
    /// </summary>
    Task<IEnumerable<PartnerDto>> GetAllAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// Get partner by ID
    /// الحصول على الشريك بواسطة المعرف
    /// </summary>
    Task<PartnerDto?> GetByIdAsync(int id, CancellationToken cancellationToken = default);

    /// <summary>
    /// Get partners by company
    /// الحصول على الشركاء بواسطة الشركة
    /// </summary>
    Task<IEnumerable<PartnerDto>> GetByCompanyAsync(int companyId, CancellationToken cancellationToken = default);

    /// <summary>
    /// Get active partners by company
    /// الحصول على الشركاء النشطين بواسطة الشركة
    /// </summary>
    Task<IEnumerable<PartnerDto>> GetActiveByCompanyAsync(int companyId, CancellationToken cancellationToken = default);

    /// <summary>
    /// Create new partner
    /// إنشاء شريك جديد
    /// </summary>
    Task<PartnerDto> CreateAsync(CreatePartnerDto createPartnerDto, CancellationToken cancellationToken = default);

    /// <summary>
    /// Update existing partner
    /// تحديث شريك موجود
    /// </summary>
    Task<PartnerDto> UpdateAsync(int id, UpdatePartnerDto updatePartnerDto, CancellationToken cancellationToken = default);

    /// <summary>
    /// Add capital contribution
    /// إضافة مساهمة رأس المال
    /// </summary>
    Task<object> AddCapitalContributionAsync(int id, CapitalContributionDto contributionDto, CancellationToken cancellationToken = default);

    /// <summary>
    /// Process capital withdrawal
    /// معالجة سحب رأس المال
    /// </summary>
    Task<object> ProcessCapitalWithdrawalAsync(int id, CapitalWithdrawalDto withdrawalDto, CancellationToken cancellationToken = default);

    /// <summary>
    /// Get partner capital history
    /// الحصول على تاريخ رأس مال الشريك
    /// </summary>
    Task<IEnumerable<object>> GetCapitalHistoryAsync(int id, CancellationToken cancellationToken = default);

    /// <summary>
    /// Set partner active status
    /// تعيين حالة تفعيل الشريك
    /// </summary>
    Task<bool> SetActiveStatusAsync(int id, bool isActive, CancellationToken cancellationToken = default);

    /// <summary>
    /// Delete partner
    /// حذف الشريك
    /// </summary>
    Task<bool> DeleteAsync(int id, CancellationToken cancellationToken = default);

    /// <summary>
    /// Get partner statistics
    /// الحصول على إحصائيات الشريك
    /// </summary>
    Task<object> GetPartnerStatisticsAsync(int id, CancellationToken cancellationToken = default);
}
