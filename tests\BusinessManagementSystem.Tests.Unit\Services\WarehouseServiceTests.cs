using AutoMapper;
using FluentAssertions;
using Microsoft.Extensions.Logging;
using Moq;
using BusinessManagementSystem.Application.DTOs.Warehouse;
using BusinessManagementSystem.Application.Interfaces;
using BusinessManagementSystem.Application.Services.Implementations;
using BusinessManagementSystem.Domain.Entities;
using System.Linq.Expressions;

namespace BusinessManagementSystem.Tests.Unit.Services;

/// <summary>
/// Unit tests for WarehouseService
/// اختبارات الوحدة لخدمة المستودعات
/// </summary>
public class WarehouseServiceTests
{
    private readonly Mock<IUnitOfWork> _mockUnitOfWork;
    private readonly Mock<IMapper> _mockMapper;
    private readonly Mock<ILogger<WarehouseService>> _mockLogger;
    private readonly WarehouseService _warehouseService;

    public WarehouseServiceTests()
    {
        _mockUnitOfWork = new Mock<IUnitOfWork>();
        _mockMapper = new Mock<IMapper>();
        _mockLogger = new Mock<ILogger<WarehouseService>>();
        _warehouseService = new WarehouseService(_mockUnitOfWork.Object, _mockMapper.Object, _mockLogger.Object);
    }

    [Fact]
    public async Task GetAllAsync_ShouldReturnAllWarehouses_WhenWarehousesExist()
    {
        // Arrange
        var warehouses = new List<Warehouse>
        {
            new Warehouse { Id = 1, Name = "Warehouse 1", CompanyId = 1, MaxCapacity = 1000, CurrentUtilization = 500 },
            new Warehouse { Id = 2, Name = "Warehouse 2", CompanyId = 1, MaxCapacity = 2000, CurrentUtilization = 800 }
        };

        var warehouseDtos = new List<WarehouseDto>
        {
            new WarehouseDto { Id = 1, Name = "Warehouse 1", CompanyId = 1, MaxCapacity = 1000, CurrentUtilization = 500 },
            new WarehouseDto { Id = 2, Name = "Warehouse 2", CompanyId = 1, MaxCapacity = 2000, CurrentUtilization = 800 }
        };

        _mockUnitOfWork.Setup(x => x.Warehouses.GetAsync(
            It.IsAny<Expression<Func<Warehouse, bool>>>(),
            It.IsAny<Func<IQueryable<Warehouse>, IOrderedQueryable<Warehouse>>>(),
            It.IsAny<string>(),
            It.IsAny<CancellationToken>()))
            .ReturnsAsync(warehouses);

        _mockMapper.Setup(x => x.Map<IEnumerable<WarehouseDto>>(warehouses))
            .Returns(warehouseDtos);

        // Act
        var result = await _warehouseService.GetAllAsync();

        // Assert
        result.Should().NotBeNull();
        result.Should().HaveCount(2);
        result.Should().BeEquivalentTo(warehouseDtos);
    }

    [Fact]
    public async Task GetByIdAsync_ShouldReturnWarehouse_WhenWarehouseExists()
    {
        // Arrange
        var warehouseId = 1;
        var warehouse = new Warehouse { Id = warehouseId, Name = "Test Warehouse", CompanyId = 1, MaxCapacity = 1000 };
        var warehouseDto = new WarehouseDto { Id = warehouseId, Name = "Test Warehouse", CompanyId = 1, MaxCapacity = 1000 };

        _mockUnitOfWork.Setup(x => x.Warehouses.GetByIdAsync(warehouseId, It.IsAny<CancellationToken>()))
            .ReturnsAsync(warehouse);

        _mockMapper.Setup(x => x.Map<WarehouseDto>(warehouse))
            .Returns(warehouseDto);

        // Act
        var result = await _warehouseService.GetByIdAsync(warehouseId);

        // Assert
        result.Should().NotBeNull();
        result.Should().BeEquivalentTo(warehouseDto);
    }

    [Fact]
    public async Task GetByIdAsync_ShouldReturnNull_WhenWarehouseDoesNotExist()
    {
        // Arrange
        var warehouseId = 999;

        _mockUnitOfWork.Setup(x => x.Warehouses.GetByIdAsync(warehouseId, It.IsAny<CancellationToken>()))
            .ReturnsAsync((Warehouse?)null);

        // Act
        var result = await _warehouseService.GetByIdAsync(warehouseId);

        // Assert
        result.Should().BeNull();
    }

    [Fact]
    public async Task CreateAsync_ShouldCreateWarehouse_WhenValidDataProvided()
    {
        // Arrange
        var createWarehouseDto = new CreateWarehouseDto
        {
            Name = "New Warehouse",
            Description = "Test Description",
            Location = "Test Location",
            MaxCapacity = 1000,
            CompanyId = 1
        };

        var warehouse = new Warehouse
        {
            Id = 1,
            Name = "New Warehouse",
            Description = "Test Description",
            Location = "Test Location",
            MaxCapacity = 1000,
            CurrentUtilization = 0,
            CompanyId = 1
        };

        var warehouseDto = new WarehouseDto
        {
            Id = 1,
            Name = "New Warehouse",
            Description = "Test Description",
            Location = "Test Location",
            MaxCapacity = 1000,
            CurrentUtilization = 0,
            CompanyId = 1
        };

        var company = new Company { Id = 1, Name = "Test Company" };

        _mockUnitOfWork.Setup(x => x.Companies.GetByIdAsync(1, It.IsAny<CancellationToken>()))
            .ReturnsAsync(company);

        _mockUnitOfWork.Setup(x => x.Warehouses.GetAsync(
            It.IsAny<Expression<Func<Warehouse, bool>>>(),
            It.IsAny<Func<IQueryable<Warehouse>, IOrderedQueryable<Warehouse>>>(),
            It.IsAny<string>(),
            It.IsAny<CancellationToken>()))
            .ReturnsAsync(new List<Warehouse>());

        _mockMapper.Setup(x => x.Map<Warehouse>(createWarehouseDto))
            .Returns(warehouse);

        _mockMapper.Setup(x => x.Map<WarehouseDto>(warehouse))
            .Returns(warehouseDto);

        _mockUnitOfWork.Setup(x => x.Warehouses.AddAsync(It.IsAny<Warehouse>(), It.IsAny<CancellationToken>()))
            .Returns(Task.CompletedTask);

        _mockUnitOfWork.Setup(x => x.SaveChangesAsync(It.IsAny<CancellationToken>()))
            .Returns(Task.CompletedTask);

        // Act
        var result = await _warehouseService.CreateAsync(createWarehouseDto);

        // Assert
        result.Should().NotBeNull();
        result.Should().BeEquivalentTo(warehouseDto);
        result.CurrentUtilization.Should().Be(0); // New warehouse should start empty
        _mockUnitOfWork.Verify(x => x.Warehouses.AddAsync(It.IsAny<Warehouse>(), It.IsAny<CancellationToken>()), Times.Once);
        _mockUnitOfWork.Verify(x => x.SaveChangesAsync(It.IsAny<CancellationToken>()), Times.Once);
    }

    [Fact]
    public async Task CreateAsync_ShouldThrowArgumentException_WhenCompanyDoesNotExist()
    {
        // Arrange
        var createWarehouseDto = new CreateWarehouseDto
        {
            Name = "New Warehouse",
            CompanyId = 999
        };

        _mockUnitOfWork.Setup(x => x.Companies.GetByIdAsync(999, It.IsAny<CancellationToken>()))
            .ReturnsAsync((Company?)null);

        // Act & Assert
        await _warehouseService.Invoking(x => x.CreateAsync(createWarehouseDto))
            .Should().ThrowAsync<ArgumentException>()
            .WithMessage("Invalid company ID.*");
    }

    [Fact]
    public async Task CreateAsync_ShouldThrowArgumentException_WhenWarehouseNameAlreadyExists()
    {
        // Arrange
        var createWarehouseDto = new CreateWarehouseDto
        {
            Name = "Existing Warehouse",
            CompanyId = 1
        };

        var company = new Company { Id = 1, Name = "Test Company" };
        var existingWarehouses = new List<Warehouse>
        {
            new Warehouse { Id = 1, Name = "Existing Warehouse", CompanyId = 1 }
        };

        _mockUnitOfWork.Setup(x => x.Companies.GetByIdAsync(1, It.IsAny<CancellationToken>()))
            .ReturnsAsync(company);

        _mockUnitOfWork.Setup(x => x.Warehouses.GetAsync(
            It.IsAny<Expression<Func<Warehouse, bool>>>(),
            It.IsAny<Func<IQueryable<Warehouse>, IOrderedQueryable<Warehouse>>>(),
            It.IsAny<string>(),
            It.IsAny<CancellationToken>()))
            .ReturnsAsync(existingWarehouses);

        // Act & Assert
        await _warehouseService.Invoking(x => x.CreateAsync(createWarehouseDto))
            .Should().ThrowAsync<ArgumentException>()
            .WithMessage("A warehouse with this name already exists in the company.");
    }

    [Fact]
    public async Task UpdateAsync_ShouldUpdateWarehouse_WhenValidDataProvided()
    {
        // Arrange
        var warehouseId = 1;
        var updateWarehouseDto = new UpdateWarehouseDto
        {
            Name = "Updated Warehouse",
            Description = "Updated Description",
            MaxCapacity = 1500
        };

        var existingWarehouse = new Warehouse
        {
            Id = warehouseId,
            Name = "Original Warehouse",
            Description = "Original Description",
            MaxCapacity = 1000,
            CurrentUtilization = 500,
            CompanyId = 1
        };

        var updatedWarehouseDto = new WarehouseDto
        {
            Id = warehouseId,
            Name = "Updated Warehouse",
            Description = "Updated Description",
            MaxCapacity = 1500,
            CurrentUtilization = 500,
            CompanyId = 1
        };

        _mockUnitOfWork.Setup(x => x.Warehouses.GetByIdAsync(warehouseId, It.IsAny<CancellationToken>()))
            .ReturnsAsync(existingWarehouse);

        _mockUnitOfWork.Setup(x => x.Warehouses.GetAsync(
            It.IsAny<Expression<Func<Warehouse, bool>>>(),
            It.IsAny<Func<IQueryable<Warehouse>, IOrderedQueryable<Warehouse>>>(),
            It.IsAny<string>(),
            It.IsAny<CancellationToken>()))
            .ReturnsAsync(new List<Warehouse>());

        _mockMapper.Setup(x => x.Map(updateWarehouseDto, existingWarehouse))
            .Returns(existingWarehouse);

        _mockMapper.Setup(x => x.Map<WarehouseDto>(existingWarehouse))
            .Returns(updatedWarehouseDto);

        _mockUnitOfWork.Setup(x => x.Warehouses.Update(existingWarehouse));
        _mockUnitOfWork.Setup(x => x.SaveChangesAsync(It.IsAny<CancellationToken>()))
            .Returns(Task.CompletedTask);

        // Act
        var result = await _warehouseService.UpdateAsync(warehouseId, updateWarehouseDto);

        // Assert
        result.Should().NotBeNull();
        result.Should().BeEquivalentTo(updatedWarehouseDto);
        _mockUnitOfWork.Verify(x => x.Warehouses.Update(existingWarehouse), Times.Once);
        _mockUnitOfWork.Verify(x => x.SaveChangesAsync(It.IsAny<CancellationToken>()), Times.Once);
    }

    [Fact]
    public async Task UpdateAsync_ShouldThrowInvalidOperationException_WhenReducingCapacityBelowUtilization()
    {
        // Arrange
        var warehouseId = 1;
        var updateWarehouseDto = new UpdateWarehouseDto
        {
            Name = "Updated Warehouse",
            MaxCapacity = 400 // Less than current utilization of 500
        };

        var existingWarehouse = new Warehouse
        {
            Id = warehouseId,
            Name = "Original Warehouse",
            MaxCapacity = 1000,
            CurrentUtilization = 500,
            CompanyId = 1
        };

        _mockUnitOfWork.Setup(x => x.Warehouses.GetByIdAsync(warehouseId, It.IsAny<CancellationToken>()))
            .ReturnsAsync(existingWarehouse);

        _mockUnitOfWork.Setup(x => x.Warehouses.GetAsync(
            It.IsAny<Expression<Func<Warehouse, bool>>>(),
            It.IsAny<Func<IQueryable<Warehouse>, IOrderedQueryable<Warehouse>>>(),
            It.IsAny<string>(),
            It.IsAny<CancellationToken>()))
            .ReturnsAsync(new List<Warehouse>());

        // Act & Assert
        await _warehouseService.Invoking(x => x.UpdateAsync(warehouseId, updateWarehouseDto))
            .Should().ThrowAsync<InvalidOperationException>()
            .WithMessage("Cannot reduce max capacity below current utilization*");
    }

    [Fact]
    public async Task DeleteAsync_ShouldDeleteWarehouse_WhenWarehouseExistsAndIsEmpty()
    {
        // Arrange
        var warehouseId = 1;
        var warehouse = new Warehouse 
        { 
            Id = warehouseId, 
            Name = "Test Warehouse", 
            CompanyId = 1,
            CurrentUtilization = 0 
        };

        _mockUnitOfWork.Setup(x => x.Warehouses.GetByIdAsync(warehouseId, It.IsAny<CancellationToken>()))
            .ReturnsAsync(warehouse);

        _mockUnitOfWork.Setup(x => x.InventoryTransactions.GetAsync(
            It.IsAny<Expression<Func<InventoryTransaction, bool>>>(),
            It.IsAny<Func<IQueryable<InventoryTransaction>, IOrderedQueryable<InventoryTransaction>>>(),
            It.IsAny<string>(),
            It.IsAny<CancellationToken>()))
            .ReturnsAsync(new List<InventoryTransaction>());

        _mockUnitOfWork.Setup(x => x.Warehouses.Delete(warehouse));
        _mockUnitOfWork.Setup(x => x.SaveChangesAsync(It.IsAny<CancellationToken>()))
            .Returns(Task.CompletedTask);

        // Act
        var result = await _warehouseService.DeleteAsync(warehouseId);

        // Assert
        result.Should().BeTrue();
        _mockUnitOfWork.Verify(x => x.Warehouses.Delete(warehouse), Times.Once);
        _mockUnitOfWork.Verify(x => x.SaveChangesAsync(It.IsAny<CancellationToken>()), Times.Once);
    }

    [Fact]
    public async Task DeleteAsync_ShouldThrowInvalidOperationException_WhenWarehouseContainsInventory()
    {
        // Arrange
        var warehouseId = 1;
        var warehouse = new Warehouse 
        { 
            Id = warehouseId, 
            Name = "Test Warehouse", 
            CompanyId = 1,
            CurrentUtilization = 500 // Has inventory
        };

        _mockUnitOfWork.Setup(x => x.Warehouses.GetByIdAsync(warehouseId, It.IsAny<CancellationToken>()))
            .ReturnsAsync(warehouse);

        // Act & Assert
        await _warehouseService.Invoking(x => x.DeleteAsync(warehouseId))
            .Should().ThrowAsync<InvalidOperationException>()
            .WithMessage("Cannot delete warehouse that contains inventory.");
    }

    [Fact]
    public async Task HasSufficientCapacityAsync_ShouldReturnTrue_WhenCapacityIsAvailable()
    {
        // Arrange
        var warehouseId = 1;
        var requiredCapacity = 300m;
        var warehouse = new Warehouse 
        { 
            Id = warehouseId, 
            MaxCapacity = 1000, 
            CurrentUtilization = 500,
            IsActive = true
        };

        _mockUnitOfWork.Setup(x => x.Warehouses.GetByIdAsync(warehouseId, It.IsAny<CancellationToken>()))
            .ReturnsAsync(warehouse);

        // Act
        var result = await _warehouseService.HasSufficientCapacityAsync(warehouseId, requiredCapacity);

        // Assert
        result.Should().BeTrue();
    }

    [Fact]
    public async Task HasSufficientCapacityAsync_ShouldReturnFalse_WhenCapacityIsNotAvailable()
    {
        // Arrange
        var warehouseId = 1;
        var requiredCapacity = 600m; // More than available (1000 - 500 = 500)
        var warehouse = new Warehouse 
        { 
            Id = warehouseId, 
            MaxCapacity = 1000, 
            CurrentUtilization = 500,
            IsActive = true
        };

        _mockUnitOfWork.Setup(x => x.Warehouses.GetByIdAsync(warehouseId, It.IsAny<CancellationToken>()))
            .ReturnsAsync(warehouse);

        // Act
        var result = await _warehouseService.HasSufficientCapacityAsync(warehouseId, requiredCapacity);

        // Assert
        result.Should().BeFalse();
    }

    [Fact]
    public async Task HasSufficientCapacityAsync_ShouldReturnFalse_WhenWarehouseIsInactive()
    {
        // Arrange
        var warehouseId = 1;
        var requiredCapacity = 300m;
        var warehouse = new Warehouse 
        { 
            Id = warehouseId, 
            MaxCapacity = 1000, 
            CurrentUtilization = 500,
            IsActive = false // Inactive warehouse
        };

        _mockUnitOfWork.Setup(x => x.Warehouses.GetByIdAsync(warehouseId, It.IsAny<CancellationToken>()))
            .ReturnsAsync(warehouse);

        // Act
        var result = await _warehouseService.HasSufficientCapacityAsync(warehouseId, requiredCapacity);

        // Assert
        result.Should().BeFalse();
    }

    [Fact]
    public async Task UpdateUtilizationAsync_ShouldUpdateUtilization_WhenValidChange()
    {
        // Arrange
        var warehouseId = 1;
        var utilizationChange = 200m;
        var warehouse = new Warehouse 
        { 
            Id = warehouseId, 
            MaxCapacity = 1000, 
            CurrentUtilization = 500
        };

        _mockUnitOfWork.Setup(x => x.Warehouses.GetByIdAsync(warehouseId, It.IsAny<CancellationToken>()))
            .ReturnsAsync(warehouse);

        _mockUnitOfWork.Setup(x => x.Warehouses.Update(warehouse));
        _mockUnitOfWork.Setup(x => x.SaveChangesAsync(It.IsAny<CancellationToken>()))
            .Returns(Task.CompletedTask);

        // Act
        var result = await _warehouseService.UpdateUtilizationAsync(warehouseId, utilizationChange);

        // Assert
        result.Should().BeTrue();
        warehouse.CurrentUtilization.Should().Be(700); // 500 + 200
        _mockUnitOfWork.Verify(x => x.Warehouses.Update(warehouse), Times.Once);
        _mockUnitOfWork.Verify(x => x.SaveChangesAsync(It.IsAny<CancellationToken>()), Times.Once);
    }

    [Fact]
    public async Task UpdateUtilizationAsync_ShouldThrowInvalidOperationException_WhenResultingInNegativeUtilization()
    {
        // Arrange
        var warehouseId = 1;
        var utilizationChange = -600m; // Would result in negative utilization
        var warehouse = new Warehouse 
        { 
            Id = warehouseId, 
            MaxCapacity = 1000, 
            CurrentUtilization = 500
        };

        _mockUnitOfWork.Setup(x => x.Warehouses.GetByIdAsync(warehouseId, It.IsAny<CancellationToken>()))
            .ReturnsAsync(warehouse);

        // Act & Assert
        await _warehouseService.Invoking(x => x.UpdateUtilizationAsync(warehouseId, utilizationChange))
            .Should().ThrowAsync<InvalidOperationException>()
            .WithMessage("Warehouse utilization cannot be negative.");
    }

    [Fact]
    public async Task UpdateUtilizationAsync_ShouldThrowInvalidOperationException_WhenExceedingMaxCapacity()
    {
        // Arrange
        var warehouseId = 1;
        var utilizationChange = 600m; // Would exceed max capacity
        var warehouse = new Warehouse 
        { 
            Id = warehouseId, 
            MaxCapacity = 1000, 
            CurrentUtilization = 500
        };

        _mockUnitOfWork.Setup(x => x.Warehouses.GetByIdAsync(warehouseId, It.IsAny<CancellationToken>()))
            .ReturnsAsync(warehouse);

        // Act & Assert
        await _warehouseService.Invoking(x => x.UpdateUtilizationAsync(warehouseId, utilizationChange))
            .Should().ThrowAsync<InvalidOperationException>()
            .WithMessage("Warehouse utilization cannot exceed maximum capacity.");
    }
}
