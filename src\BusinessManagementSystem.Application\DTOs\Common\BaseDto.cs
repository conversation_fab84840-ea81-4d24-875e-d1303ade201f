namespace BusinessManagementSystem.Application.DTOs.Common;

/// <summary>
/// Base DTO class with common properties including audit trail
/// فئة DTO الأساسية مع الخصائص المشتركة بما في ذلك مسار التدقيق
/// </summary>
public abstract class BaseDto
{
    /// <summary>
    /// Entity identifier
    /// معرف الكيان
    /// </summary>
    public int Id { get; set; }

    /// <summary>
    /// Creation date and time
    /// تاريخ ووقت الإنشاء
    /// </summary>
    public DateTime CreatedAt { get; set; }

    /// <summary>
    /// Last update date and time
    /// تاريخ ووقت آخر تحديث
    /// </summary>
    public DateTime UpdatedAt { get; set; }

    /// <summary>
    /// User ID who created this record
    /// معرف المستخدم الذي أنشأ هذا السجل
    /// </summary>
    public int? CreatedBy { get; set; }

    /// <summary>
    /// User ID who last updated this record
    /// معرف المستخدم الذي قام بآخر تحديث لهذا السجل
    /// </summary>
    public int? UpdatedBy { get; set; }

    /// <summary>
    /// Name of user who created this record
    /// اسم المستخدم الذي أنشأ هذا السجل
    /// </summary>
    public string? CreatedByName { get; set; }

    /// <summary>
    /// Name of user who last updated this record
    /// اسم المستخدم الذي قام بآخر تحديث لهذا السجل
    /// </summary>
    public string? UpdatedByName { get; set; }
}
