namespace BusinessManagementSystem.Application.DTOs.Common;

/// <summary>
/// Base DTO class with common properties
/// فئة DTO الأساسية مع الخصائص المشتركة
/// </summary>
public abstract class BaseDto
{
    /// <summary>
    /// Entity identifier
    /// معرف الكيان
    /// </summary>
    public int Id { get; set; }

    /// <summary>
    /// Creation date and time
    /// تاريخ ووقت الإنشاء
    /// </summary>
    public DateTime CreatedAt { get; set; }

    /// <summary>
    /// Last update date and time
    /// تاريخ ووقت آخر تحديث
    /// </summary>
    public DateTime UpdatedAt { get; set; }
}
