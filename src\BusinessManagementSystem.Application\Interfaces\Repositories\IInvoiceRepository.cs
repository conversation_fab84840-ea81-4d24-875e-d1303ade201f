using BusinessManagementSystem.Domain.Entities;

namespace BusinessManagementSystem.Application.Interfaces.Repositories;

/// <summary>
/// Invoice repository interface with specific operations
/// واجهة مستودع الفاتورة مع العمليات المخصصة
/// </summary>
public interface IInvoiceRepository : IGenericRepository<Invoice>
{
    /// <summary>
    /// Get invoice by invoice number
    /// الحصول على الفاتورة بواسطة رقم الفاتورة
    /// </summary>
    Task<Invoice?> GetByInvoiceNumberAsync(string invoiceNumber, CancellationToken cancellationToken = default);

    /// <summary>
    /// Get invoice with details
    /// الحصول على الفاتورة مع التفاصيل
    /// </summary>
    Task<Invoice?> GetWithDetailsAsync(int invoiceId, CancellationToken cancellationToken = default);

    /// <summary>
    /// Get invoices by company and type
    /// الحصول على الفواتير بواسطة الشركة والنوع
    /// </summary>
    Task<IEnumerable<Invoice>> GetByCompanyAndTypeAsync(int companyId, string invoiceType, CancellationToken cancellationToken = default);

    /// <summary>
    /// Get invoices by person (customer/supplier)
    /// الحصول على الفواتير بواسطة الشخص (عميل/مورد)
    /// </summary>
    Task<IEnumerable<Invoice>> GetByPersonAsync(int personId, CancellationToken cancellationToken = default);

    /// <summary>
    /// Get invoices by date range
    /// الحصول على الفواتير بواسطة نطاق التاريخ
    /// </summary>
    Task<IEnumerable<Invoice>> GetByDateRangeAsync(int companyId, DateTime startDate, DateTime endDate, CancellationToken cancellationToken = default);

    /// <summary>
    /// Get sales summary by date range
    /// الحصول على ملخص المبيعات بواسطة نطاق التاريخ
    /// </summary>
    Task<decimal> GetSalesTotalByDateRangeAsync(int companyId, DateTime startDate, DateTime endDate, CancellationToken cancellationToken = default);

    /// <summary>
    /// Get purchases summary by date range
    /// الحصول على ملخص المشتريات بواسطة نطاق التاريخ
    /// </summary>
    Task<decimal> GetPurchasesTotalByDateRangeAsync(int companyId, DateTime startDate, DateTime endDate, CancellationToken cancellationToken = default);

    /// <summary>
    /// Generate next invoice number
    /// توليد رقم الفاتورة التالي
    /// </summary>
    Task<string> GenerateNextInvoiceNumberAsync(int companyId, string invoiceType, CancellationToken cancellationToken = default);
}
