using BusinessManagementSystem.Application.DTOs.Item;
using Microsoft.AspNetCore.Http;

namespace BusinessManagementSystem.Application.Services.Interfaces;

/// <summary>
/// Item service interface for business logic operations
/// واجهة خدمة الصنف لعمليات منطق الأعمال
/// </summary>
public interface IItemService
{
    /// <summary>
    /// Get all items
    /// الحصول على جميع الأصناف
    /// </summary>
    Task<IEnumerable<ItemDto>> GetAllAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// Get item by ID
    /// الحصول على الصنف بواسطة المعرف
    /// </summary>
    Task<ItemDto?> GetByIdAsync(int id, CancellationToken cancellationToken = default);

    /// <summary>
    /// Get item by barcode
    /// الحصول على الصنف بواسطة الباركود
    /// </summary>
    Task<ItemDto?> GetByBarcodeAsync(string barcode, CancellationToken cancellationToken = default);

    /// <summary>
    /// Get items by company
    /// الحصول على الأصناف بواسطة الشركة
    /// </summary>
    Task<IEnumerable<ItemDto>> GetByCompanyAsync(int companyId, CancellationToken cancellationToken = default);

    /// <summary>
    /// Get items by category
    /// الحصول على الأصناف بواسطة الفئة
    /// </summary>
    Task<IEnumerable<ItemDto>> GetByCategoryAsync(int companyId, string category, CancellationToken cancellationToken = default);

    /// <summary>
    /// Search items by name or description
    /// البحث في الأصناف بواسطة الاسم أو الوصف
    /// </summary>
    Task<IEnumerable<ItemDto>> SearchAsync(int companyId, string searchTerm, CancellationToken cancellationToken = default);

    /// <summary>
    /// Get low stock items
    /// الحصول على الأصناف منخفضة المخزون
    /// </summary>
    Task<IEnumerable<ItemDto>> GetLowStockItemsAsync(int companyId, CancellationToken cancellationToken = default);

    /// <summary>
    /// Get active items by company
    /// الحصول على الأصناف النشطة بواسطة الشركة
    /// </summary>
    Task<IEnumerable<ItemDto>> GetActiveByCompanyAsync(int companyId, CancellationToken cancellationToken = default);

    /// <summary>
    /// Create new item
    /// إنشاء صنف جديد
    /// </summary>
    Task<ItemDto> CreateAsync(CreateItemDto createItemDto, CancellationToken cancellationToken = default);

    /// <summary>
    /// Update existing item
    /// تحديث صنف موجود
    /// </summary>
    Task<ItemDto> UpdateAsync(int id, UpdateItemDto updateItemDto, CancellationToken cancellationToken = default);

    /// <summary>
    /// Upload item image
    /// رفع صورة الصنف
    /// </summary>
    Task<string> UploadImageAsync(int id, IFormFile image, CancellationToken cancellationToken = default);

    /// <summary>
    /// Generate barcode for item
    /// توليد باركود للصنف
    /// </summary>
    Task<string> GenerateBarcodeAsync(int id, CancellationToken cancellationToken = default);

    /// <summary>
    /// Set item active status
    /// تعيين حالة تفعيل الصنف
    /// </summary>
    Task<bool> SetActiveStatusAsync(int id, bool isActive, CancellationToken cancellationToken = default);

    /// <summary>
    /// Delete item
    /// حذف الصنف
    /// </summary>
    Task<bool> DeleteAsync(int id, CancellationToken cancellationToken = default);

    /// <summary>
    /// Update stock quantity
    /// تحديث كمية المخزون
    /// </summary>
    Task<bool> UpdateStockQuantityAsync(int id, decimal quantity, string operation, CancellationToken cancellationToken = default);

    /// <summary>
    /// Check if barcode exists
    /// التحقق من وجود الباركود
    /// </summary>
    Task<bool> IsBarcodeExistsAsync(string barcode, int? excludeId = null, CancellationToken cancellationToken = default);

    /// <summary>
    /// Get item categories
    /// الحصول على فئات الأصناف
    /// </summary>
    Task<IEnumerable<string>> GetCategoriesAsync(int companyId, CancellationToken cancellationToken = default);

    /// <summary>
    /// Get current stock value
    /// الحصول على قيمة المخزون الحالية
    /// </summary>
    Task<decimal> GetCurrentStockValueAsync(int companyId, CancellationToken cancellationToken = default);

    /// <summary>
    /// Get items requiring reorder
    /// الحصول على الأصناف التي تحتاج إعادة طلب
    /// </summary>
    Task<IEnumerable<ItemDto>> GetItemsRequiringReorderAsync(int companyId, CancellationToken cancellationToken = default);
}
