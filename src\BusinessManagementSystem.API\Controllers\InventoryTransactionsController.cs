using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using BusinessManagementSystem.Application.DTOs.InventoryTransaction;
using BusinessManagementSystem.Application.Services.Interfaces;
using Microsoft.Extensions.Logging;

namespace BusinessManagementSystem.API.Controllers;

/// <summary>
/// Inventory Transactions controller for stock movement tracking and management
/// تحكم معاملات المخزون لتتبع وإدارة حركة المخزون
/// </summary>
[ApiController]
[Route("api/[controller]")]
[Authorize]
public class InventoryTransactionsController : BaseApiController
{
    private readonly IInventoryTransactionService _inventoryTransactionService;

    public InventoryTransactionsController(IInventoryTransactionService inventoryTransactionService, ILogger<InventoryTransactionsController> logger) : base(logger)
    {
        _inventoryTransactionService = inventoryTransactionService;
    }

    /// <summary>
    /// Get all inventory transactions
    /// الحصول على جميع معاملات المخزون
    /// </summary>
    [HttpGet]
    [Authorize(Roles = "Administrator,Manager,Employee")]
    public async Task<ActionResult<IEnumerable<InventoryTransactionDto>>> GetAll(CancellationToken cancellationToken = default)
    {
        try
        {
            var transactions = await _inventoryTransactionService.GetAllAsync(cancellationToken);
            return Ok(transactions);
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error occurred while getting all inventory transactions");
            return StatusCode(500, "An error occurred while processing your request.");
        }
    }

    /// <summary>
    /// Get inventory transaction by ID
    /// الحصول على معاملة المخزون بواسطة المعرف
    /// </summary>
    [HttpGet("{id}")]
    [Authorize(Roles = "Administrator,Manager,Employee")]
    public async Task<ActionResult<InventoryTransactionDto>> GetById(int id, CancellationToken cancellationToken = default)
    {
        try
        {
            var transaction = await _inventoryTransactionService.GetByIdAsync(id, cancellationToken);
            if (transaction == null)
            {
                return NotFound($"Inventory transaction with ID {id} not found.");
            }

            return Ok(transaction);
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error occurred while getting inventory transaction with ID: {TransactionId}", id);
            return StatusCode(500, "An error occurred while processing your request.");
        }
    }

    /// <summary>
    /// Get inventory transactions by company
    /// الحصول على معاملات المخزون بواسطة الشركة
    /// </summary>
    [HttpGet("company/{companyId}")]
    [Authorize(Roles = "Administrator,Manager,Employee")]
    public async Task<ActionResult<IEnumerable<InventoryTransactionDto>>> GetByCompany(int companyId, CancellationToken cancellationToken = default)
    {
        try
        {
            if (!await HasCompanyAccessAsync(companyId))
            {
                return Forbid("You don't have access to this company's data.");
            }

            var transactions = await _inventoryTransactionService.GetByCompanyAsync(companyId, cancellationToken);
            return Ok(transactions);
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error occurred while getting inventory transactions for company: {CompanyId}", companyId);
            return StatusCode(500, "An error occurred while processing your request.");
        }
    }

    /// <summary>
    /// Get inventory transactions by warehouse
    /// الحصول على معاملات المخزون بواسطة المستودع
    /// </summary>
    [HttpGet("warehouse/{warehouseId}")]
    [Authorize(Roles = "Administrator,Manager,Employee")]
    public async Task<ActionResult<IEnumerable<InventoryTransactionDto>>> GetByWarehouse(int warehouseId, CancellationToken cancellationToken = default)
    {
        try
        {
            var transactions = await _inventoryTransactionService.GetByWarehouseAsync(warehouseId, cancellationToken);
            return Ok(transactions);
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error occurred while getting inventory transactions for warehouse: {WarehouseId}", warehouseId);
            return StatusCode(500, "An error occurred while processing your request.");
        }
    }

    /// <summary>
    /// Get inventory transactions by item
    /// الحصول على معاملات المخزون بواسطة الصنف
    /// </summary>
    [HttpGet("item/{itemId}")]
    [Authorize(Roles = "Administrator,Manager,Employee")]
    public async Task<ActionResult<IEnumerable<InventoryTransactionDto>>> GetByItem(int itemId, CancellationToken cancellationToken = default)
    {
        try
        {
            var transactions = await _inventoryTransactionService.GetByItemAsync(itemId, cancellationToken);
            return Ok(transactions);
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error occurred while getting inventory transactions for item: {ItemId}", itemId);
            return StatusCode(500, "An error occurred while processing your request.");
        }
    }

    /// <summary>
    /// Get inventory transactions by type
    /// الحصول على معاملات المخزون بواسطة النوع
    /// </summary>
    [HttpGet("type/{transactionType}")]
    [Authorize(Roles = "Administrator,Manager,Employee")]
    public async Task<ActionResult<IEnumerable<InventoryTransactionDto>>> GetByType(string transactionType, [FromQuery] int companyId, CancellationToken cancellationToken = default)
    {
        try
        {
            if (!await HasCompanyAccessAsync(companyId))
            {
                return Forbid("You don't have access to this company's data.");
            }

            var transactions = await _inventoryTransactionService.GetByTypeAsync(companyId, transactionType, cancellationToken);
            return Ok(transactions);
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error occurred while getting inventory transactions by type: {TransactionType} for company: {CompanyId}", transactionType, companyId);
            return StatusCode(500, "An error occurred while processing your request.");
        }
    }

    /// <summary>
    /// Get inventory transactions by date range
    /// الحصول على معاملات المخزون بواسطة نطاق التاريخ
    /// </summary>
    [HttpGet("date-range")]
    [Authorize(Roles = "Administrator,Manager,Employee")]
    public async Task<ActionResult<IEnumerable<InventoryTransactionDto>>> GetByDateRange([FromQuery] int companyId, [FromQuery] DateTime startDate, [FromQuery] DateTime endDate, CancellationToken cancellationToken = default)
    {
        try
        {
            if (!await HasCompanyAccessAsync(companyId))
            {
                return Forbid("You don't have access to this company's data.");
            }

            var transactions = await _inventoryTransactionService.GetByDateRangeAsync(companyId, startDate, endDate, cancellationToken);
            return Ok(transactions);
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error occurred while getting inventory transactions by date range for company: {CompanyId}", companyId);
            return StatusCode(500, "An error occurred while processing your request.");
        }
    }

    /// <summary>
    /// Search inventory transactions
    /// البحث في معاملات المخزون
    /// </summary>
    [HttpGet("search")]
    [Authorize(Roles = "Administrator,Manager,Employee")]
    public async Task<ActionResult<IEnumerable<InventoryTransactionDto>>> Search([FromQuery] int companyId, [FromQuery] string searchTerm, CancellationToken cancellationToken = default)
    {
        try
        {
            if (!await HasCompanyAccessAsync(companyId))
            {
                return Forbid("You don't have access to this company's data.");
            }

            if (string.IsNullOrWhiteSpace(searchTerm))
            {
                return BadRequest("Search term is required.");
            }

            var transactions = await _inventoryTransactionService.SearchAsync(companyId, searchTerm, cancellationToken);
            return Ok(transactions);
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error occurred while searching inventory transactions for company: {CompanyId}, term: {SearchTerm}", companyId, searchTerm);
            return StatusCode(500, "An error occurred while processing your request.");
        }
    }

    /// <summary>
    /// Create new inventory transaction
    /// إنشاء معاملة مخزون جديدة
    /// </summary>
    [HttpPost]
    [Authorize(Roles = "Administrator,Manager,Employee")]
    public async Task<ActionResult<InventoryTransactionDto>> Create([FromBody] CreateInventoryTransactionDto createInventoryTransactionDto, CancellationToken cancellationToken = default)
    {
        try
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ModelState);
            }

            if (!await HasCompanyAccessAsync(createInventoryTransactionDto.CompanyId))
            {
                return Forbid("You don't have access to this company's data.");
            }

            var transaction = await _inventoryTransactionService.CreateAsync(createInventoryTransactionDto, cancellationToken);
            
            Logger.LogInformation("Inventory transaction created successfully with ID: {TransactionId} by user: {UserId}", transaction.Id, GetCurrentUserId());
            return CreatedAtAction(nameof(GetById), new { id = transaction.Id }, transaction);
        }
        catch (ArgumentException ex)
        {
            Logger.LogWarning(ex, "Invalid argument while creating inventory transaction");
            return BadRequest(ex.Message);
        }
        catch (InvalidOperationException ex)
        {
            Logger.LogWarning(ex, "Invalid operation while creating inventory transaction");
            return BadRequest(ex.Message);
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error occurred while creating inventory transaction");
            return StatusCode(500, "An error occurred while processing your request.");
        }
    }

    /// <summary>
    /// Update existing inventory transaction
    /// تحديث معاملة مخزون موجودة
    /// </summary>
    [HttpPut("{id}")]
    [Authorize(Roles = "Administrator,Manager")]
    public async Task<ActionResult<InventoryTransactionDto>> Update(int id, [FromBody] UpdateInventoryTransactionDto updateInventoryTransactionDto, CancellationToken cancellationToken = default)
    {
        try
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ModelState);
            }

            var existingTransaction = await _inventoryTransactionService.GetByIdAsync(id, cancellationToken);
            if (existingTransaction == null)
            {
                return NotFound($"Inventory transaction with ID {id} not found.");
            }

            if (!await HasCompanyAccessAsync(existingTransaction.CompanyId))
            {
                return Forbid("You don't have access to this company's data.");
            }

            var transaction = await _inventoryTransactionService.UpdateAsync(id, updateInventoryTransactionDto, cancellationToken);
            
            Logger.LogInformation("Inventory transaction updated successfully with ID: {TransactionId} by user: {UserId}", id, GetCurrentUserId());
            return Ok(transaction);
        }
        catch (ArgumentException ex)
        {
            Logger.LogWarning(ex, "Invalid argument while updating inventory transaction with ID: {TransactionId}", id);
            return BadRequest(ex.Message);
        }
        catch (InvalidOperationException ex)
        {
            Logger.LogWarning(ex, "Invalid operation while updating inventory transaction with ID: {TransactionId}", id);
            return BadRequest(ex.Message);
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error occurred while updating inventory transaction with ID: {TransactionId}", id);
            return StatusCode(500, "An error occurred while processing your request.");
        }
    }

    /// <summary>
    /// Delete inventory transaction
    /// حذف معاملة المخزون
    /// </summary>
    [HttpDelete("{id}")]
    [Authorize(Roles = "Administrator")]
    public async Task<ActionResult> Delete(int id, CancellationToken cancellationToken = default)
    {
        try
        {
            var existingTransaction = await _inventoryTransactionService.GetByIdAsync(id, cancellationToken);
            if (existingTransaction == null)
            {
                return NotFound($"Inventory transaction with ID {id} not found.");
            }

            if (!await HasCompanyAccessAsync(existingTransaction.CompanyId))
            {
                return Forbid("You don't have access to this company's data.");
            }

            var result = await _inventoryTransactionService.DeleteAsync(id, cancellationToken);
            if (!result)
            {
                return NotFound($"Inventory transaction with ID {id} not found.");
            }

            Logger.LogInformation("Inventory transaction deleted successfully with ID: {TransactionId} by user: {UserId}", id, GetCurrentUserId());
            return NoContent();
        }
        catch (InvalidOperationException ex)
        {
            Logger.LogWarning(ex, "Invalid operation while deleting inventory transaction with ID: {TransactionId}", id);
            return BadRequest(ex.Message);
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error occurred while deleting inventory transaction with ID: {TransactionId}", id);
            return StatusCode(500, "An error occurred while processing your request.");
        }
    }

    /// <summary>
    /// Process stock in transaction
    /// معالجة معاملة دخول المخزون
    /// </summary>
    [HttpPost("stock-in")]
    [Authorize(Roles = "Administrator,Manager,Employee")]
    public async Task<ActionResult<InventoryTransactionDto>> ProcessStockIn([FromBody] StockInRequestDto request, CancellationToken cancellationToken = default)
    {
        try
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ModelState);
            }

            var transaction = await _inventoryTransactionService.ProcessStockInAsync(
                request.ItemId, request.WarehouseId, request.Quantity, request.UnitCost, request.Notes, request.InvoiceId, cancellationToken);
            
            Logger.LogInformation("Stock in transaction processed successfully with ID: {TransactionId} by user: {UserId}", transaction.Id, GetCurrentUserId());
            return CreatedAtAction(nameof(GetById), new { id = transaction.Id }, transaction);
        }
        catch (ArgumentException ex)
        {
            Logger.LogWarning(ex, "Invalid argument while processing stock in");
            return BadRequest(ex.Message);
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error occurred while processing stock in");
            return StatusCode(500, "An error occurred while processing your request.");
        }
    }

    /// <summary>
    /// Process stock out transaction
    /// معالجة معاملة خروج المخزون
    /// </summary>
    [HttpPost("stock-out")]
    [Authorize(Roles = "Administrator,Manager,Employee")]
    public async Task<ActionResult<InventoryTransactionDto>> ProcessStockOut([FromBody] StockOutRequestDto request, CancellationToken cancellationToken = default)
    {
        try
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ModelState);
            }

            var transaction = await _inventoryTransactionService.ProcessStockOutAsync(
                request.ItemId, request.WarehouseId, request.Quantity, request.Notes, request.InvoiceId, cancellationToken);
            
            Logger.LogInformation("Stock out transaction processed successfully with ID: {TransactionId} by user: {UserId}", transaction.Id, GetCurrentUserId());
            return CreatedAtAction(nameof(GetById), new { id = transaction.Id }, transaction);
        }
        catch (ArgumentException ex)
        {
            Logger.LogWarning(ex, "Invalid argument while processing stock out");
            return BadRequest(ex.Message);
        }
        catch (InvalidOperationException ex)
        {
            Logger.LogWarning(ex, "Invalid operation while processing stock out");
            return BadRequest(ex.Message);
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error occurred while processing stock out");
            return StatusCode(500, "An error occurred while processing your request.");
        }
    }

    /// <summary>
    /// Process stock adjustment transaction
    /// معالجة معاملة تعديل المخزون
    /// </summary>
    [HttpPost("stock-adjustment")]
    [Authorize(Roles = "Administrator,Manager")]
    public async Task<ActionResult<InventoryTransactionDto>> ProcessStockAdjustment([FromBody] StockAdjustmentRequestDto request, CancellationToken cancellationToken = default)
    {
        try
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ModelState);
            }

            var transaction = await _inventoryTransactionService.ProcessStockAdjustmentAsync(
                request.ItemId, request.WarehouseId, request.AdjustmentQuantity, request.Reason, cancellationToken);
            
            Logger.LogInformation("Stock adjustment transaction processed successfully with ID: {TransactionId} by user: {UserId}", transaction.Id, GetCurrentUserId());
            return CreatedAtAction(nameof(GetById), new { id = transaction.Id }, transaction);
        }
        catch (ArgumentException ex)
        {
            Logger.LogWarning(ex, "Invalid argument while processing stock adjustment");
            return BadRequest(ex.Message);
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error occurred while processing stock adjustment");
            return StatusCode(500, "An error occurred while processing your request.");
        }
    }

    /// <summary>
    /// Process stock transfer between warehouses
    /// معالجة نقل المخزون بين المستودعات
    /// </summary>
    [HttpPost("stock-transfer")]
    [Authorize(Roles = "Administrator,Manager")]
    public async Task<ActionResult<object>> ProcessStockTransfer([FromBody] StockTransferRequestDto request, CancellationToken cancellationToken = default)
    {
        try
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ModelState);
            }

            var (outTransaction, inTransaction) = await _inventoryTransactionService.ProcessStockTransferAsync(
                request.ItemId, request.FromWarehouseId, request.ToWarehouseId, request.Quantity, request.Notes, cancellationToken);
            
            Logger.LogInformation("Stock transfer processed successfully from warehouse {FromId} to {ToId} by user: {UserId}", 
                request.FromWarehouseId, request.ToWarehouseId, GetCurrentUserId());
            
            return Ok(new { OutTransaction = outTransaction, InTransaction = inTransaction });
        }
        catch (ArgumentException ex)
        {
            Logger.LogWarning(ex, "Invalid argument while processing stock transfer");
            return BadRequest(ex.Message);
        }
        catch (InvalidOperationException ex)
        {
            Logger.LogWarning(ex, "Invalid operation while processing stock transfer");
            return BadRequest(ex.Message);
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error occurred while processing stock transfer");
            return StatusCode(500, "An error occurred while processing your request.");
        }
    }

    /// <summary>
    /// Get inventory movement report
    /// الحصول على تقرير حركة المخزون
    /// </summary>
    [HttpGet("movement-report")]
    [Authorize(Roles = "Administrator,Manager")]
    public async Task<ActionResult<InventoryMovementReportDto>> GetMovementReport([FromQuery] int companyId, [FromQuery] DateTime startDate, [FromQuery] DateTime endDate, CancellationToken cancellationToken = default)
    {
        try
        {
            if (!await HasCompanyAccessAsync(companyId))
            {
                return Forbid("You don't have access to this company's data.");
            }

            var report = await _inventoryTransactionService.GetMovementReportAsync(companyId, startDate, endDate, cancellationToken);
            return Ok(report);
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error occurred while getting movement report for company: {CompanyId}", companyId);
            return StatusCode(500, "An error occurred while processing your request.");
        }
    }

    /// <summary>
    /// Calculate current stock for item in warehouse
    /// حساب المخزون الحالي للصنف في المستودع
    /// </summary>
    [HttpGet("current-stock")]
    [Authorize(Roles = "Administrator,Manager,Employee")]
    public async Task<ActionResult<decimal>> CalculateCurrentStock([FromQuery] int itemId, [FromQuery] int warehouseId, CancellationToken cancellationToken = default)
    {
        try
        {
            var currentStock = await _inventoryTransactionService.CalculateCurrentStockAsync(itemId, warehouseId, cancellationToken);
            return Ok(currentStock);
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error occurred while calculating current stock for item: {ItemId} in warehouse: {WarehouseId}", itemId, warehouseId);
            return StatusCode(500, "An error occurred while processing your request.");
        }
    }
}

// Request DTOs for specific operations
public class StockInRequestDto
{
    public int ItemId { get; set; }
    public int WarehouseId { get; set; }
    public decimal Quantity { get; set; }
    public decimal UnitCost { get; set; }
    public string? Notes { get; set; }
    public int? InvoiceId { get; set; }
}

public class StockOutRequestDto
{
    public int ItemId { get; set; }
    public int WarehouseId { get; set; }
    public decimal Quantity { get; set; }
    public string? Notes { get; set; }
    public int? InvoiceId { get; set; }
}

public class StockAdjustmentRequestDto
{
    public int ItemId { get; set; }
    public int WarehouseId { get; set; }
    public decimal AdjustmentQuantity { get; set; }
    public string Reason { get; set; } = string.Empty;
}

public class StockTransferRequestDto
{
    public int ItemId { get; set; }
    public int FromWarehouseId { get; set; }
    public int ToWarehouseId { get; set; }
    public decimal Quantity { get; set; }
    public string? Notes { get; set; }
}
