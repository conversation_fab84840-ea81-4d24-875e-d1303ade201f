using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using BusinessManagementSystem.Domain.Common;

namespace BusinessManagementSystem.Domain.Entities;

/// <summary>
/// Partner entity - stores business partner information
/// كيان الشريك - يخزن معلومات الشركاء التجاريين
/// </summary>
public class Partner : BaseEntity
{
    /// <summary>
    /// Partner name
    /// اسم الشريك
    /// </summary>
    [Required]
    [MaxLength(200)]
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// Partner address
    /// عنوان الشريك
    /// </summary>
    [MaxLength(500)]
    public string? Address { get; set; }

    /// <summary>
    /// Initial capital contribution
    /// رأس المال الأولي
    /// </summary>
    [Column(TypeName = "decimal(18,2)")]
    public decimal InitialCapital { get; set; }

    /// <summary>
    /// Ownership percentage
    /// نسبة الملكية
    /// </summary>
    [Column(TypeName = "decimal(5,2)")]
    public decimal OwnershipPercentage { get; set; }

    /// <summary>
    /// Company identifier (Foreign Key)
    /// معرف الشركة (مفتاح خارجي)
    /// </summary>
    [Required]
    public int CompanyId { get; set; }

    // Navigation properties
    [ForeignKey(nameof(CompanyId))]
    public virtual Company Company { get; set; } = null!;
}
