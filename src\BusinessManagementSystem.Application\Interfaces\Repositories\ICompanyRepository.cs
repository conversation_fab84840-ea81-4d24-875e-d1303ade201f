using BusinessManagementSystem.Domain.Entities;

namespace BusinessManagementSystem.Application.Interfaces.Repositories;

/// <summary>
/// Company repository interface with specific operations
/// واجهة مستودع الشركة مع العمليات المخصصة
/// </summary>
public interface ICompanyRepository : IGenericRepository<Company>
{
    /// <summary>
    /// Get company by name
    /// الحصول على الشركة بواسطة الاسم
    /// </summary>
    Task<Company?> GetByNameAsync(string name, CancellationToken cancellationToken = default);

    /// <summary>
    /// Get company by commercial registration number
    /// الحصول على الشركة بواسطة رقم السجل التجاري
    /// </summary>
    Task<Company?> GetByCommercialRegistrationNumberAsync(string registrationNumber, CancellationToken cancellationToken = default);

    /// <summary>
    /// Get company by tax number
    /// الحصول على الشركة بواسطة الرقم الضريبي
    /// </summary>
    Task<Company?> GetByTaxNumberAsync(string taxNumber, CancellationToken cancellationToken = default);

    /// <summary>
    /// Get company with all related data
    /// الحصول على الشركة مع جميع البيانات المرتبطة
    /// </summary>
    Task<Company?> GetWithAllRelatedDataAsync(int companyId, CancellationToken cancellationToken = default);
}
