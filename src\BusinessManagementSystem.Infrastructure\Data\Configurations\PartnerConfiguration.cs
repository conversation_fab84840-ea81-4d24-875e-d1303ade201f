using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using BusinessManagementSystem.Domain.Entities;

namespace BusinessManagementSystem.Infrastructure.Data.Configurations;

/// <summary>
/// Entity configuration for Partner
/// تكوين الكيان للشريك
/// </summary>
public class PartnerConfiguration : IEntityTypeConfiguration<Partner>
{
    public void Configure(EntityTypeBuilder<Partner> builder)
    {
        builder.ToTable("Partners", t =>
        {
            t.HasCheckConstraint("CK_Partner_OwnershipPercentage", "[OwnershipPercentage] >= 0 AND [OwnershipPercentage] <= 100");
        });

        builder.HasKey(p => p.Id);

        builder.Property(p => p.Name)
            .IsRequired()
            .HasMaxLength(200);

        builder.Property(p => p.Address)
            .HasMaxLength(500);

        builder.Property(p => p.InitialCapital)
            .HasColumnType("decimal(18,2)")
            .HasDefaultValue(0);

        builder.Property(p => p.OwnershipPercentage)
            .HasColumnType("decimal(5,2)")
            .IsRequired();

        // Indexes
        builder.HasIndex(p => new { p.CompanyId, p.Name });

        // Relationships
        builder.HasOne(p => p.Company)
            .WithMany(c => c.Partners)
            .HasForeignKey(p => p.CompanyId)
            .OnDelete(DeleteBehavior.Cascade);
    }
}
