using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using BusinessManagementSystem.Domain.Entities;

namespace BusinessManagementSystem.Infrastructure.Data.Configurations;

/// <summary>
/// Entity configuration for InvoiceDetail
/// تكوين الكيان لتفاصيل الفاتورة
/// </summary>
public class InvoiceDetailConfiguration : IEntityTypeConfiguration<InvoiceDetail>
{
    public void Configure(EntityTypeBuilder<InvoiceDetail> builder)
    {
        builder.ToTable("InvoiceDetails", t =>
        {
            t.HasCheckConstraint("CK_InvoiceDetail_Quantity", "[Quantity] > 0");
            t.HasCheckConstraint("CK_InvoiceDetail_UnitPrice", "[UnitPrice] >= 0");
            t.HasCheckConstraint("CK_InvoiceDetail_DiscountPercentage", "[DiscountPercentage] >= 0 AND [DiscountPercentage] <= 100");
        });

        builder.HasKey(id => id.Id);

        builder.Property(id => id.Quantity)
            .IsRequired();

        builder.Property(id => id.UnitPrice)
            .HasColumnType("decimal(18,2)")
            .IsRequired();

        builder.Property(id => id.LineTotal)
            .HasColumnType("decimal(18,2)")
            .IsRequired();

        builder.Property(id => id.DiscountPercentage)
            .HasColumnType("decimal(5,2)")
            .HasDefaultValue(0);

        // Indexes
        builder.HasIndex(id => id.InvoiceId);

        builder.HasIndex(id => new { id.InvoiceId, id.ItemId });

        // Relationships
        builder.HasOne(id => id.Invoice)
            .WithMany(i => i.InvoiceDetails)
            .HasForeignKey(id => id.InvoiceId)
            .OnDelete(DeleteBehavior.Cascade);

        builder.HasOne(id => id.Item)
            .WithMany(i => i.InvoiceDetails)
            .HasForeignKey(id => id.ItemId)
            .OnDelete(DeleteBehavior.Restrict);
    }
}
