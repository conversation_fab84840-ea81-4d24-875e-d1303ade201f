using BusinessManagementSystem.Application.DTOs.Invoice;

namespace BusinessManagementSystem.Application.Services.Interfaces;

/// <summary>
/// Invoice service interface for business logic operations
/// واجهة خدمة الفاتورة لعمليات منطق الأعمال
/// </summary>
public interface IInvoiceService
{
    /// <summary>
    /// Get all invoices
    /// الحصول على جميع الفواتير
    /// </summary>
    Task<IEnumerable<InvoiceDto>> GetAllAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// Get invoice by ID with details
    /// الحصول على الفاتورة بواسطة المعرف مع التفاصيل
    /// </summary>
    Task<InvoiceDto?> GetByIdAsync(int id, CancellationToken cancellationToken = default);

    /// <summary>
    /// Get invoice by invoice number
    /// الحصول على الفاتورة بواسطة رقم الفاتورة
    /// </summary>
    Task<InvoiceDto?> GetByInvoiceNumberAsync(string invoiceNumber, CancellationToken cancellationToken = default);

    /// <summary>
    /// Get invoices by company and type
    /// الحصول على الفواتير بواسطة الشركة والنوع
    /// </summary>
    Task<IEnumerable<InvoiceDto>> GetByCompanyAndTypeAsync(int companyId, string invoiceType, CancellationToken cancellationToken = default);

    /// <summary>
    /// Get invoices by person (customer/supplier)
    /// الحصول على الفواتير بواسطة الشخص (عميل/مورد)
    /// </summary>
    Task<IEnumerable<InvoiceDto>> GetByPersonAsync(int personId, CancellationToken cancellationToken = default);

    /// <summary>
    /// Get invoices by date range
    /// الحصول على الفواتير بواسطة نطاق التاريخ
    /// </summary>
    Task<IEnumerable<InvoiceDto>> GetByDateRangeAsync(int companyId, DateTime startDate, DateTime endDate, CancellationToken cancellationToken = default);

    /// <summary>
    /// Create new invoice
    /// إنشاء فاتورة جديدة
    /// </summary>
    Task<InvoiceDto> CreateAsync(CreateInvoiceDto createInvoiceDto, CancellationToken cancellationToken = default);

    /// <summary>
    /// Update existing invoice
    /// تحديث فاتورة موجودة
    /// </summary>
    Task<InvoiceDto> UpdateAsync(int id, CreateInvoiceDto updateInvoiceDto, CancellationToken cancellationToken = default);

    /// <summary>
    /// Confirm invoice (change status to confirmed)
    /// تأكيد الفاتورة (تغيير الحالة إلى مؤكدة)
    /// </summary>
    Task<bool> ConfirmInvoiceAsync(int id, CancellationToken cancellationToken = default);

    /// <summary>
    /// Cancel invoice
    /// إلغاء الفاتورة
    /// </summary>
    Task<bool> CancelInvoiceAsync(int id, CancellationToken cancellationToken = default);

    /// <summary>
    /// Delete invoice
    /// حذف الفاتورة
    /// </summary>
    Task<bool> DeleteAsync(int id, CancellationToken cancellationToken = default);

    /// <summary>
    /// Get sales summary by date range
    /// الحصول على ملخص المبيعات بواسطة نطاق التاريخ
    /// </summary>
    Task<decimal> GetSalesTotalByDateRangeAsync(int companyId, DateTime startDate, DateTime endDate, CancellationToken cancellationToken = default);

    /// <summary>
    /// Get purchases summary by date range
    /// الحصول على ملخص المشتريات بواسطة نطاق التاريخ
    /// </summary>
    Task<decimal> GetPurchasesTotalByDateRangeAsync(int companyId, DateTime startDate, DateTime endDate, CancellationToken cancellationToken = default);

    /// <summary>
    /// Generate next invoice number
    /// توليد رقم الفاتورة التالي
    /// </summary>
    Task<string> GenerateNextInvoiceNumberAsync(int companyId, string invoiceType, CancellationToken cancellationToken = default);
}
