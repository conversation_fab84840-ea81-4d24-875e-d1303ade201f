using BusinessManagementSystem.Application.DTOs.Department;

namespace BusinessManagementSystem.Application.Services.Interfaces;

/// <summary>
/// Department service interface for business logic operations
/// واجهة خدمة القسم لعمليات منطق الأعمال
/// </summary>
public interface IDepartmentService
{
    /// <summary>
    /// Get all departments
    /// الحصول على جميع الأقسام
    /// </summary>
    Task<IEnumerable<DepartmentDto>> GetAllAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// Get department by ID
    /// الحصول على القسم بواسطة المعرف
    /// </summary>
    Task<DepartmentDto?> GetByIdAsync(int id, CancellationToken cancellationToken = default);

    /// <summary>
    /// Get departments by company
    /// الحصول على الأقسام بواسطة الشركة
    /// </summary>
    Task<IEnumerable<DepartmentDto>> GetByCompanyAsync(int companyId, CancellationToken cancellationToken = default);

    /// <summary>
    /// Get active departments by company
    /// الحصول على الأقسام النشطة بواسطة الشركة
    /// </summary>
    Task<IEnumerable<DepartmentDto>> GetActiveByCompanyAsync(int companyId, CancellationToken cancellationToken = default);

    /// <summary>
    /// Create new department
    /// إنشاء قسم جديد
    /// </summary>
    Task<DepartmentDto> CreateAsync(CreateDepartmentDto createDepartmentDto, CancellationToken cancellationToken = default);

    /// <summary>
    /// Update existing department
    /// تحديث قسم موجود
    /// </summary>
    Task<DepartmentDto> UpdateAsync(int id, UpdateDepartmentDto updateDepartmentDto, CancellationToken cancellationToken = default);

    /// <summary>
    /// Set department active status
    /// تعيين حالة تفعيل القسم
    /// </summary>
    Task<bool> SetActiveStatusAsync(int id, bool isActive, CancellationToken cancellationToken = default);

    /// <summary>
    /// Delete department
    /// حذف القسم
    /// </summary>
    Task<bool> DeleteAsync(int id, CancellationToken cancellationToken = default);

    /// <summary>
    /// Get department statistics
    /// الحصول على إحصائيات القسم
    /// </summary>
    Task<object> GetDepartmentStatisticsAsync(int id, CancellationToken cancellationToken = default);

    /// <summary>
    /// Get users by department
    /// الحصول على المستخدمين بواسطة القسم
    /// </summary>
    Task<IEnumerable<object>> GetUsersByDepartmentAsync(int id, CancellationToken cancellationToken = default);
}
