using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using BusinessManagementSystem.Application.DTOs.Invoice;
using BusinessManagementSystem.Application.Services.Interfaces;

namespace BusinessManagementSystem.API.Controllers;

/// <summary>
/// Invoices controller for invoice management operations
/// وحدة تحكم الفواتير لعمليات إدارة الفواتير
/// </summary>
[Authorize]
public class InvoicesController : BaseApiController
{
    private readonly IInvoiceService _invoiceService;
    

    public InvoicesController(IInvoiceService invoiceService, ILogger<InvoicesController> logger) : base(logger)
    {
        _invoiceService = invoiceService;
        
    }

    /// <summary>
    /// Get all invoices with pagination
    /// الحصول على جميع الفواتير مع التصفح
    /// </summary>
    [HttpGet]
    public async Task<IActionResult> GetAll(
        [FromQuery] int page = 1, 
        [FromQuery] int pageSize = 10,
        [FromQuery] string? invoiceType = null,
        [FromQuery] DateTime? startDate = null,
        [FromQuery] DateTime? endDate = null,
        CancellationToken cancellationToken = default)
    {
        try
        {
            if (!HasPermission("Invoices", "View"))
            {
                return CreateErrorResponse("You don't have permission to view invoices.", 403);
            }

            var companyId = GetCurrentCompanyId();
            IEnumerable<InvoiceDto> invoices;

            if (!string.IsNullOrEmpty(invoiceType))
            {
                invoices = await _invoiceService.GetByCompanyAndTypeAsync(companyId, invoiceType, cancellationToken);
            }
            else if (startDate.HasValue && endDate.HasValue)
            {
                invoices = await _invoiceService.GetByDateRangeAsync(companyId, startDate.Value, endDate.Value, cancellationToken);
            }
            else
            {
                invoices = await _invoiceService.GetAllAsync(cancellationToken);
                invoices = invoices.Where(i => i.CompanyId == companyId);
            }

            // Apply pagination
            var totalCount = invoices.Count();
            var pagedInvoices = invoices
                .Skip((page - 1) * pageSize)
                .Take(pageSize)
                .ToList();

            var result = new
            {
                Items = pagedInvoices,
                TotalCount = totalCount,
                Page = page,
                PageSize = pageSize,
                TotalPages = (int)Math.Ceiling((double)totalCount / pageSize)
            };

            return CreateResponse(result, "Invoices retrieved successfully.");
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error retrieving invoices");
            return CreateErrorResponse("An error occurred while retrieving invoices.", 500);
        }
    }

    /// <summary>
    /// Get invoice by ID with details
    /// الحصول على الفاتورة بواسطة المعرف مع التفاصيل
    /// </summary>
    [HttpGet("{id}")]
    public async Task<IActionResult> GetById(int id, CancellationToken cancellationToken = default)
    {
        try
        {
            if (!HasPermission("Invoices", "View"))
            {
                return CreateErrorResponse("You don't have permission to view invoices.", 403);
            }

            var invoice = await _invoiceService.GetByIdAsync(id, cancellationToken);
            if (invoice == null)
            {
                return CreateErrorResponse("Invoice not found.", 404);
            }

            // Check if user can access this invoice (same company)
            var companyId = GetCurrentCompanyId();
            if (invoice.CompanyId != companyId)
            {
                return CreateErrorResponse("You don't have permission to view this invoice.", 403);
            }

            return CreateResponse(invoice, "Invoice retrieved successfully.");
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error retrieving invoice with ID: {InvoiceId}", id);
            return CreateErrorResponse("An error occurred while retrieving the invoice.", 500);
        }
    }

    /// <summary>
    /// Get invoice by invoice number
    /// الحصول على الفاتورة بواسطة رقم الفاتورة
    /// </summary>
    [HttpGet("by-number/{invoiceNumber}")]
    public async Task<IActionResult> GetByInvoiceNumber(string invoiceNumber, CancellationToken cancellationToken = default)
    {
        try
        {
            if (!HasPermission("Invoices", "View"))
            {
                return CreateErrorResponse("You don't have permission to view invoices.", 403);
            }

            var invoice = await _invoiceService.GetByInvoiceNumberAsync(invoiceNumber, cancellationToken);
            if (invoice == null)
            {
                return CreateErrorResponse("Invoice not found.", 404);
            }

            // Check if user can access this invoice (same company)
            var companyId = GetCurrentCompanyId();
            if (invoice.CompanyId != companyId)
            {
                return CreateErrorResponse("You don't have permission to view this invoice.", 403);
            }

            return CreateResponse(invoice, "Invoice retrieved successfully.");
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error retrieving invoice with number: {InvoiceNumber}", invoiceNumber);
            return CreateErrorResponse("An error occurred while retrieving the invoice.", 500);
        }
    }

    /// <summary>
    /// Create new invoice
    /// إنشاء فاتورة جديدة
    /// </summary>
    [HttpPost]
    public async Task<IActionResult> Create([FromBody] CreateInvoiceDto createInvoiceDto, CancellationToken cancellationToken = default)
    {
        try
        {
            if (!HasPermission("Invoices", "Add"))
            {
                return CreateErrorResponse("You don't have permission to create invoices.", 403);
            }

            if (!ModelState.IsValid)
            {
                return CreateErrorResponse("Invalid invoice data.", 400);
            }

            // Ensure the invoice is created for the current user's company
            createInvoiceDto.CompanyId = GetCurrentCompanyId();

            var invoice = await _invoiceService.CreateAsync(createInvoiceDto, cancellationToken);
            
            Logger.LogInformation("Invoice created successfully with ID: {InvoiceId} by user: {UserId}", 
                invoice.Id, GetCurrentUserId());
            
            return CreatedAtAction(nameof(GetById), new { id = invoice.Id }, 
                CreateResponse(invoice, "Invoice created successfully."));
        }
        catch (ArgumentException ex)
        {
            Logger.LogWarning(ex, "Validation error creating invoice");
            return CreateErrorResponse(ex.Message, 400);
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error creating invoice");
            return CreateErrorResponse("An error occurred while creating the invoice.", 500);
        }
    }

    /// <summary>
    /// Update existing invoice
    /// تحديث فاتورة موجودة
    /// </summary>
    [HttpPut("{id}")]
    public async Task<IActionResult> Update(int id, [FromBody] CreateInvoiceDto updateInvoiceDto, CancellationToken cancellationToken = default)
    {
        try
        {
            if (!HasPermission("Invoices", "Edit"))
            {
                return CreateErrorResponse("You don't have permission to edit invoices.", 403);
            }

            if (!ModelState.IsValid)
            {
                return CreateErrorResponse("Invalid invoice data.", 400);
            }

            // Ensure the invoice belongs to the current user's company
            updateInvoiceDto.CompanyId = GetCurrentCompanyId();

            var invoice = await _invoiceService.UpdateAsync(id, updateInvoiceDto, cancellationToken);
            
            Logger.LogInformation("Invoice updated successfully with ID: {InvoiceId} by user: {UserId}", 
                id, GetCurrentUserId());
            
            return CreateResponse(invoice, "Invoice updated successfully.");
        }
        catch (ArgumentException ex)
        {
            Logger.LogWarning(ex, "Validation error updating invoice with ID: {InvoiceId}", id);
            return CreateErrorResponse(ex.Message, 400);
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error updating invoice with ID: {InvoiceId}", id);
            return CreateErrorResponse("An error occurred while updating the invoice.", 500);
        }
    }

    /// <summary>
    /// Confirm invoice
    /// تأكيد الفاتورة
    /// </summary>
    [HttpPost("{id}/confirm")]
    public async Task<IActionResult> Confirm(int id, CancellationToken cancellationToken = default)
    {
        try
        {
            if (!HasPermission("Invoices", "Edit"))
            {
                return CreateErrorResponse("You don't have permission to confirm invoices.", 403);
            }

            var result = await _invoiceService.ConfirmInvoiceAsync(id, cancellationToken);
            if (!result)
            {
                return CreateErrorResponse("Invoice not found or cannot be confirmed.", 400);
            }

            Logger.LogInformation("Invoice confirmed successfully with ID: {InvoiceId} by user: {UserId}", 
                id, GetCurrentUserId());
            
            return CreateResponse<object?>(null, "Invoice confirmed successfully.");
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error confirming invoice with ID: {InvoiceId}", id);
            return CreateErrorResponse("An error occurred while confirming the invoice.", 500);
        }
    }

    /// <summary>
    /// Cancel invoice
    /// إلغاء الفاتورة
    /// </summary>
    [HttpPost("{id}/cancel")]
    public async Task<IActionResult> Cancel(int id, CancellationToken cancellationToken = default)
    {
        try
        {
            if (!HasPermission("Invoices", "Edit"))
            {
                return CreateErrorResponse("You don't have permission to cancel invoices.", 403);
            }

            var result = await _invoiceService.CancelInvoiceAsync(id, cancellationToken);
            if (!result)
            {
                return CreateErrorResponse("Invoice not found or cannot be cancelled.", 400);
            }

            Logger.LogInformation("Invoice cancelled successfully with ID: {InvoiceId} by user: {UserId}", 
                id, GetCurrentUserId());
            
            return CreateResponse<object?>(null, "Invoice cancelled successfully.");
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error cancelling invoice with ID: {InvoiceId}", id);
            return CreateErrorResponse("An error occurred while cancelling the invoice.", 500);
        }
    }

    /// <summary>
    /// Delete invoice
    /// حذف الفاتورة
    /// </summary>
    [HttpDelete("{id}")]
    public async Task<IActionResult> Delete(int id, CancellationToken cancellationToken = default)
    {
        try
        {
            if (!HasPermission("Invoices", "Delete"))
            {
                return CreateErrorResponse("You don't have permission to delete invoices.", 403);
            }

            var result = await _invoiceService.DeleteAsync(id, cancellationToken);
            if (!result)
            {
                return CreateErrorResponse("Invoice not found.", 404);
            }

            Logger.LogInformation("Invoice deleted successfully with ID: {InvoiceId} by user: {UserId}", 
                id, GetCurrentUserId());
            
            return CreateResponse<object?>(null, "Invoice deleted successfully.");
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error deleting invoice with ID: {InvoiceId}", id);
            return CreateErrorResponse("An error occurred while deleting the invoice.", 500);
        }
    }

    /// <summary>
    /// Generate next invoice number
    /// توليد رقم الفاتورة التالي
    /// </summary>
    [HttpGet("next-number/{invoiceType}")]
    public async Task<IActionResult> GetNextInvoiceNumber(string invoiceType, CancellationToken cancellationToken = default)
    {
        try
        {
            var companyId = GetCurrentCompanyId();
            var nextNumber = await _invoiceService.GenerateNextInvoiceNumberAsync(companyId, invoiceType, cancellationToken);
            
            return CreateResponse(new { InvoiceNumber = nextNumber }, "Next invoice number generated successfully.");
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error generating next invoice number for type: {InvoiceType}", invoiceType);
            return CreateErrorResponse("An error occurred while generating invoice number.", 500);
        }
    }
}
