using BusinessManagementSystem.Application.DTOs.Warehouse;

namespace BusinessManagementSystem.Application.Services.Interfaces;

/// <summary>
/// Warehouse service interface for business logic operations
/// واجهة خدمة المستودع لعمليات منطق الأعمال
/// </summary>
public interface IWarehouseService
{
    /// <summary>
    /// Get all warehouses
    /// الحصول على جميع المستودعات
    /// </summary>
    Task<IEnumerable<WarehouseDto>> GetAllAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// Get warehouse by ID
    /// الحصول على المستودع بواسطة المعرف
    /// </summary>
    Task<WarehouseDto?> GetByIdAsync(int id, CancellationToken cancellationToken = default);

    /// <summary>
    /// Get warehouses by company
    /// الحصول على المستودعات بواسطة الشركة
    /// </summary>
    Task<IEnumerable<WarehouseDto>> GetByCompanyAsync(int companyId, CancellationToken cancellationToken = default);

    /// <summary>
    /// Get active warehouses by company
    /// الحصول على المستودعات النشطة بواسطة الشركة
    /// </summary>
    Task<IEnumerable<WarehouseDto>> GetActiveByCompanyAsync(int companyId, CancellationToken cancellationToken = default);

    /// <summary>
    /// Search warehouses by name or location
    /// البحث في المستودعات بواسطة الاسم أو الموقع
    /// </summary>
    Task<IEnumerable<WarehouseDto>> SearchAsync(int companyId, string searchTerm, CancellationToken cancellationToken = default);

    /// <summary>
    /// Get warehouses with low capacity
    /// الحصول على المستودعات ذات السعة المنخفضة
    /// </summary>
    Task<IEnumerable<WarehouseDto>> GetLowCapacityWarehousesAsync(int companyId, decimal thresholdPercentage = 90, CancellationToken cancellationToken = default);

    /// <summary>
    /// Create new warehouse
    /// إنشاء مستودع جديد
    /// </summary>
    Task<WarehouseDto> CreateAsync(CreateWarehouseDto createWarehouseDto, CancellationToken cancellationToken = default);

    /// <summary>
    /// Update existing warehouse
    /// تحديث مستودع موجود
    /// </summary>
    Task<WarehouseDto> UpdateAsync(int id, UpdateWarehouseDto updateWarehouseDto, CancellationToken cancellationToken = default);

    /// <summary>
    /// Set warehouse active status
    /// تعيين حالة تفعيل المستودع
    /// </summary>
    Task<bool> SetActiveStatusAsync(int id, bool isActive, CancellationToken cancellationToken = default);

    /// <summary>
    /// Delete warehouse
    /// حذف المستودع
    /// </summary>
    Task<bool> DeleteAsync(int id, CancellationToken cancellationToken = default);

    /// <summary>
    /// Transfer items between warehouses
    /// نقل الأصناف بين المستودعات
    /// </summary>
    Task<bool> TransferItemsAsync(WarehouseTransferDto transferDto, CancellationToken cancellationToken = default);

    /// <summary>
    /// Update warehouse utilization
    /// تحديث استخدام المستودع
    /// </summary>
    Task<bool> UpdateUtilizationAsync(int warehouseId, decimal utilizationChange, CancellationToken cancellationToken = default);

    /// <summary>
    /// Get warehouse capacity report
    /// الحصول على تقرير سعة المستودع
    /// </summary>
    Task<WarehouseCapacityReportDto> GetCapacityReportAsync(int warehouseId, CancellationToken cancellationToken = default);

    /// <summary>
    /// Get warehouse utilization summary
    /// الحصول على ملخص استخدام المستودع
    /// </summary>
    Task<object> GetUtilizationSummaryAsync(int companyId, CancellationToken cancellationToken = default);

    /// <summary>
    /// Check if warehouse has sufficient capacity
    /// التحقق من وجود سعة كافية في المستودع
    /// </summary>
    Task<bool> HasSufficientCapacityAsync(int warehouseId, decimal requiredCapacity, CancellationToken cancellationToken = default);

    /// <summary>
    /// Get optimal warehouse for item allocation
    /// الحصول على المستودع الأمثل لتخصيص الصنف
    /// </summary>
    Task<WarehouseDto?> GetOptimalWarehouseAsync(int companyId, decimal requiredCapacity, CancellationToken cancellationToken = default);

    /// <summary>
    /// Get warehouse statistics
    /// الحصول على إحصائيات المستودع
    /// </summary>
    Task<object> GetWarehouseStatisticsAsync(int warehouseId, CancellationToken cancellationToken = default);

    /// <summary>
    /// Validate warehouse transfer
    /// التحقق من صحة نقل المستودع
    /// </summary>
    Task<bool> ValidateTransferAsync(WarehouseTransferDto transferDto, CancellationToken cancellationToken = default);
}
