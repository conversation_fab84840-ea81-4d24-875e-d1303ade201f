{"ConnectionStrings": {"DefaultConnection": "Server=(localdb)\\mssqllocaldb;Database=BusinessManagementSystemDb;Trusted_Connection=true;MultipleActiveResultSets=true"}, "JwtSettings": {"SecretKey": "BusinessManagementSystemSecretKey2024!@#$%^&*()_+", "Issuer": "BusinessManagementSystem", "Audience": "BusinessManagementSystemUsers", "AccessTokenExpiryMinutes": 60, "RefreshTokenExpiryDays": 7}, "Serilog": {"Using": ["Serilog.Sinks.Console", "Serilog.Sinks.File"], "MinimumLevel": {"Default": "Information", "Override": {"Microsoft": "Warning", "Microsoft.Hosting.Lifetime": "Information", "Microsoft.EntityFrameworkCore": "Warning"}}, "WriteTo": [{"Name": "<PERSON><PERSON><PERSON>", "Args": {"outputTemplate": "[{Timestamp:HH:mm:ss} {Level:u3}] {Message:lj} {Properties:j}{NewLine}{Exception}"}}, {"Name": "File", "Args": {"path": "logs/business-management-.txt", "rollingInterval": "Day", "outputTemplate": "[{Timestamp:yyyy-MM-dd HH:mm:ss.fff zzz} {Level:u3}] {Message:lj} {Properties:j}{NewLine}{Exception}", "retainedFileCountLimit": 30}}], "Enrich": ["FromLogContext", "WithMachineName", "WithThreadId"]}, "Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning"}}, "AllowedHosts": "*"}