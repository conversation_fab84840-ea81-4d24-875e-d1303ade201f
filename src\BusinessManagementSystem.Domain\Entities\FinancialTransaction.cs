using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using BusinessManagementSystem.Domain.Common;

namespace BusinessManagementSystem.Domain.Entities;

/// <summary>
/// Financial transaction entity - tracks all financial movements
/// كيان المعاملة المالية - يتتبع جميع الحركات المالية
/// </summary>
public class FinancialTransaction : BaseEntity
{
    /// <summary>
    /// Transaction date
    /// تاريخ المعاملة
    /// </summary>
    [Required]
    public DateTime TransactionDate { get; set; }

    /// <summary>
    /// Transaction amount
    /// مبلغ المعاملة
    /// </summary>
    [Column(TypeName = "decimal(18,2)")]
    public decimal Amount { get; set; }

    /// <summary>
    /// Transaction type (Income/Expense)
    /// نوع المعاملة (دخل/مصروف)
    /// </summary>
    [Required]
    [MaxLength(20)]
    public string TransactionType { get; set; } = string.Empty;

    /// <summary>
    /// Transaction description
    /// وصف المعاملة
    /// </summary>
    [MaxLength(1000)]
    public string? Description { get; set; }

    /// <summary>
    /// Reference number for tracking
    /// رقم المرجع للتتبع
    /// </summary>
    [MaxLength(50)]
    public string? ReferenceNumber { get; set; }

    /// <summary>
    /// Company identifier (Foreign Key)
    /// معرف الشركة (مفتاح خارجي)
    /// </summary>
    [Required]
    public int CompanyId { get; set; }

    /// <summary>
    /// Main cash identifier (Foreign Key)
    /// معرف الخزينة الرئيسية (مفتاح خارجي)
    /// </summary>
    public int? MainCashId { get; set; }

    /// <summary>
    /// Person identifier (Foreign Key) - for customer/supplier transactions
    /// معرف الشخص (مفتاح خارجي) - لمعاملات العملاء/الموردين
    /// </summary>
    public int? PersonId { get; set; }

    /// <summary>
    /// Invoice identifier (Foreign Key) - for invoice-related transactions
    /// معرف الفاتورة (مفتاح خارجي) - للمعاملات المرتبطة بالفواتير
    /// </summary>
    public int? InvoiceId { get; set; }

    // Navigation properties
    [ForeignKey(nameof(CompanyId))]
    public virtual Company Company { get; set; } = null!;

    [ForeignKey(nameof(MainCashId))]
    public virtual MainCash? MainCash { get; set; }

    [ForeignKey(nameof(PersonId))]
    public virtual Person? Person { get; set; }

    [ForeignKey(nameof(InvoiceId))]
    public virtual Invoice? Invoice { get; set; }
}
