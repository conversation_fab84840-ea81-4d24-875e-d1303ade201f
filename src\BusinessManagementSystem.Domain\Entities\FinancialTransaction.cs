using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using BusinessManagementSystem.Domain.Common;

namespace BusinessManagementSystem.Domain.Entities;

/// <summary>
/// Financial transaction entity - tracks all financial movements
/// كيان المعاملة المالية - يتتبع جميع الحركات المالية
/// </summary>
public class FinancialTransaction : BaseEntity
{
    /// <summary>
    /// Transaction date
    /// تاريخ المعاملة
    /// </summary>
    [Required]
    public DateTime TransactionDate { get; set; }

    /// <summary>
    /// Transaction amount
    /// مبلغ المعاملة
    /// </summary>
    [Column(TypeName = "decimal(18,2)")]
    public decimal Amount { get; set; }

    /// <summary>
    /// Transaction type (Income/Expense)
    /// نوع المعاملة (دخل/مصروف)
    /// </summary>
    [Required]
    [MaxLength(20)]
    public string TransactionType { get; set; } = string.Empty;

    /// <summary>
    /// Transaction description
    /// وصف المعاملة
    /// </summary>
    [MaxLength(1000)]
    public string? Description { get; set; }

    /// <summary>
    /// Reference number for tracking
    /// رقم المرجع للتتبع
    /// </summary>
    [MaxLength(50)]
    public string? ReferenceNumber { get; set; }

    /// <summary>
    /// Company identifier (Foreign Key)
    /// معرف الشركة (مفتاح خارجي)
    /// </summary>
    [Required]
    public int CompanyId { get; set; }

    /// <summary>
    /// Main cash identifier (Foreign Key)
    /// معرف الخزينة الرئيسية (مفتاح خارجي)
    /// </summary>
    [Required]
    public int MainCashId { get; set; }

    // Navigation properties
    [ForeignKey(nameof(CompanyId))]
    public virtual Company Company { get; set; } = null!;

    [ForeignKey(nameof(MainCashId))]
    public virtual MainCash MainCash { get; set; } = null!;
}
