using AutoMapper;
using Microsoft.Extensions.Logging;
using BusinessManagementSystem.Application.DTOs.RolePermission;
using BusinessManagementSystem.Application.Interfaces;
using BusinessManagementSystem.Application.Services.Interfaces;
using BusinessManagementSystem.Domain.Entities;

namespace BusinessManagementSystem.Application.Services.Implementations;

/// <summary>
/// Role Permission service implementation for business logic operations
/// تنفيذ خدمة صلاحية الدور لعمليات منطق الأعمال
/// </summary>
public class RolePermissionService : IRolePermissionService
{
    private readonly IUnitOfWork _unitOfWork;
    private readonly IMapper _mapper;
    private readonly ILogger<RolePermissionService> _logger;

    // System pages configuration
    private readonly Dictionary<string, SystemPageDto> _systemPages = new()
    {
        { "Companies", new SystemPageDto { PageName = "Companies", DisplayName = "Companies", DisplayNameArabic = "الشركات", Category = "Administration", SortOrder = 1 } },
        { "Users", new SystemPageDto { PageName = "Users", DisplayName = "Users", DisplayNameArabic = "المستخدمون", Category = "Administration", SortOrder = 2 } },
        { "Roles", new SystemPageDto { PageName = "Roles", DisplayName = "Roles", DisplayNameArabic = "الأدوار", Category = "Administration", SortOrder = 3 } },
        { "RolePermissions", new SystemPageDto { PageName = "RolePermissions", DisplayName = "Role Permissions", DisplayNameArabic = "صلاحيات الأدوار", Category = "Administration", SortOrder = 4 } },
        { "Departments", new SystemPageDto { PageName = "Departments", DisplayName = "Departments", DisplayNameArabic = "الأقسام", Category = "Organization", SortOrder = 5 } },
        { "Partners", new SystemPageDto { PageName = "Partners", DisplayName = "Partners", DisplayNameArabic = "الشركاء", Category = "Organization", SortOrder = 6 } },
        { "Persons", new SystemPageDto { PageName = "Persons", DisplayName = "Customers & Suppliers", DisplayNameArabic = "العملاء والموردون", Category = "Business", SortOrder = 7 } },
        { "Items", new SystemPageDto { PageName = "Items", DisplayName = "Items & Products", DisplayNameArabic = "الأصناف والمنتجات", Category = "Inventory", SortOrder = 8 } },
        { "Warehouses", new SystemPageDto { PageName = "Warehouses", DisplayName = "Warehouses", DisplayNameArabic = "المستودعات", Category = "Inventory", SortOrder = 9 } },
        { "InventoryTransactions", new SystemPageDto { PageName = "InventoryTransactions", DisplayName = "Inventory Transactions", DisplayNameArabic = "معاملات المخزون", Category = "Inventory", SortOrder = 10 } },
        { "Invoices", new SystemPageDto { PageName = "Invoices", DisplayName = "Invoices", DisplayNameArabic = "الفواتير", Category = "Sales", SortOrder = 11 } },
        { "InvoiceDetails", new SystemPageDto { PageName = "InvoiceDetails", DisplayName = "Invoice Details", DisplayNameArabic = "تفاصيل الفواتير", Category = "Sales", SortOrder = 12 } },
        { "FinancialTransactions", new SystemPageDto { PageName = "FinancialTransactions", DisplayName = "Financial Transactions", DisplayNameArabic = "المعاملات المالية", Category = "Finance", SortOrder = 13 } },
        { "MainCashes", new SystemPageDto { PageName = "MainCashes", DisplayName = "Cash Management", DisplayNameArabic = "إدارة النقدية", Category = "Finance", SortOrder = 14 } }
    };

    public RolePermissionService(IUnitOfWork unitOfWork, IMapper mapper, ILogger<RolePermissionService> logger)
    {
        _unitOfWork = unitOfWork;
        _mapper = mapper;
        _logger = logger;
    }

    public async Task<IEnumerable<RolePermissionDto>> GetAllAsync(CancellationToken cancellationToken = default)
    {
        var permissions = await _unitOfWork.RolePermissions.GetAsync(
            includeProperties: "Role",
            orderBy: q => q.OrderBy(p => p.Role.Name).ThenBy(p => p.PageName),
            cancellationToken: cancellationToken);
        return _mapper.Map<IEnumerable<RolePermissionDto>>(permissions);
    }

    public async Task<RolePermissionDto?> GetByIdAsync(int id, CancellationToken cancellationToken = default)
    {
        var permission = await _unitOfWork.RolePermissions.GetByIdAsync(id, cancellationToken);
        return permission != null ? _mapper.Map<RolePermissionDto>(permission) : null;
    }

    public async Task<IEnumerable<RolePermissionDto>> GetByRoleIdAsync(int roleId, CancellationToken cancellationToken = default)
    {
        var permissions = await _unitOfWork.RolePermissions.GetAsync(
            filter: p => p.RoleId == roleId,
            includeProperties: "Role",
            orderBy: q => q.OrderBy(p => p.PageName),
            cancellationToken: cancellationToken);
        return _mapper.Map<IEnumerable<RolePermissionDto>>(permissions);
    }

    public async Task<IEnumerable<RolePermissionDto>> GetByPageNameAsync(string pageName, CancellationToken cancellationToken = default)
    {
        var permissions = await _unitOfWork.RolePermissions.GetAsync(
            filter: p => p.PageName == pageName,
            includeProperties: "Role",
            orderBy: q => q.OrderBy(p => p.Role.Name),
            cancellationToken: cancellationToken);
        return _mapper.Map<IEnumerable<RolePermissionDto>>(permissions);
    }

    public async Task<RolePermissionDto?> GetByRoleAndPageAsync(int roleId, string pageName, CancellationToken cancellationToken = default)
    {
        var permissions = await _unitOfWork.RolePermissions.GetAsync(
            filter: p => p.RoleId == roleId && p.PageName == pageName,
            includeProperties: "Role",
            cancellationToken: cancellationToken);
        var permission = permissions.FirstOrDefault();
        return permission != null ? _mapper.Map<RolePermissionDto>(permission) : null;
    }

    public async Task<RolePermissionDto> CreateAsync(CreateRolePermissionDto createRolePermissionDto, CancellationToken cancellationToken = default)
    {
        // Validate permission request
        await ValidatePermissionRequestAsync(createRolePermissionDto, cancellationToken);

        // Check if permission already exists
        var existingPermission = await GetByRoleAndPageAsync(createRolePermissionDto.RoleId, createRolePermissionDto.PageName, cancellationToken);
        if (existingPermission != null)
        {
            throw new ArgumentException("Permission already exists for this role and page.");
        }

        var permission = _mapper.Map<RolePermission>(createRolePermissionDto);
        await _unitOfWork.RolePermissions.AddAsync(permission, cancellationToken);
        await _unitOfWork.SaveChangesAsync(cancellationToken);

        _logger.LogInformation("Role permission created successfully with ID: {PermissionId}", permission.Id);
        return _mapper.Map<RolePermissionDto>(permission);
    }

    public async Task<RolePermissionDto> UpdateAsync(int id, UpdateRolePermissionDto updateRolePermissionDto, CancellationToken cancellationToken = default)
    {
        var existingPermission = await _unitOfWork.RolePermissions.GetByIdAsync(id, cancellationToken);
        if (existingPermission == null)
        {
            throw new ArgumentException($"Role permission with ID {id} not found.", nameof(id));
        }

        _mapper.Map(updateRolePermissionDto, existingPermission);
        _unitOfWork.RolePermissions.Update(existingPermission);
        await _unitOfWork.SaveChangesAsync(cancellationToken);

        _logger.LogInformation("Role permission updated successfully with ID: {PermissionId}", id);
        return _mapper.Map<RolePermissionDto>(existingPermission);
    }

    public async Task<bool> DeleteAsync(int id, CancellationToken cancellationToken = default)
    {
        var permission = await _unitOfWork.RolePermissions.GetByIdAsync(id, cancellationToken);
        if (permission == null)
        {
            return false;
        }

        _unitOfWork.RolePermissions.Delete(permission);
        await _unitOfWork.SaveChangesAsync(cancellationToken);

        _logger.LogInformation("Role permission deleted successfully with ID: {PermissionId}", id);
        return true;
    }

    public async Task<bool> DeleteByRoleIdAsync(int roleId, CancellationToken cancellationToken = default)
    {
        var permissions = await _unitOfWork.RolePermissions.GetAsync(
            filter: p => p.RoleId == roleId, cancellationToken: cancellationToken);

        if (!permissions.Any())
        {
            return false;
        }

        foreach (var permission in permissions)
        {
            _unitOfWork.RolePermissions.Delete(permission);
        }

        await _unitOfWork.SaveChangesAsync(cancellationToken);

        _logger.LogInformation("All permissions deleted for role ID: {RoleId}", roleId);
        return true;
    }

    public async Task<bool> DeleteByPageNameAsync(string pageName, CancellationToken cancellationToken = default)
    {
        var permissions = await _unitOfWork.RolePermissions.GetAsync(
            filter: p => p.PageName == pageName, cancellationToken: cancellationToken);

        if (!permissions.Any())
        {
            return false;
        }

        foreach (var permission in permissions)
        {
            _unitOfWork.RolePermissions.Delete(permission);
        }

        await _unitOfWork.SaveChangesAsync(cancellationToken);

        _logger.LogInformation("All permissions deleted for page: {PageName}", pageName);
        return true;
    }

    public async Task<PermissionMatrixDto> GetPermissionMatrixAsync(int roleId, CancellationToken cancellationToken = default)
    {
        var role = await _unitOfWork.Roles.GetByIdAsync(roleId, cancellationToken);
        if (role == null)
        {
            throw new ArgumentException($"Role with ID {roleId} not found.");
        }

        var permissions = await GetByRoleIdAsync(roleId, cancellationToken);
        var permissionDict = permissions.ToDictionary(p => p.PageName, p => p);

        var pagePermissions = _systemPages.Values.Select(page => new PagePermissionDto
        {
            PageName = page.PageName,
            PageDisplayName = page.DisplayName,
            PageCategory = page.Category,
            CanAdd = permissionDict.ContainsKey(page.PageName) && permissionDict[page.PageName].CanAdd,
            CanEdit = permissionDict.ContainsKey(page.PageName) && permissionDict[page.PageName].CanEdit,
            CanDelete = permissionDict.ContainsKey(page.PageName) && permissionDict[page.PageName].CanDelete,
            CanView = permissionDict.ContainsKey(page.PageName) && permissionDict[page.PageName].CanView
        }).OrderBy(p => p.PageCategory).ThenBy(p => p.PageDisplayName).ToList();

        var totalPermissions = _systemPages.Count * 4; // 4 actions per page
        var grantedPermissions = pagePermissions.Sum(p => 
            (p.CanAdd ? 1 : 0) + (p.CanEdit ? 1 : 0) + (p.CanDelete ? 1 : 0) + (p.CanView ? 1 : 0));

        return new PermissionMatrixDto
        {
            Role = new RoleInfoDto
            {
                Id = role.Id,
                Name = role.Name,
                Description = role.Description,
                IsSystemRole = role.IsSystemRole,
                IsActive = role.IsActive
            },
            PagePermissions = pagePermissions,
            TotalPermissions = totalPermissions,
            GrantedPermissions = grantedPermissions
        };
    }

    public async Task<bool> BulkUpdatePermissionsAsync(BulkPermissionUpdateDto bulkUpdateDto, CancellationToken cancellationToken = default)
    {
        var role = await _unitOfWork.Roles.GetByIdAsync(bulkUpdateDto.RoleId, cancellationToken);
        if (role == null)
        {
            throw new ArgumentException("Role not found.");
        }

        await _unitOfWork.BeginTransactionAsync(cancellationToken);
        try
        {
            if (bulkUpdateDto.ReplaceExisting)
            {
                // Delete existing permissions
                await DeleteByRoleIdAsync(bulkUpdateDto.RoleId, cancellationToken);
            }

            // Add new permissions
            foreach (var permissionUpdate in bulkUpdateDto.Permissions)
            {
                var existingPermission = await GetByRoleAndPageAsync(bulkUpdateDto.RoleId, permissionUpdate.PageName, cancellationToken);
                
                if (existingPermission != null)
                {
                    // Update existing permission
                    var updateDto = new UpdateRolePermissionDto
                    {
                        CanAdd = permissionUpdate.CanAdd,
                        CanEdit = permissionUpdate.CanEdit,
                        CanDelete = permissionUpdate.CanDelete,
                        CanView = permissionUpdate.CanView
                    };
                    await UpdateAsync(existingPermission.Id, updateDto, cancellationToken);
                }
                else
                {
                    // Create new permission
                    var createDto = new CreateRolePermissionDto
                    {
                        RoleId = bulkUpdateDto.RoleId,
                        PageName = permissionUpdate.PageName,
                        CanAdd = permissionUpdate.CanAdd,
                        CanEdit = permissionUpdate.CanEdit,
                        CanDelete = permissionUpdate.CanDelete,
                        CanView = permissionUpdate.CanView
                    };
                    await CreateAsync(createDto, cancellationToken);
                }
            }

            await _unitOfWork.CommitTransactionAsync(cancellationToken);

            _logger.LogInformation("Bulk permissions updated for role {RoleId}", bulkUpdateDto.RoleId);
            return true;
        }
        catch
        {
            await _unitOfWork.RollbackTransactionAsync(cancellationToken);
            throw;
        }
    }

    public async Task<bool> CopyPermissionsAsync(int sourceRoleId, int targetRoleId, bool replaceExisting = true, CancellationToken cancellationToken = default)
    {
        var sourceRole = await _unitOfWork.Roles.GetByIdAsync(sourceRoleId, cancellationToken);
        var targetRole = await _unitOfWork.Roles.GetByIdAsync(targetRoleId, cancellationToken);

        if (sourceRole == null || targetRole == null)
        {
            throw new ArgumentException("Source or target role not found.");
        }

        var sourcePermissions = await GetByRoleIdAsync(sourceRoleId, cancellationToken);

        var bulkUpdateDto = new BulkPermissionUpdateDto
        {
            RoleId = targetRoleId,
            ReplaceExisting = replaceExisting,
            Permissions = sourcePermissions.Select(p => new PagePermissionUpdateDto
            {
                PageName = p.PageName,
                CanAdd = p.CanAdd,
                CanEdit = p.CanEdit,
                CanDelete = p.CanDelete,
                CanView = p.CanView
            }).ToList()
        };

        return await BulkUpdatePermissionsAsync(bulkUpdateDto, cancellationToken);
    }

    public async Task<bool> HasPermissionAsync(int userId, string pageName, string action, CancellationToken cancellationToken = default)
    {
        var user = await _unitOfWork.Users.GetByIdAsync(userId, cancellationToken);
        if (user == null || !user.IsActive || !user.RoleId.HasValue)
        {
            return false;
        }

        var permission = await GetByRoleAndPageAsync(user.RoleId.Value, pageName, cancellationToken);
        if (permission == null)
        {
            return false;
        }

        return action.ToLower() switch
        {
            "add" or "create" => permission.CanAdd,
            "edit" or "update" => permission.CanEdit,
            "delete" => permission.CanDelete,
            "view" or "read" => permission.CanView,
            _ => false
        };
    }

    public async Task<object> GetUserPermissionsAsync(int userId, CancellationToken cancellationToken = default)
    {
        var user = await _unitOfWork.Users.GetByIdAsync(userId, cancellationToken);
        if (user == null || !user.RoleId.HasValue)
        {
            return new { UserId = userId, RoleId = (int?)null, Permissions = new List<object>() };
        }

        var permissions = await GetByRoleIdAsync(user.RoleId.Value, cancellationToken);
        
        return new
        {
            UserId = userId,
            RoleId = user.RoleId,
            RoleName = user.Role?.Name,
            Permissions = permissions.Select(p => new
            {
                p.PageName,
                p.CanAdd,
                p.CanEdit,
                p.CanDelete,
                p.CanView,
                p.PermissionSummary
            })
        };
    }

    public async Task<AvailablePagesDto> GetAvailablePagesAsync(CancellationToken cancellationToken = default)
    {
        var pages = _systemPages.Values.OrderBy(p => p.SortOrder).ToList();
        var pagesByCategory = pages.GroupBy(p => p.Category)
            .ToDictionary(g => g.Key, g => g.ToList());

        return new AvailablePagesDto
        {
            Pages = pages,
            PagesByCategory = pagesByCategory
        };
    }

    public async Task<bool> ValidatePermissionRequestAsync(CreateRolePermissionDto permissionDto, CancellationToken cancellationToken = default)
    {
        // Validate role exists
        var role = await _unitOfWork.Roles.GetByIdAsync(permissionDto.RoleId, cancellationToken);
        if (role == null)
        {
            throw new ArgumentException("Role not found.");
        }

        // Validate page name exists in system
        if (!_systemPages.ContainsKey(permissionDto.PageName))
        {
            throw new ArgumentException($"Page '{permissionDto.PageName}' is not a valid system page.");
        }

        // At least one permission must be granted
        if (!permissionDto.CanAdd && !permissionDto.CanEdit && !permissionDto.CanDelete && !permissionDto.CanView)
        {
            throw new ArgumentException("At least one permission (Add, Edit, Delete, or View) must be granted.");
        }

        return true;
    }

    public async Task<IEnumerable<PermissionTemplateDto>> GetPermissionTemplatesAsync(CancellationToken cancellationToken = default)
    {
        var templates = new List<PermissionTemplateDto>
        {
            new PermissionTemplateDto
            {
                TemplateName = "Administrator",
                Description = "Full access to all system features",
                Permissions = _systemPages.Values.Select(page => new PagePermissionDto
                {
                    PageName = page.PageName,
                    PageDisplayName = page.DisplayName,
                    PageCategory = page.Category,
                    CanAdd = true,
                    CanEdit = true,
                    CanDelete = true,
                    CanView = true
                }).ToList()
            },
            new PermissionTemplateDto
            {
                TemplateName = "Manager",
                Description = "Management level access with limited administrative functions",
                Permissions = _systemPages.Values.Where(p => p.Category != "Administration").Select(page => new PagePermissionDto
                {
                    PageName = page.PageName,
                    PageDisplayName = page.DisplayName,
                    PageCategory = page.Category,
                    CanAdd = true,
                    CanEdit = true,
                    CanDelete = page.Category != "Finance", // No delete access to finance
                    CanView = true
                }).ToList()
            },
            new PermissionTemplateDto
            {
                TemplateName = "Employee",
                Description = "Basic employee access with view and limited edit permissions",
                Permissions = _systemPages.Values.Where(p => p.Category == "Business" || p.Category == "Inventory" || p.Category == "Sales").Select(page => new PagePermissionDto
                {
                    PageName = page.PageName,
                    PageDisplayName = page.DisplayName,
                    PageCategory = page.Category,
                    CanAdd = page.Category == "Inventory" || page.Category == "Sales",
                    CanEdit = page.Category == "Inventory" || page.Category == "Sales",
                    CanDelete = false,
                    CanView = true
                }).ToList()
            },
            new PermissionTemplateDto
            {
                TemplateName = "ReadOnly",
                Description = "View-only access to all non-administrative features",
                Permissions = _systemPages.Values.Where(p => p.Category != "Administration").Select(page => new PagePermissionDto
                {
                    PageName = page.PageName,
                    PageDisplayName = page.DisplayName,
                    PageCategory = page.Category,
                    CanAdd = false,
                    CanEdit = false,
                    CanDelete = false,
                    CanView = true
                }).ToList()
            }
        };

        return templates;
    }

    public async Task<bool> ApplyPermissionTemplateAsync(int roleId, string templateName, CancellationToken cancellationToken = default)
    {
        var templates = await GetPermissionTemplatesAsync(cancellationToken);
        var template = templates.FirstOrDefault(t => t.TemplateName == templateName);

        if (template == null)
        {
            throw new ArgumentException($"Permission template '{templateName}' not found.");
        }

        var bulkUpdateDto = new BulkPermissionUpdateDto
        {
            RoleId = roleId,
            ReplaceExisting = true,
            Permissions = template.Permissions.Select(p => new PagePermissionUpdateDto
            {
                PageName = p.PageName,
                CanAdd = p.CanAdd,
                CanEdit = p.CanEdit,
                CanDelete = p.CanDelete,
                CanView = p.CanView
            }).ToList()
        };

        return await BulkUpdatePermissionsAsync(bulkUpdateDto, cancellationToken);
    }

    public async Task<object> ValidatePermissionConsistencyAsync(int roleId, CancellationToken cancellationToken = default)
    {
        var permissions = await GetByRoleIdAsync(roleId, cancellationToken);
        var issues = new List<string>();

        foreach (var permission in permissions)
        {
            // Check if page exists in system
            if (!_systemPages.ContainsKey(permission.PageName))
            {
                issues.Add($"Permission exists for unknown page: {permission.PageName}");
            }

            // Check logical consistency
            if (permission.CanDelete && !permission.CanView)
            {
                issues.Add($"Page {permission.PageName}: Cannot delete without view permission");
            }

            if (permission.CanEdit && !permission.CanView)
            {
                issues.Add($"Page {permission.PageName}: Cannot edit without view permission");
            }

            if (!permission.CanAdd && !permission.CanEdit && !permission.CanDelete && !permission.CanView)
            {
                issues.Add($"Page {permission.PageName}: No permissions granted");
            }
        }

        return new
        {
            RoleId = roleId,
            IsValid = !issues.Any(),
            Issues = issues,
            TotalPermissions = permissions.Count(),
            ValidatedAt = DateTime.UtcNow
        };
    }

    public async Task<object> GetPermissionUsageStatisticsAsync(CancellationToken cancellationToken = default)
    {
        var allPermissions = await GetAllAsync(cancellationToken);
        var roles = await _unitOfWork.Roles.GetAsync(cancellationToken: cancellationToken);

        var permissionsByPage = allPermissions.GroupBy(p => p.PageName)
            .Select(g => new
            {
                PageName = g.Key,
                RolesWithPermission = g.Count(),
                TotalAddPermissions = g.Count(p => p.CanAdd),
                TotalEditPermissions = g.Count(p => p.CanEdit),
                TotalDeletePermissions = g.Count(p => p.CanDelete),
                TotalViewPermissions = g.Count(p => p.CanView)
            }).ToList();

        var permissionsByRole = allPermissions.GroupBy(p => new { p.RoleId, p.RoleName })
            .Select(g => new
            {
                RoleId = g.Key.RoleId,
                RoleName = g.Key.RoleName,
                TotalPermissions = g.Count(),
                PagesWithPermission = g.Select(p => p.PageName).Distinct().Count(),
                TotalAddPermissions = g.Count(p => p.CanAdd),
                TotalEditPermissions = g.Count(p => p.CanEdit),
                TotalDeletePermissions = g.Count(p => p.CanDelete),
                TotalViewPermissions = g.Count(p => p.CanView)
            }).ToList();

        return new
        {
            TotalRoles = roles.Count(),
            TotalPermissions = allPermissions.Count(),
            TotalPages = _systemPages.Count,
            PagesWithoutPermissions = _systemPages.Keys.Except(allPermissions.Select(p => p.PageName)).ToList(),
            RolesWithoutPermissions = roles.Where(r => !allPermissions.Any(p => p.RoleId == r.Id)).Select(r => new { r.Id, r.Name }).ToList(),
            PermissionsByPage = permissionsByPage,
            PermissionsByRole = permissionsByRole,
            MostPermissiveRoles = permissionsByRole.OrderByDescending(r => r.TotalPermissions).Take(5).ToList(),
            LeastPermissiveRoles = permissionsByRole.OrderBy(r => r.TotalPermissions).Take(5).ToList()
        };
    }

    public async Task<byte[]> ExportPermissionsAsync(int? roleId = null, string format = "csv", CancellationToken cancellationToken = default)
    {
        var permissions = roleId.HasValue
            ? await GetByRoleIdAsync(roleId.Value, cancellationToken)
            : await GetAllAsync(cancellationToken);

        // Simplified CSV export
        var csv = "RoleId,RoleName,PageName,CanAdd,CanEdit,CanDelete,CanView\n";
        foreach (var permission in permissions)
        {
            csv += $"{permission.RoleId},{permission.RoleName},{permission.PageName},{permission.CanAdd},{permission.CanEdit},{permission.CanDelete},{permission.CanView}\n";
        }

        return System.Text.Encoding.UTF8.GetBytes(csv);
    }

    public async Task<object> ImportPermissionsAsync(byte[] fileData, string fileName, bool replaceExisting = false, CancellationToken cancellationToken = default)
    {
        // This is a simplified implementation
        // In a real scenario, you'd parse CSV/Excel files properly
        var content = System.Text.Encoding.UTF8.GetString(fileData);
        var lines = content.Split('\n', StringSplitOptions.RemoveEmptyEntries);

        var imported = 0;
        var errors = new List<string>();

        for (int i = 1; i < lines.Length; i++) // Skip header
        {
            try
            {
                var parts = lines[i].Split(',');
                if (parts.Length >= 7)
                {
                    var createDto = new CreateRolePermissionDto
                    {
                        RoleId = int.Parse(parts[0]),
                        PageName = parts[2],
                        CanAdd = bool.Parse(parts[3]),
                        CanEdit = bool.Parse(parts[4]),
                        CanDelete = bool.Parse(parts[5]),
                        CanView = bool.Parse(parts[6])
                    };

                    await CreateAsync(createDto, cancellationToken);
                    imported++;
                }
            }
            catch (Exception ex)
            {
                errors.Add($"Line {i + 1}: {ex.Message}");
            }
        }

        return new
        {
            FileName = fileName,
            TotalLines = lines.Length - 1,
            ImportedPermissions = imported,
            Errors = errors,
            ImportedAt = DateTime.UtcNow
        };
    }

    public async Task<IEnumerable<object>> GetPermissionAuditTrailAsync(int? roleId = null, string? pageName = null, CancellationToken cancellationToken = default)
    {
        // This would typically query an audit log table
        // For now, return basic information
        var permissions = await GetAllAsync(cancellationToken);

        if (roleId.HasValue)
        {
            permissions = permissions.Where(p => p.RoleId == roleId.Value);
        }

        if (!string.IsNullOrEmpty(pageName))
        {
            permissions = permissions.Where(p => p.PageName == pageName);
        }

        return permissions.Select(p => new
        {
            PermissionId = p.Id,
            RoleId = p.RoleId,
            RoleName = p.RoleName,
            PageName = p.PageName,
            Action = "Created/Updated",
            Date = p.CreatedAt,
            User = p.CreatedBy,
            Details = $"Permission for {p.RoleName} on {p.PageName}: {p.PermissionSummary}"
        });
    }

    public async Task<bool> SetDefaultPermissionsAsync(int roleId, string roleType = "Employee", CancellationToken cancellationToken = default)
    {
        var templateName = roleType switch
        {
            "Administrator" => "Administrator",
            "Manager" => "Manager",
            "Employee" => "Employee",
            "ReadOnly" => "ReadOnly",
            _ => "Employee"
        };

        return await ApplyPermissionTemplateAsync(roleId, templateName, cancellationToken);
    }

    public async Task<IEnumerable<object>> GetRolesWithPermissionAsync(string pageName, string action, CancellationToken cancellationToken = default)
    {
        var permissions = await GetByPageNameAsync(pageName, cancellationToken);

        var filteredPermissions = permissions.Where(p => action.ToLower() switch
        {
            "add" or "create" => p.CanAdd,
            "edit" or "update" => p.CanEdit,
            "delete" => p.CanDelete,
            "view" or "read" => p.CanView,
            _ => false
        });

        return filteredPermissions.Select(p => new
        {
            RoleId = p.RoleId,
            RoleName = p.RoleName,
            PageName = p.PageName,
            Action = action,
            HasPermission = true
        });
    }
}
