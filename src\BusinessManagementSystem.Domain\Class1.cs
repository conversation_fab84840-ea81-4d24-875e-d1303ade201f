﻿using System.ComponentModel.DataAnnotations;

namespace BusinessManagementSystem.Domain.Common;

/// <summary>
/// Base entity class with common properties
/// فئة الكيان الأساسية مع الخصائص المشتركة
/// </summary>
public abstract class BaseEntity
{
    /// <summary>
    /// Primary key identifier
    /// المعرف الأساسي
    /// </summary>
    [Key]
    public int Id { get; set; }

    /// <summary>
    /// Creation date and time
    /// تاريخ ووقت الإنشاء
    /// </summary>
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// Last update date and time
    /// تاريخ ووقت آخر تحديث
    /// </summary>
    public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;
}
