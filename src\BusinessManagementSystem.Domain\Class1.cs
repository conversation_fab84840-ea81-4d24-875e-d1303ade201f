﻿using System.ComponentModel.DataAnnotations;

namespace BusinessManagementSystem.Domain.Common;

/// <summary>
/// Base entity class with common properties including audit trail
/// فئة الكيان الأساسية مع الخصائص المشتركة بما في ذلك مسار التدقيق
/// </summary>
public abstract class BaseEntity
{
    /// <summary>
    /// Primary key identifier
    /// المعرف الأساسي
    /// </summary>
    [Key]
    public int Id { get; set; }

    /// <summary>
    /// Creation date and time
    /// تاريخ ووقت الإنشاء
    /// </summary>
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// Last update date and time
    /// تاريخ ووقت آخر تحديث
    /// </summary>
    public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// User ID who created this record
    /// معرف المستخدم الذي أنشأ هذا السجل
    /// </summary>
    public int? CreatedBy { get; set; }

    /// <summary>
    /// User ID who last updated this record
    /// معرف المستخدم الذي قام بآخر تحديث لهذا السجل
    /// </summary>
    public int? UpdatedBy { get; set; }
}
