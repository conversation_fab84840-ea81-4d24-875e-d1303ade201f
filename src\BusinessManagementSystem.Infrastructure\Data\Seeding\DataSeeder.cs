using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using BusinessManagementSystem.Domain.Entities;

namespace BusinessManagementSystem.Infrastructure.Data.Seeding;

/// <summary>
/// Data seeder for initial system data
/// بذر البيانات للبيانات الأولية للنظام
/// </summary>
public class DataSeeder
{
    private readonly ApplicationDbContext _context;
    private readonly ILogger<DataSeeder> _logger;

    public DataSeeder(ApplicationDbContext context, ILogger<DataSeeder> logger)
    {
        _context = context;
        _logger = logger;
    }

    /// <summary>
    /// Seed all initial data
    /// بذر جميع البيانات الأولية
    /// </summary>
    public async Task SeedAllAsync()
    {
        try
        {
            await SeedRolesAsync();
            await SeedCompaniesAsync();
            await SeedUsersAsync();
            await SeedRolePermissionsAsync();
            await SeedDepartmentsAsync();
            await Seed<PERSON>arehousesAsync();
            await SeedPersonsAsync();
            await SeedItemsAsync();
            await SeedMainCashesAsync();

            await _context.SaveChangesAsync();
            _logger.LogInformation("Data seeding completed successfully");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error occurred during data seeding");
            throw;
        }
    }

    /// <summary>
    /// Seed system roles
    /// بذر أدوار النظام
    /// </summary>
    private async Task SeedRolesAsync()
    {
        if (await _context.Roles.AnyAsync())
            return;

        var roles = new List<Role>
        {
            new Role
            {
                Name = "Administrator",
                Description = "System Administrator with full access",
                IsSystemRole = true,
                IsActive = true,
                CreatedAt = DateTime.UtcNow,
                CreatedBy = "System"
            },
            new Role
            {
                Name = "Manager",
                Description = "Manager with business operations access",
                IsSystemRole = false,
                IsActive = true,
                CreatedAt = DateTime.UtcNow,
                CreatedBy = "System"
            },
            new Role
            {
                Name = "Employee",
                Description = "Employee with limited access",
                IsSystemRole = false,
                IsActive = true,
                CreatedAt = DateTime.UtcNow,
                CreatedBy = "System"
            },
            new Role
            {
                Name = "ReadOnly",
                Description = "Read-only access to system data",
                IsSystemRole = false,
                IsActive = true,
                CreatedAt = DateTime.UtcNow,
                CreatedBy = "System"
            }
        };

        await _context.Roles.AddRangeAsync(roles);
        _logger.LogInformation("Seeded {Count} roles", roles.Count);
    }

    /// <summary>
    /// Seed demo companies
    /// بذر الشركات التجريبية
    /// </summary>
    private async Task SeedCompaniesAsync()
    {
        if (await _context.Companies.AnyAsync())
            return;

        var companies = new List<Company>
        {
            new Company
            {
                Name = "Demo Company Ltd",
                TradeName = "Demo Trade",
                TaxNumber = "*********",
                Address = "123 Business Street, Business City",
                Phone = "******-0123",
                Email = "<EMAIL>",
                Website = "www.democompany.com",
                IsActive = true,
                CreatedAt = DateTime.UtcNow,
                CreatedBy = "System"
            },
            new Company
            {
                Name = "Sample Corp",
                TradeName = "Sample",
                TaxNumber = "*********",
                Address = "456 Corporate Ave, Corporate City",
                Phone = "******-0456",
                Email = "<EMAIL>",
                Website = "www.samplecorp.com",
                IsActive = true,
                CreatedAt = DateTime.UtcNow,
                CreatedBy = "System"
            }
        };

        await _context.Companies.AddRangeAsync(companies);
        _logger.LogInformation("Seeded {Count} companies", companies.Count);
    }

    /// <summary>
    /// Seed demo users
    /// بذر المستخدمين التجريبيين
    /// </summary>
    private async Task SeedUsersAsync()
    {
        if (await _context.Users.AnyAsync())
            return;

        var users = new List<User>
        {
            new User
            {
                UserName = "admin",
                FullName = "System Administrator",
                Email = "<EMAIL>",
                PasswordHash = BCrypt.Net.BCrypt.HashPassword("Admin123!"),
                CompanyId = 1,
                RoleId = 1,
                IsActive = true,
                CreatedAt = DateTime.UtcNow,
                CreatedBy = "System"
            },
            new User
            {
                UserName = "manager",
                FullName = "Business Manager",
                Email = "<EMAIL>",
                PasswordHash = BCrypt.Net.BCrypt.HashPassword("Manager123!"),
                CompanyId = 1,
                RoleId = 2,
                IsActive = true,
                CreatedAt = DateTime.UtcNow,
                CreatedBy = "System"
            },
            new User
            {
                UserName = "employee",
                FullName = "Demo Employee",
                Email = "<EMAIL>",
                PasswordHash = BCrypt.Net.BCrypt.HashPassword("Employee123!"),
                CompanyId = 1,
                RoleId = 3,
                IsActive = true,
                CreatedAt = DateTime.UtcNow,
                CreatedBy = "System"
            }
        };

        await _context.Users.AddRangeAsync(users);
        _logger.LogInformation("Seeded {Count} users", users.Count);
    }

    /// <summary>
    /// Seed role permissions
    /// بذر صلاحيات الأدوار
    /// </summary>
    private async Task SeedRolePermissionsAsync()
    {
        if (await _context.RolePermissions.AnyAsync())
            return;

        var permissions = new List<RolePermission>();

        // Administrator permissions (full access to all pages)
        var adminPages = new[] { "Companies", "Users", "Roles", "RolePermissions", "Departments", "Partners", 
                                "Persons", "Items", "Warehouses", "InventoryTransactions", "Invoices", 
                                "InvoiceDetails", "FinancialTransactions", "MainCashes" };

        foreach (var page in adminPages)
        {
            permissions.Add(new RolePermission
            {
                RoleId = 1, // Administrator
                PageName = page,
                CanAdd = true,
                CanEdit = true,
                CanDelete = true,
                CanView = true,
                CreatedAt = DateTime.UtcNow,
                CreatedBy = "System"
            });
        }

        // Manager permissions (business operations)
        var managerPages = new[] { "Departments", "Partners", "Persons", "Items", "Warehouses", 
                                  "InventoryTransactions", "Invoices", "InvoiceDetails", "FinancialTransactions" };

        foreach (var page in managerPages)
        {
            permissions.Add(new RolePermission
            {
                RoleId = 2, // Manager
                PageName = page,
                CanAdd = true,
                CanEdit = true,
                CanDelete = page != "FinancialTransactions", // No delete for financial transactions
                CanView = true,
                CreatedAt = DateTime.UtcNow,
                CreatedBy = "System"
            });
        }

        // Employee permissions (limited operations)
        var employeePages = new[] { "Items", "Warehouses", "InventoryTransactions", "Invoices", "InvoiceDetails" };

        foreach (var page in employeePages)
        {
            permissions.Add(new RolePermission
            {
                RoleId = 3, // Employee
                PageName = page,
                CanAdd = page == "Items" || page == "InventoryTransactions" || page == "Invoices",
                CanEdit = page == "Items" || page == "InventoryTransactions",
                CanDelete = false,
                CanView = true,
                CreatedAt = DateTime.UtcNow,
                CreatedBy = "System"
            });
        }

        // ReadOnly permissions (view only)
        var readOnlyPages = new[] { "Departments", "Partners", "Persons", "Items", "Warehouses", 
                                   "InventoryTransactions", "Invoices", "InvoiceDetails", "FinancialTransactions" };

        foreach (var page in readOnlyPages)
        {
            permissions.Add(new RolePermission
            {
                RoleId = 4, // ReadOnly
                PageName = page,
                CanAdd = false,
                CanEdit = false,
                CanDelete = false,
                CanView = true,
                CreatedAt = DateTime.UtcNow,
                CreatedBy = "System"
            });
        }

        await _context.RolePermissions.AddRangeAsync(permissions);
        _logger.LogInformation("Seeded {Count} role permissions", permissions.Count);
    }

    /// <summary>
    /// Seed demo departments
    /// بذر الأقسام التجريبية
    /// </summary>
    private async Task SeedDepartmentsAsync()
    {
        if (await _context.Departments.AnyAsync())
            return;

        var departments = new List<Department>
        {
            new Department
            {
                Name = "Sales",
                Description = "Sales and Marketing Department",
                IsActive = true,
                CompanyId = 1,
                CreatedAt = DateTime.UtcNow,
                CreatedBy = "System"
            },
            new Department
            {
                Name = "Warehouse",
                Description = "Warehouse and Inventory Management",
                IsActive = true,
                CompanyId = 1,
                CreatedAt = DateTime.UtcNow,
                CreatedBy = "System"
            },
            new Department
            {
                Name = "Finance",
                Description = "Finance and Accounting Department",
                IsActive = true,
                CompanyId = 1,
                CreatedAt = DateTime.UtcNow,
                CreatedBy = "System"
            },
            new Department
            {
                Name = "Administration",
                Description = "Administration and HR Department",
                IsActive = true,
                CompanyId = 1,
                CreatedAt = DateTime.UtcNow,
                CreatedBy = "System"
            }
        };

        await _context.Departments.AddRangeAsync(departments);
        _logger.LogInformation("Seeded {Count} departments", departments.Count);
    }

    /// <summary>
    /// Seed demo warehouses
    /// بذر المستودعات التجريبية
    /// </summary>
    private async Task SeedWarehousesAsync()
    {
        if (await _context.Warehouses.AnyAsync())
            return;

        var warehouses = new List<Warehouse>
        {
            new Warehouse
            {
                Name = "Main Warehouse",
                Description = "Primary storage facility",
                Location = "Building A, Floor 1",
                MaxCapacity = 10000,
                CurrentUtilization = 0,
                IsActive = true,
                CompanyId = 1,
                CreatedAt = DateTime.UtcNow,
                CreatedBy = "System"
            },
            new Warehouse
            {
                Name = "Secondary Warehouse",
                Description = "Secondary storage facility",
                Location = "Building B, Floor 2",
                MaxCapacity = 5000,
                CurrentUtilization = 0,
                IsActive = true,
                CompanyId = 1,
                CreatedAt = DateTime.UtcNow,
                CreatedBy = "System"
            }
        };

        await _context.Warehouses.AddRangeAsync(warehouses);
        _logger.LogInformation("Seeded {Count} warehouses", warehouses.Count);
    }

    /// <summary>
    /// Seed demo persons (customers and suppliers)
    /// بذر الأشخاص التجريبيين (العملاء والموردين)
    /// </summary>
    private async Task SeedPersonsAsync()
    {
        if (await _context.Persons.AnyAsync())
            return;

        var persons = new List<Person>
        {
            new Person
            {
                Name = "ABC Customer Corp",
                PersonType = "Customer",
                Email = "<EMAIL>",
                Phone = "******-1001",
                Address = "789 Customer Street, Customer City",
                TaxNumber = "CUS*********",
                IsActive = true,
                CompanyId = 1,
                CreatedAt = DateTime.UtcNow,
                CreatedBy = "System"
            },
            new Person
            {
                Name = "XYZ Supplier Ltd",
                PersonType = "Supplier",
                Email = "<EMAIL>",
                Phone = "******-2001",
                Address = "321 Supplier Avenue, Supplier City",
                TaxNumber = "SUP*********",
                IsActive = true,
                CompanyId = 1,
                CreatedAt = DateTime.UtcNow,
                CreatedBy = "System"
            },
            new Person
            {
                Name = "John Doe",
                PersonType = "Customer",
                Email = "<EMAIL>",
                Phone = "******-3001",
                Address = "456 Individual Street, Personal City",
                IsActive = true,
                CompanyId = 1,
                CreatedAt = DateTime.UtcNow,
                CreatedBy = "System"
            }
        };

        await _context.Persons.AddRangeAsync(persons);
        _logger.LogInformation("Seeded {Count} persons", persons.Count);
    }

    /// <summary>
    /// Seed demo items
    /// بذر الأصناف التجريبية
    /// </summary>
    private async Task SeedItemsAsync()
    {
        if (await _context.Items.AnyAsync())
            return;

        var items = new List<Item>
        {
            new Item
            {
                Name = "Laptop Computer",
                Description = "High-performance business laptop",
                UnitPrice = 1200.00m,
                CostPrice = 900.00m,
                StockQuantity = 25,
                MinimumStockLevel = 5,
                MaximumStockLevel = 50,
                ReorderPoint = 10,
                Category = "Electronics",
                ItemType = "Product",
                Barcode = "*********0123",
                IsActive = true,
                CompanyId = 1,
                CreatedAt = DateTime.UtcNow,
                CreatedBy = "System"
            },
            new Item
            {
                Name = "Office Chair",
                Description = "Ergonomic office chair with lumbar support",
                UnitPrice = 250.00m,
                CostPrice = 180.00m,
                StockQuantity = 15,
                MinimumStockLevel = 3,
                MaximumStockLevel = 30,
                ReorderPoint = 5,
                Category = "Furniture",
                ItemType = "Product",
                Barcode = "2345678901234",
                IsActive = true,
                CompanyId = 1,
                CreatedAt = DateTime.UtcNow,
                CreatedBy = "System"
            },
            new Item
            {
                Name = "Printer Paper",
                Description = "A4 white printer paper, 500 sheets",
                UnitPrice = 8.50m,
                CostPrice = 6.00m,
                StockQuantity = 100,
                MinimumStockLevel = 20,
                MaximumStockLevel = 200,
                ReorderPoint = 30,
                Category = "Office Supplies",
                ItemType = "Product",
                Barcode = "3456789012345",
                IsActive = true,
                CompanyId = 1,
                CreatedAt = DateTime.UtcNow,
                CreatedBy = "System"
            },
            new Item
            {
                Name = "Consulting Service",
                Description = "Business consulting service per hour",
                UnitPrice = 150.00m,
                CostPrice = 100.00m,
                StockQuantity = 0,
                MinimumStockLevel = 0,
                MaximumStockLevel = 0,
                ReorderPoint = 0,
                Category = "Services",
                ItemType = "Service",
                IsActive = true,
                CompanyId = 1,
                CreatedAt = DateTime.UtcNow,
                CreatedBy = "System"
            }
        };

        await _context.Items.AddRangeAsync(items);
        _logger.LogInformation("Seeded {Count} items", items.Count);
    }

    /// <summary>
    /// Seed demo main cash accounts
    /// بذر حسابات النقدية الرئيسية التجريبية
    /// </summary>
    private async Task SeedMainCashesAsync()
    {
        if (await _context.MainCashes.AnyAsync())
            return;

        var mainCashes = new List<MainCash>
        {
            new MainCash
            {
                Balance = 50000.00m,
                Currency = "USD",
                LastUpdated = DateTime.UtcNow,
                CompanyId = 1,
                CreatedAt = DateTime.UtcNow,
                CreatedBy = "System"
            },
            new MainCash
            {
                Balance = 25000.00m,
                Currency = "EUR",
                LastUpdated = DateTime.UtcNow,
                CompanyId = 1,
                CreatedAt = DateTime.UtcNow,
                CreatedBy = "System"
            }
        };

        await _context.MainCashes.AddRangeAsync(mainCashes);
        _logger.LogInformation("Seeded {Count} main cash accounts", mainCashes.Count);
    }
}
