using BusinessManagementSystem.Application.DTOs.Role;

namespace BusinessManagementSystem.Application.Services.Interfaces;

/// <summary>
/// Role service interface for business logic operations
/// واجهة خدمة الدور لعمليات منطق الأعمال
/// </summary>
public interface IRoleService
{
    /// <summary>
    /// Get all roles
    /// الحصول على جميع الأدوار
    /// </summary>
    Task<IEnumerable<RoleDto>> GetAllAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// Get role by ID
    /// الحصول على الدور بواسطة المعرف
    /// </summary>
    Task<RoleDto?> GetByIdAsync(int id, CancellationToken cancellationToken = default);

    /// <summary>
    /// Get role by name
    /// الحصول على الدور بواسطة الاسم
    /// </summary>
    Task<RoleDto?> GetByNameAsync(string name, CancellationToken cancellationToken = default);

    /// <summary>
    /// Get active roles
    /// الحصول على الأدوار النشطة
    /// </summary>
    Task<IEnumerable<RoleDto>> GetActiveRolesAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// Get system roles
    /// الحصول على أدوار النظام
    /// </summary>
    Task<IEnumerable<RoleDto>> GetSystemRolesAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// Get custom roles (non-system)
    /// الحصول على الأدوار المخصصة (غير النظام)
    /// </summary>
    Task<IEnumerable<RoleDto>> GetCustomRolesAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// Search roles by name or description
    /// البحث في الأدوار بواسطة الاسم أو الوصف
    /// </summary>
    Task<IEnumerable<RoleDto>> SearchAsync(string searchTerm, CancellationToken cancellationToken = default);

    /// <summary>
    /// Create new role
    /// إنشاء دور جديد
    /// </summary>
    Task<RoleDto> CreateAsync(CreateRoleDto createRoleDto, CancellationToken cancellationToken = default);

    /// <summary>
    /// Update existing role
    /// تحديث دور موجود
    /// </summary>
    Task<RoleDto> UpdateAsync(int id, UpdateRoleDto updateRoleDto, CancellationToken cancellationToken = default);

    /// <summary>
    /// Set role active status
    /// تعيين حالة تفعيل الدور
    /// </summary>
    Task<bool> SetActiveStatusAsync(int id, bool isActive, CancellationToken cancellationToken = default);

    /// <summary>
    /// Delete role
    /// حذف الدور
    /// </summary>
    Task<bool> DeleteAsync(int id, CancellationToken cancellationToken = default);

    /// <summary>
    /// Check if role name exists
    /// التحقق من وجود اسم الدور
    /// </summary>
    Task<bool> IsRoleNameExistsAsync(string name, int? excludeId = null, CancellationToken cancellationToken = default);

    /// <summary>
    /// Get role statistics
    /// الحصول على إحصائيات الدور
    /// </summary>
    Task<RoleStatisticsDto> GetRoleStatisticsAsync(int id, CancellationToken cancellationToken = default);

    /// <summary>
    /// Get users assigned to role
    /// الحصول على المستخدمين المعينين للدور
    /// </summary>
    Task<IEnumerable<object>> GetRoleUsersAsync(int id, CancellationToken cancellationToken = default);

    /// <summary>
    /// Assign role to user
    /// تعيين دور للمستخدم
    /// </summary>
    Task<bool> AssignRoleToUserAsync(int roleId, int userId, CancellationToken cancellationToken = default);

    /// <summary>
    /// Remove role from user
    /// إزالة دور من المستخدم
    /// </summary>
    Task<bool> RemoveRoleFromUserAsync(int roleId, int userId, CancellationToken cancellationToken = default);

    /// <summary>
    /// Bulk update role permissions
    /// تحديث صلاحيات الدور بالجملة
    /// </summary>
    Task<bool> BulkUpdatePermissionsAsync(BulkRolePermissionUpdateDto bulkUpdateDto, CancellationToken cancellationToken = default);

    /// <summary>
    /// Clone role with permissions
    /// استنساخ الدور مع الصلاحيات
    /// </summary>
    Task<RoleDto> CloneRoleAsync(int sourceRoleId, string newRoleName, string? description = null, CancellationToken cancellationToken = default);

    /// <summary>
    /// Get role permissions summary
    /// الحصول على ملخص صلاحيات الدور
    /// </summary>
    Task<IEnumerable<RolePermissionSummaryDto>> GetRolePermissionsAsync(int id, CancellationToken cancellationToken = default);

    /// <summary>
    /// Validate role for deletion
    /// التحقق من صحة حذف الدور
    /// </summary>
    Task<bool> CanDeleteRoleAsync(int id, CancellationToken cancellationToken = default);

    /// <summary>
    /// Get role hierarchy (if applicable)
    /// الحصول على تسلسل الأدوار (إن أمكن)
    /// </summary>
    Task<object> GetRoleHierarchyAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// Export roles to CSV/Excel format
    /// تصدير الأدوار إلى تنسيق CSV/Excel
    /// </summary>
    Task<byte[]> ExportRolesAsync(string format = "csv", CancellationToken cancellationToken = default);

    /// <summary>
    /// Get role audit trail
    /// الحصول على مسار تدقيق الدور
    /// </summary>
    Task<IEnumerable<object>> GetRoleAuditTrailAsync(int id, CancellationToken cancellationToken = default);
}
