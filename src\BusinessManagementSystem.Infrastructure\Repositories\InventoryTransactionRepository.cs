using Microsoft.EntityFrameworkCore;
using BusinessManagementSystem.Application.Interfaces.Repositories;
using BusinessManagementSystem.Domain.Entities;
using BusinessManagementSystem.Infrastructure.Data;

namespace BusinessManagementSystem.Infrastructure.Repositories;

/// <summary>
/// Inventory transaction repository implementation with specific operations
/// تنفيذ مستودع معاملة المخزون مع العمليات المخصصة
/// </summary>
public class InventoryTransactionRepository : GenericRepository<InventoryTransaction>, IInventoryTransactionRepository
{
    public InventoryTransactionRepository(BusinessManagementDbContext context) : base(context)
    {
    }

    public async Task<IEnumerable<InventoryTransaction>> GetByItemAsync(int itemId, CancellationToken cancellationToken = default)
    {
        return await _dbSet
            .Include(it => it.Item)
            .Include(it => it.Warehouse)
            .Where(it => it.ItemId == itemId)
            .OrderByDescending(it => it.TransactionDate)
            .ToListAsync(cancellationToken);
    }

    public async Task<IEnumerable<InventoryTransaction>> GetByWarehouseAsync(int warehouseId, CancellationToken cancellationToken = default)
    {
        return await _dbSet
            .Include(it => it.Item)
            .Include(it => it.Warehouse)
            .Where(it => it.WarehouseId == warehouseId)
            .OrderByDescending(it => it.TransactionDate)
            .ToListAsync(cancellationToken);
    }

    public async Task<IEnumerable<InventoryTransaction>> GetByDateRangeAsync(int companyId, DateTime startDate, DateTime endDate, CancellationToken cancellationToken = default)
    {
        return await _dbSet
            .Include(it => it.Item)
            .Include(it => it.Warehouse)
            .Where(it => it.CompanyId == companyId && 
                        it.TransactionDate >= startDate && 
                        it.TransactionDate <= endDate)
            .OrderByDescending(it => it.TransactionDate)
            .ToListAsync(cancellationToken);
    }

    public async Task<int> GetCurrentStockLevelAsync(int itemId, int warehouseId, CancellationToken cancellationToken = default)
    {
        var inQuantity = await _dbSet
            .Where(it => it.ItemId == itemId && 
                        it.WarehouseId == warehouseId && 
                        it.TransactionType == "In")
            .SumAsync(it => it.Quantity, cancellationToken);

        var outQuantity = await _dbSet
            .Where(it => it.ItemId == itemId && 
                        it.WarehouseId == warehouseId && 
                        it.TransactionType == "Out")
            .SumAsync(it => it.Quantity, cancellationToken);

        return inQuantity - outQuantity;
    }

    public async Task<Dictionary<int, int>> GetStockLevelsByWarehouseAsync(int warehouseId, CancellationToken cancellationToken = default)
    {
        var transactions = await _dbSet
            .Where(it => it.WarehouseId == warehouseId)
            .GroupBy(it => it.ItemId)
            .Select(g => new
            {
                ItemId = g.Key,
                InQuantity = g.Where(it => it.TransactionType == "In").Sum(it => it.Quantity),
                OutQuantity = g.Where(it => it.TransactionType == "Out").Sum(it => it.Quantity)
            })
            .ToListAsync(cancellationToken);

        return transactions.ToDictionary(
            t => t.ItemId,
            t => t.InQuantity - t.OutQuantity
        );
    }

    public async Task<IEnumerable<InventoryTransaction>> GetItemMovementsAsync(int itemId, DateTime? startDate = null, DateTime? endDate = null, CancellationToken cancellationToken = default)
    {
        var query = _dbSet
            .Include(it => it.Warehouse)
            .Where(it => it.ItemId == itemId);

        if (startDate.HasValue)
        {
            query = query.Where(it => it.TransactionDate >= startDate.Value);
        }

        if (endDate.HasValue)
        {
            query = query.Where(it => it.TransactionDate <= endDate.Value);
        }

        return await query
            .OrderByDescending(it => it.TransactionDate)
            .ToListAsync(cancellationToken);
    }
}
