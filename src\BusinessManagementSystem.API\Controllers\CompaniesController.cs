using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using BusinessManagementSystem.Application.DTOs.Company;
using BusinessManagementSystem.Application.Services.Interfaces;

namespace BusinessManagementSystem.API.Controllers;

/// <summary>
/// Companies controller for company management operations
/// وحدة تحكم الشركات لعمليات إدارة الشركات
/// </summary>
[Authorize]
public class CompaniesController : BaseApiController
{
    private readonly ICompanyService _companyService;
    private readonly ILogger<CompaniesController> _logger;

    public CompaniesController(ICompanyService companyService, ILogger<CompaniesController> logger)
    {
        _companyService = companyService;
        _logger = logger;
    }

    /// <summary>
    /// Get all companies
    /// الحصول على جميع الشركات
    /// </summary>
    [HttpGet]
    public async Task<IActionResult> GetAll(CancellationToken cancellationToken = default)
    {
        try
        {
            if (!HasPermission("Companies", "View"))
            {
                return CreateErrorResponse("You don't have permission to view companies.", 403);
            }

            var companies = await _companyService.GetAllAsync(cancellationToken);
            return CreateResponse(companies, "Companies retrieved successfully.");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving companies");
            return CreateErrorResponse("An error occurred while retrieving companies.", 500);
        }
    }

    /// <summary>
    /// Get company by ID
    /// الحصول على الشركة بواسطة المعرف
    /// </summary>
    [HttpGet("{id}")]
    public async Task<IActionResult> GetById(int id, CancellationToken cancellationToken = default)
    {
        try
        {
            if (!HasPermission("Companies", "View"))
            {
                return CreateErrorResponse("You don't have permission to view companies.", 403);
            }

            var company = await _companyService.GetByIdAsync(id, cancellationToken);
            if (company == null)
            {
                return CreateErrorResponse("Company not found.", 404);
            }

            return CreateResponse(company, "Company retrieved successfully.");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving company with ID: {CompanyId}", id);
            return CreateErrorResponse("An error occurred while retrieving the company.", 500);
        }
    }

    /// <summary>
    /// Create new company
    /// إنشاء شركة جديدة
    /// </summary>
    [HttpPost]
    public async Task<IActionResult> Create([FromBody] CreateCompanyDto createCompanyDto, CancellationToken cancellationToken = default)
    {
        try
        {
            if (!HasPermission("Companies", "Add"))
            {
                return CreateErrorResponse("You don't have permission to create companies.", 403);
            }

            if (!ModelState.IsValid)
            {
                return CreateErrorResponse("Invalid company data.", 400);
            }

            var company = await _companyService.CreateAsync(createCompanyDto, cancellationToken);
            
            _logger.LogInformation("Company created successfully with ID: {CompanyId} by user: {UserId}", 
                company.Id, GetCurrentUserId());
            
            return CreatedAtAction(nameof(GetById), new { id = company.Id }, 
                CreateResponse(company, "Company created successfully."));
        }
        catch (ArgumentException ex)
        {
            _logger.LogWarning(ex, "Validation error creating company");
            return CreateErrorResponse(ex.Message, 400);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating company");
            return CreateErrorResponse("An error occurred while creating the company.", 500);
        }
    }

    /// <summary>
    /// Update existing company
    /// تحديث شركة موجودة
    /// </summary>
    [HttpPut("{id}")]
    public async Task<IActionResult> Update(int id, [FromBody] UpdateCompanyDto updateCompanyDto, CancellationToken cancellationToken = default)
    {
        try
        {
            if (!HasPermission("Companies", "Edit"))
            {
                return CreateErrorResponse("You don't have permission to edit companies.", 403);
            }

            if (!ModelState.IsValid)
            {
                return CreateErrorResponse("Invalid company data.", 400);
            }

            var company = await _companyService.UpdateAsync(id, updateCompanyDto, cancellationToken);
            
            _logger.LogInformation("Company updated successfully with ID: {CompanyId} by user: {UserId}", 
                id, GetCurrentUserId());
            
            return CreateResponse(company, "Company updated successfully.");
        }
        catch (ArgumentException ex)
        {
            _logger.LogWarning(ex, "Validation error updating company with ID: {CompanyId}", id);
            return CreateErrorResponse(ex.Message, 400);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating company with ID: {CompanyId}", id);
            return CreateErrorResponse("An error occurred while updating the company.", 500);
        }
    }

    /// <summary>
    /// Delete company
    /// حذف الشركة
    /// </summary>
    [HttpDelete("{id}")]
    public async Task<IActionResult> Delete(int id, CancellationToken cancellationToken = default)
    {
        try
        {
            if (!HasPermission("Companies", "Delete"))
            {
                return CreateErrorResponse("You don't have permission to delete companies.", 403);
            }

            var result = await _companyService.DeleteAsync(id, cancellationToken);
            if (!result)
            {
                return CreateErrorResponse("Company not found.", 404);
            }

            _logger.LogInformation("Company deleted successfully with ID: {CompanyId} by user: {UserId}", 
                id, GetCurrentUserId());
            
            return CreateResponse<object>(null, "Company deleted successfully.");
        }
        catch (InvalidOperationException ex)
        {
            _logger.LogWarning(ex, "Cannot delete company with ID: {CompanyId}", id);
            return CreateErrorResponse(ex.Message, 400);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deleting company with ID: {CompanyId}", id);
            return CreateErrorResponse("An error occurred while deleting the company.", 500);
        }
    }

    /// <summary>
    /// Check if company name exists
    /// التحقق من وجود اسم الشركة
    /// </summary>
    [HttpGet("check-name/{name}")]
    public async Task<IActionResult> CheckNameExists(string name, [FromQuery] int? excludeId = null, CancellationToken cancellationToken = default)
    {
        try
        {
            var exists = await _companyService.IsNameExistsAsync(name, excludeId, cancellationToken);
            return CreateResponse(new { Exists = exists }, "Name availability checked.");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error checking company name existence: {Name}", name);
            return CreateErrorResponse("An error occurred while checking name availability.", 500);
        }
    }
}
