using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using BusinessManagementSystem.Domain.Common;

namespace BusinessManagementSystem.Domain.Entities;

/// <summary>
/// Role permission entity - stores page-level permissions for each role
/// كيان صلاحيات الدور - يخزن الصلاحيات على مستوى الصفحة لكل دور
/// </summary>
public class RolePermission : BaseEntity
{
    /// <summary>
    /// Page or module name
    /// اسم الصفحة أو الوحدة
    /// </summary>
    [Required]
    [MaxLength(100)]
    public string PageName { get; set; } = string.Empty;

    /// <summary>
    /// Permission to add new records
    /// صلاحية إضافة سجلات جديدة
    /// </summary>
    public bool CanAdd { get; set; }

    /// <summary>
    /// Permission to edit existing records
    /// صلاحية تعديل السجلات الموجودة
    /// </summary>
    public bool CanEdit { get; set; }

    /// <summary>
    /// Permission to delete records
    /// صلاحية حذف السجلات
    /// </summary>
    public bool CanDelete { get; set; }

    /// <summary>
    /// Permission to view records
    /// صلاحية عرض السجلات
    /// </summary>
    public bool CanView { get; set; }

    /// <summary>
    /// Role identifier (Foreign Key)
    /// معرف الدور (مفتاح خارجي)
    /// </summary>
    [Required]
    public int RoleId { get; set; }

    // Navigation properties
    [ForeignKey(nameof(RoleId))]
    public virtual Role Role { get; set; } = null!;
}
