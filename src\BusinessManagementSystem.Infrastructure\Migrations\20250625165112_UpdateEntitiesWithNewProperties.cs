﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace BusinessManagementSystem.Infrastructure.Migrations
{
    /// <inheritdoc />
    public partial class UpdateEntitiesWithNewProperties : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<string>(
                name: "Category",
                table: "Items",
                type: "nvarchar(100)",
                maxLength: 100,
                nullable: true);

            migrationBuilder.AddColumn<decimal>(
                name: "CostPrice",
                table: "Items",
                type: "decimal(18,2)",
                nullable: false,
                defaultValue: 0m);

            migrationBuilder.AddColumn<string>(
                name: "ImageUrl",
                table: "Items",
                type: "nvarchar(500)",
                maxLength: 500,
                nullable: true);

            migrationBuilder.AddColumn<bool>(
                name: "IsActive",
                table: "Items",
                type: "bit",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<decimal>(
                name: "MaximumStockLevel",
                table: "Items",
                type: "decimal(18,2)",
                nullable: false,
                defaultValue: 0m);

            migrationBuilder.AddColumn<decimal>(
                name: "MinimumStockLevel",
                table: "Items",
                type: "decimal(18,2)",
                nullable: false,
                defaultValue: 0m);

            migrationBuilder.AddColumn<decimal>(
                name: "ReorderPoint",
                table: "Items",
                type: "decimal(18,2)",
                nullable: false,
                defaultValue: 0m);

            migrationBuilder.AddColumn<decimal>(
                name: "StockQuantity",
                table: "Items",
                type: "decimal(18,2)",
                nullable: false,
                defaultValue: 0m);

            migrationBuilder.AlterColumn<int>(
                name: "MainCashId",
                table: "FinancialTransactions",
                type: "int",
                nullable: true,
                oldClrType: typeof(int),
                oldType: "int");

            migrationBuilder.AddColumn<int>(
                name: "InvoiceId",
                table: "FinancialTransactions",
                type: "int",
                nullable: true);

            migrationBuilder.AddColumn<int>(
                name: "PersonId",
                table: "FinancialTransactions",
                type: "int",
                nullable: true);

            migrationBuilder.UpdateData(
                table: "Roles",
                keyColumn: "Id",
                keyValue: 1,
                columns: new[] { "CreatedAt", "UpdatedAt" },
                values: new object[] { new DateTime(2025, 6, 25, 16, 51, 7, 575, DateTimeKind.Utc).AddTicks(5355), new DateTime(2025, 6, 25, 16, 51, 7, 575, DateTimeKind.Utc).AddTicks(5744) });

            migrationBuilder.UpdateData(
                table: "Roles",
                keyColumn: "Id",
                keyValue: 2,
                columns: new[] { "CreatedAt", "UpdatedAt" },
                values: new object[] { new DateTime(2025, 6, 25, 16, 51, 7, 575, DateTimeKind.Utc).AddTicks(6118), new DateTime(2025, 6, 25, 16, 51, 7, 575, DateTimeKind.Utc).AddTicks(6119) });

            migrationBuilder.UpdateData(
                table: "Roles",
                keyColumn: "Id",
                keyValue: 3,
                columns: new[] { "CreatedAt", "UpdatedAt" },
                values: new object[] { new DateTime(2025, 6, 25, 16, 51, 7, 575, DateTimeKind.Utc).AddTicks(6121), new DateTime(2025, 6, 25, 16, 51, 7, 575, DateTimeKind.Utc).AddTicks(6122) });

            migrationBuilder.CreateIndex(
                name: "IX_FinancialTransactions_InvoiceId",
                table: "FinancialTransactions",
                column: "InvoiceId");

            migrationBuilder.CreateIndex(
                name: "IX_FinancialTransactions_PersonId",
                table: "FinancialTransactions",
                column: "PersonId");

            migrationBuilder.AddForeignKey(
                name: "FK_FinancialTransactions_Invoices_InvoiceId",
                table: "FinancialTransactions",
                column: "InvoiceId",
                principalTable: "Invoices",
                principalColumn: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_FinancialTransactions_Persons_PersonId",
                table: "FinancialTransactions",
                column: "PersonId",
                principalTable: "Persons",
                principalColumn: "Id");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_FinancialTransactions_Invoices_InvoiceId",
                table: "FinancialTransactions");

            migrationBuilder.DropForeignKey(
                name: "FK_FinancialTransactions_Persons_PersonId",
                table: "FinancialTransactions");

            migrationBuilder.DropIndex(
                name: "IX_FinancialTransactions_InvoiceId",
                table: "FinancialTransactions");

            migrationBuilder.DropIndex(
                name: "IX_FinancialTransactions_PersonId",
                table: "FinancialTransactions");

            migrationBuilder.DropColumn(
                name: "Category",
                table: "Items");

            migrationBuilder.DropColumn(
                name: "CostPrice",
                table: "Items");

            migrationBuilder.DropColumn(
                name: "ImageUrl",
                table: "Items");

            migrationBuilder.DropColumn(
                name: "IsActive",
                table: "Items");

            migrationBuilder.DropColumn(
                name: "MaximumStockLevel",
                table: "Items");

            migrationBuilder.DropColumn(
                name: "MinimumStockLevel",
                table: "Items");

            migrationBuilder.DropColumn(
                name: "ReorderPoint",
                table: "Items");

            migrationBuilder.DropColumn(
                name: "StockQuantity",
                table: "Items");

            migrationBuilder.DropColumn(
                name: "InvoiceId",
                table: "FinancialTransactions");

            migrationBuilder.DropColumn(
                name: "PersonId",
                table: "FinancialTransactions");

            migrationBuilder.AlterColumn<int>(
                name: "MainCashId",
                table: "FinancialTransactions",
                type: "int",
                nullable: false,
                defaultValue: 0,
                oldClrType: typeof(int),
                oldType: "int",
                oldNullable: true);

            migrationBuilder.UpdateData(
                table: "Roles",
                keyColumn: "Id",
                keyValue: 1,
                columns: new[] { "CreatedAt", "UpdatedAt" },
                values: new object[] { new DateTime(2025, 6, 25, 16, 24, 54, 645, DateTimeKind.Utc).AddTicks(6876), new DateTime(2025, 6, 25, 16, 24, 54, 645, DateTimeKind.Utc).AddTicks(7239) });

            migrationBuilder.UpdateData(
                table: "Roles",
                keyColumn: "Id",
                keyValue: 2,
                columns: new[] { "CreatedAt", "UpdatedAt" },
                values: new object[] { new DateTime(2025, 6, 25, 16, 24, 54, 645, DateTimeKind.Utc).AddTicks(7594), new DateTime(2025, 6, 25, 16, 24, 54, 645, DateTimeKind.Utc).AddTicks(7595) });

            migrationBuilder.UpdateData(
                table: "Roles",
                keyColumn: "Id",
                keyValue: 3,
                columns: new[] { "CreatedAt", "UpdatedAt" },
                values: new object[] { new DateTime(2025, 6, 25, 16, 24, 54, 645, DateTimeKind.Utc).AddTicks(7597), new DateTime(2025, 6, 25, 16, 24, 54, 645, DateTimeKind.Utc).AddTicks(7597) });
        }
    }
}
