using BusinessManagementSystem.Application.DTOs.RolePermission;

namespace BusinessManagementSystem.Application.Services.Interfaces;

/// <summary>
/// Role Permission service interface for business logic operations
/// واجهة خدمة صلاحية الدور لعمليات منطق الأعمال
/// </summary>
public interface IRolePermissionService
{
    /// <summary>
    /// Get all role permissions
    /// الحصول على جميع صلاحيات الأدوار
    /// </summary>
    Task<IEnumerable<RolePermissionDto>> GetAllAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// Get role permission by ID
    /// الحصول على صلاحية الدور بواسطة المعرف
    /// </summary>
    Task<RolePermissionDto?> GetByIdAsync(int id, CancellationToken cancellationToken = default);

    /// <summary>
    /// Get permissions by role ID
    /// الحصول على الصلاحيات بواسطة معرف الدور
    /// </summary>
    Task<IEnumerable<RolePermissionDto>> GetByRoleIdAsync(int roleId, CancellationToken cancellationToken = default);

    /// <summary>
    /// Get permissions by page name
    /// الحصول على الصلاحيات بواسطة اسم الصفحة
    /// </summary>
    Task<IEnumerable<RolePermissionDto>> GetByPageNameAsync(string pageName, CancellationToken cancellationToken = default);

    /// <summary>
    /// Get permission for specific role and page
    /// الحصول على الصلاحية لدور وصفحة محددين
    /// </summary>
    Task<RolePermissionDto?> GetByRoleAndPageAsync(int roleId, string pageName, CancellationToken cancellationToken = default);

    /// <summary>
    /// Create new role permission
    /// إنشاء صلاحية دور جديدة
    /// </summary>
    Task<RolePermissionDto> CreateAsync(CreateRolePermissionDto createRolePermissionDto, CancellationToken cancellationToken = default);

    /// <summary>
    /// Update existing role permission
    /// تحديث صلاحية دور موجودة
    /// </summary>
    Task<RolePermissionDto> UpdateAsync(int id, UpdateRolePermissionDto updateRolePermissionDto, CancellationToken cancellationToken = default);

    /// <summary>
    /// Delete role permission
    /// حذف صلاحية الدور
    /// </summary>
    Task<bool> DeleteAsync(int id, CancellationToken cancellationToken = default);

    /// <summary>
    /// Delete all permissions for a role
    /// حذف جميع صلاحيات الدور
    /// </summary>
    Task<bool> DeleteByRoleIdAsync(int roleId, CancellationToken cancellationToken = default);

    /// <summary>
    /// Delete all permissions for a page
    /// حذف جميع صلاحيات الصفحة
    /// </summary>
    Task<bool> DeleteByPageNameAsync(string pageName, CancellationToken cancellationToken = default);

    /// <summary>
    /// Get permission matrix for role
    /// الحصول على مصفوفة الصلاحيات للدور
    /// </summary>
    Task<PermissionMatrixDto> GetPermissionMatrixAsync(int roleId, CancellationToken cancellationToken = default);

    /// <summary>
    /// Bulk update permissions for role
    /// تحديث الصلاحيات بالجملة للدور
    /// </summary>
    Task<bool> BulkUpdatePermissionsAsync(BulkPermissionUpdateDto bulkUpdateDto, CancellationToken cancellationToken = default);

    /// <summary>
    /// Copy permissions from one role to another
    /// نسخ الصلاحيات من دور إلى آخر
    /// </summary>
    Task<bool> CopyPermissionsAsync(int sourceRoleId, int targetRoleId, bool replaceExisting = true, CancellationToken cancellationToken = default);

    /// <summary>
    /// Check if user has permission for specific action
    /// التحقق من وجود صلاحية للمستخدم لإجراء محدد
    /// </summary>
    Task<bool> HasPermissionAsync(int userId, string pageName, string action, CancellationToken cancellationToken = default);

    /// <summary>
    /// Get user permissions summary
    /// الحصول على ملخص صلاحيات المستخدم
    /// </summary>
    Task<object> GetUserPermissionsAsync(int userId, CancellationToken cancellationToken = default);

    /// <summary>
    /// Get all available pages in the system
    /// الحصول على جميع الصفحات المتاحة في النظام
    /// </summary>
    Task<AvailablePagesDto> GetAvailablePagesAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// Get permission templates
    /// الحصول على قوالب الصلاحيات
    /// </summary>
    Task<IEnumerable<PermissionTemplateDto>> GetPermissionTemplatesAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// Apply permission template to role
    /// تطبيق قالب الصلاحيات على الدور
    /// </summary>
    Task<bool> ApplyPermissionTemplateAsync(int roleId, string templateName, CancellationToken cancellationToken = default);

    /// <summary>
    /// Validate permission consistency
    /// التحقق من اتساق الصلاحيات
    /// </summary>
    Task<object> ValidatePermissionConsistencyAsync(int roleId, CancellationToken cancellationToken = default);

    /// <summary>
    /// Get permission usage statistics
    /// الحصول على إحصائيات استخدام الصلاحيات
    /// </summary>
    Task<object> GetPermissionUsageStatisticsAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// Export permissions to CSV/Excel format
    /// تصدير الصلاحيات إلى تنسيق CSV/Excel
    /// </summary>
    Task<byte[]> ExportPermissionsAsync(int? roleId = null, string format = "csv", CancellationToken cancellationToken = default);

    /// <summary>
    /// Import permissions from file
    /// استيراد الصلاحيات من ملف
    /// </summary>
    Task<object> ImportPermissionsAsync(byte[] fileData, string fileName, bool replaceExisting = false, CancellationToken cancellationToken = default);

    /// <summary>
    /// Get permission audit trail
    /// الحصول على مسار تدقيق الصلاحيات
    /// </summary>
    Task<IEnumerable<object>> GetPermissionAuditTrailAsync(int? roleId = null, string? pageName = null, CancellationToken cancellationToken = default);

    /// <summary>
    /// Set default permissions for new role
    /// تعيين الصلاحيات الافتراضية للدور الجديد
    /// </summary>
    Task<bool> SetDefaultPermissionsAsync(int roleId, string roleType = "Employee", CancellationToken cancellationToken = default);

    /// <summary>
    /// Get roles with specific permission
    /// الحصول على الأدوار التي لديها صلاحية محددة
    /// </summary>
    Task<IEnumerable<object>> GetRolesWithPermissionAsync(string pageName, string action, CancellationToken cancellationToken = default);

    /// <summary>
    /// Validate permission request
    /// التحقق من صحة طلب الصلاحية
    /// </summary>
    Task<bool> ValidatePermissionRequestAsync(CreateRolePermissionDto permissionDto, CancellationToken cancellationToken = default);
}
