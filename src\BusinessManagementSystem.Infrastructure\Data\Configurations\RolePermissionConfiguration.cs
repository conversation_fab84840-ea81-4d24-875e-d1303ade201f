using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using BusinessManagementSystem.Domain.Entities;

namespace BusinessManagementSystem.Infrastructure.Data.Configurations;

/// <summary>
/// Entity configuration for RolePermission
/// تكوين الكيان لصلاحيات الدور
/// </summary>
public class RolePermissionConfiguration : IEntityTypeConfiguration<RolePermission>
{
    public void Configure(EntityTypeBuilder<RolePermission> builder)
    {
        builder.ToTable("RolePermissions");

        builder.HasKey(rp => rp.Id);

        builder.Property(rp => rp.PageName)
            .IsRequired()
            .HasMaxLength(100);

        builder.Property(rp => rp.CanAdd)
            .HasDefaultValue(false);

        builder.Property(rp => rp.CanEdit)
            .HasDefaultValue(false);

        builder.Property(rp => rp.CanDelete)
            .HasDefaultValue(false);

        builder.Property(rp => rp.CanView)
            .HasDefaultValue(false);

        // Indexes
        builder.HasIndex(rp => new { rp.RoleId, rp.PageName })
            .IsUnique();

        // Relationships
        builder.HasOne(rp => rp.Role)
            .WithMany(r => r.RolePermissions)
            .HasForeignKey(rp => rp.RoleId)
            .OnDelete(DeleteBehavior.Cascade);
    }
}
