using BusinessManagementSystem.Application.Interfaces.Repositories;

namespace BusinessManagementSystem.Application.Interfaces;

/// <summary>
/// Unit of Work interface for managing transactions across multiple repositories
/// واجهة وحدة العمل لإدارة المعاملات عبر مستودعات متعددة
/// </summary>
public interface IUnitOfWork : IDisposable
{
    /// <summary>
    /// Company repository
    /// مستودع الشركة
    /// </summary>
    ICompanyRepository Companies { get; }

    /// <summary>
    /// Department repository
    /// مستودع القسم
    /// </summary>
    IGenericRepository<Domain.Entities.Department> Departments { get; }

    /// <summary>
    /// Partner repository
    /// مستودع الشريك
    /// </summary>
    IGenericRepository<Domain.Entities.Partner> Partners { get; }

    /// <summary>
    /// Main cash repository
    /// مستودع الخزينة الرئيسية
    /// </summary>
    IGenericRepository<Domain.Entities.MainCash> MainCashes { get; }

    /// <summary>
    /// Financial transaction repository
    /// مستودع المعاملة المالية
    /// </summary>
    IGenericRepository<Domain.Entities.FinancialTransaction> FinancialTransactions { get; }

    /// <summary>
    /// Warehouse repository
    /// مستودع المستودع
    /// </summary>
    IGenericRepository<Domain.Entities.Warehouse> Warehouses { get; }

    /// <summary>
    /// Item repository
    /// مستودع الصنف
    /// </summary>
    IGenericRepository<Domain.Entities.Item> Items { get; }

    /// <summary>
    /// Inventory transaction repository
    /// مستودع معاملة المخزون
    /// </summary>
    IInventoryTransactionRepository InventoryTransactions { get; }

    /// <summary>
    /// Invoice repository
    /// مستودع الفاتورة
    /// </summary>
    IInvoiceRepository Invoices { get; }

    /// <summary>
    /// Invoice detail repository
    /// مستودع تفاصيل الفاتورة
    /// </summary>
    IGenericRepository<Domain.Entities.InvoiceDetail> InvoiceDetails { get; }

    /// <summary>
    /// Person repository
    /// مستودع الشخص
    /// </summary>
    IGenericRepository<Domain.Entities.Person> Persons { get; }

    /// <summary>
    /// User repository
    /// مستودع المستخدم
    /// </summary>
    IUserRepository Users { get; }

    /// <summary>
    /// Role repository
    /// مستودع الدور
    /// </summary>
    IGenericRepository<Domain.Entities.Role> Roles { get; }

    /// <summary>
    /// Role permission repository
    /// مستودع صلاحيات الدور
    /// </summary>
    IGenericRepository<Domain.Entities.RolePermission> RolePermissions { get; }

    /// <summary>
    /// Save all changes to the database
    /// حفظ جميع التغييرات في قاعدة البيانات
    /// </summary>
    Task<int> SaveChangesAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// Begin a database transaction
    /// بدء معاملة قاعدة بيانات
    /// </summary>
    Task BeginTransactionAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// Commit the current transaction
    /// تأكيد المعاملة الحالية
    /// </summary>
    Task CommitTransactionAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// Rollback the current transaction
    /// التراجع عن المعاملة الحالية
    /// </summary>
    Task RollbackTransactionAsync(CancellationToken cancellationToken = default);
}
