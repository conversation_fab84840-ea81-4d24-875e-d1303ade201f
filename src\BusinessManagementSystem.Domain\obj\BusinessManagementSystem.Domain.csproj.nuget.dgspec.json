{"format": 1, "restore": {"D:\\AIProjectTest\\Sourcs\\DoorAPP\\src\\BusinessManagementSystem.Domain\\BusinessManagementSystem.Domain.csproj": {}}, "projects": {"D:\\AIProjectTest\\Sourcs\\DoorAPP\\src\\BusinessManagementSystem.Domain\\BusinessManagementSystem.Domain.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\AIProjectTest\\Sourcs\\DoorAPP\\src\\BusinessManagementSystem.Domain\\BusinessManagementSystem.Domain.csproj", "projectName": "BusinessManagementSystem.Domain", "projectPath": "D:\\AIProjectTest\\Sourcs\\DoorAPP\\src\\BusinessManagementSystem.Domain\\BusinessManagementSystem.Domain.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\AIProjectTest\\Sourcs\\DoorAPP\\src\\BusinessManagementSystem.Domain\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files\\DevExpress 23.2\\Components\\Offline Packages", "C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\DevExpress 23.2.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\DevExpress 23.2\\Components\\System\\Components\\Packages": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.201/PortableRuntimeIdentifierGraph.json"}}}}}