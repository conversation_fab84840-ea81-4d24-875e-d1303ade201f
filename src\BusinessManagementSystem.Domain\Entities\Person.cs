using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using BusinessManagementSystem.Domain.Common;

namespace BusinessManagementSystem.Domain.Entities;

/// <summary>
/// Person entity - stores customers and suppliers information
/// كيان الشخص - يخزن معلومات العملاء والموردين
/// </summary>
public class Person : BaseEntity
{
    /// <summary>
    /// Person name
    /// اسم الشخص
    /// </summary>
    [Required]
    [MaxLength(200)]
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// Person address
    /// عنوان الشخص
    /// </summary>
    [MaxLength(500)]
    public string? Address { get; set; }

    /// <summary>
    /// Phone number
    /// رقم الهاتف
    /// </summary>
    [MaxLength(20)]
    public string? Phone { get; set; }

    /// <summary>
    /// Email address
    /// عنوان البريد الإلكتروني
    /// </summary>
    [MaxLength(100)]
    public string? Email { get; set; }

    /// <summary>
    /// Person type (Customer/Supplier)
    /// نوع الشخص (عميل/مورد)
    /// </summary>
    [Required]
    [MaxLength(20)]
    public string PersonType { get; set; } = string.Empty;

    /// <summary>
    /// Current account balance
    /// الرصيد الحالي للحساب
    /// </summary>
    [Column(TypeName = "decimal(18,2)")]
    public decimal CurrentBalance { get; set; }

    /// <summary>
    /// Tax identification number
    /// الرقم الضريبي
    /// </summary>
    [MaxLength(50)]
    public string? TaxNumber { get; set; }

    /// <summary>
    /// Company identifier (Foreign Key)
    /// معرف الشركة (مفتاح خارجي)
    /// </summary>
    [Required]
    public int CompanyId { get; set; }

    // Navigation properties
    [ForeignKey(nameof(CompanyId))]
    public virtual Company Company { get; set; } = null!;
    
    public virtual ICollection<Invoice> Invoices { get; set; } = new List<Invoice>();
}
