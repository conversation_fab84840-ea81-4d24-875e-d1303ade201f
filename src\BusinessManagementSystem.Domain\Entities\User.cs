using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using BusinessManagementSystem.Domain.Common;

namespace BusinessManagementSystem.Domain.Entities;

/// <summary>
/// User entity - stores system users
/// كيان المستخدم - يخزن مستخدمي النظام
/// </summary>
public class User : BaseEntity
{
    /// <summary>
    /// Username for login
    /// اسم المستخدم لتسجيل الدخول
    /// </summary>
    [Required]
    [MaxLength(50)]
    public string UserName { get; set; } = string.Empty;

    /// <summary>
    /// Hashed password
    /// كلمة المرور المشفرة
    /// </summary>
    [Required]
    [MaxLength(500)]
    public string PasswordHash { get; set; } = string.Empty;

    /// <summary>
    /// Full name of the user
    /// الاسم الكامل للمستخدم
    /// </summary>
    [Required]
    [MaxLength(200)]
    public string FullName { get; set; } = string.Empty;

    /// <summary>
    /// Email address
    /// عنوان البريد الإلكتروني
    /// </summary>
    [MaxLength(100)]
    public string? Email { get; set; }

    /// <summary>
    /// Whether the user account is active
    /// ما إذا كان حساب المستخدم نشطًا
    /// </summary>
    public bool IsActive { get; set; } = true;

    /// <summary>
    /// Refresh token for JWT authentication
    /// رمز التحديث لمصادقة JWT
    /// </summary>
    [MaxLength(500)]
    public string? RefreshToken { get; set; }

    /// <summary>
    /// Refresh token expiry time
    /// وقت انتهاء رمز التحديث
    /// </summary>
    public DateTime? RefreshTokenExpiryTime { get; set; }

    /// <summary>
    /// Last login timestamp
    /// طابع زمني لآخر تسجيل دخول
    /// </summary>
    public DateTime? LastLoginDate { get; set; }

    /// <summary>
    /// Role identifier (Foreign Key)
    /// معرف الدور (مفتاح خارجي)
    /// </summary>
    [Required]
    public int RoleId { get; set; }

    /// <summary>
    /// Company identifier (Foreign Key)
    /// معرف الشركة (مفتاح خارجي)
    /// </summary>
    [Required]
    public int CompanyId { get; set; }

    // Navigation properties
    [ForeignKey(nameof(RoleId))]
    public virtual Role Role { get; set; } = null!;

    [ForeignKey(nameof(CompanyId))]
    public virtual Company Company { get; set; } = null!;
}
