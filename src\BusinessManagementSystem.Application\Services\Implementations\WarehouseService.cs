using AutoMapper;
using Microsoft.Extensions.Logging;
using BusinessManagementSystem.Application.DTOs.Warehouse;
using BusinessManagementSystem.Application.Interfaces;
using BusinessManagementSystem.Application.Services.Interfaces;
using BusinessManagementSystem.Domain.Entities;

namespace BusinessManagementSystem.Application.Services.Implementations;

/// <summary>
/// Warehouse service implementation for business logic operations
/// تنفيذ خدمة المستودع لعمليات منطق الأعمال
/// </summary>
public class WarehouseService : IWarehouseService
{
    private readonly IUnitOfWork _unitOfWork;
    private readonly IMapper _mapper;
    private readonly ILogger<WarehouseService> _logger;

    public WarehouseService(IUnitOfWork unitOfWork, IMapper mapper, ILogger<WarehouseService> logger)
    {
        _unitOfWork = unitOfWork;
        _mapper = mapper;
        _logger = logger;
    }

    public async Task<IEnumerable<WarehouseDto>> GetAllAsync(CancellationToken cancellationToken = default)
    {
        var warehouses = await _unitOfWork.Warehouses.GetAsync(
            includeProperties: "Company",
            cancellationToken: cancellationToken);
        return _mapper.Map<IEnumerable<WarehouseDto>>(warehouses);
    }

    public async Task<WarehouseDto?> GetByIdAsync(int id, CancellationToken cancellationToken = default)
    {
        var warehouse = await _unitOfWork.Warehouses.GetByIdAsync(id, cancellationToken);
        return warehouse != null ? _mapper.Map<WarehouseDto>(warehouse) : null;
    }

    public async Task<IEnumerable<WarehouseDto>> GetByCompanyAsync(int companyId, CancellationToken cancellationToken = default)
    {
        var warehouses = await _unitOfWork.Warehouses.GetAsync(
            filter: w => w.CompanyId == companyId,
            includeProperties: "Company",
            cancellationToken: cancellationToken);
        return _mapper.Map<IEnumerable<WarehouseDto>>(warehouses);
    }

    public async Task<IEnumerable<WarehouseDto>> GetActiveByCompanyAsync(int companyId, CancellationToken cancellationToken = default)
    {
        var warehouses = await _unitOfWork.Warehouses.GetAsync(
            filter: w => w.CompanyId == companyId && w.IsActive,
            includeProperties: "Company",
            cancellationToken: cancellationToken);
        return _mapper.Map<IEnumerable<WarehouseDto>>(warehouses);
    }

    public async Task<IEnumerable<WarehouseDto>> SearchAsync(int companyId, string searchTerm, CancellationToken cancellationToken = default)
    {
        var warehouses = await _unitOfWork.Warehouses.GetAsync(
            filter: w => w.CompanyId == companyId && 
                        (w.Name.Contains(searchTerm) || 
                         (w.Location != null && w.Location.Contains(searchTerm)) ||
                         (w.Description != null && w.Description.Contains(searchTerm))),
            includeProperties: "Company",
            cancellationToken: cancellationToken);
        return _mapper.Map<IEnumerable<WarehouseDto>>(warehouses);
    }

    public async Task<IEnumerable<WarehouseDto>> GetLowCapacityWarehousesAsync(int companyId, decimal thresholdPercentage = 90, CancellationToken cancellationToken = default)
    {
        var warehouses = await _unitOfWork.Warehouses.GetAsync(
            filter: w => w.CompanyId == companyId && w.IsActive && 
                        w.MaxCapacity > 0 && 
                        (w.CurrentUtilization / w.MaxCapacity * 100) >= thresholdPercentage,
            includeProperties: "Company",
            cancellationToken: cancellationToken);
        return _mapper.Map<IEnumerable<WarehouseDto>>(warehouses);
    }

    public async Task<WarehouseDto> CreateAsync(CreateWarehouseDto createWarehouseDto, CancellationToken cancellationToken = default)
    {
        // Validate business rules
        await ValidateWarehouseForCreateAsync(createWarehouseDto, cancellationToken);

        var warehouse = _mapper.Map<Warehouse>(createWarehouseDto);
        warehouse.CurrentUtilization = 0; // New warehouse starts empty

        await _unitOfWork.Warehouses.AddAsync(warehouse, cancellationToken);
        await _unitOfWork.SaveChangesAsync(cancellationToken);

        _logger.LogInformation("Warehouse created successfully with ID: {WarehouseId}", warehouse.Id);
        return _mapper.Map<WarehouseDto>(warehouse);
    }

    public async Task<WarehouseDto> UpdateAsync(int id, UpdateWarehouseDto updateWarehouseDto, CancellationToken cancellationToken = default)
    {
        var existingWarehouse = await _unitOfWork.Warehouses.GetByIdAsync(id, cancellationToken);
        if (existingWarehouse == null)
        {
            throw new ArgumentException($"Warehouse with ID {id} not found.", nameof(id));
        }

        // Validate business rules
        await ValidateWarehouseForUpdateAsync(id, updateWarehouseDto, cancellationToken);

        // Check if reducing max capacity below current utilization
        if (updateWarehouseDto.MaxCapacity < existingWarehouse.CurrentUtilization)
        {
            throw new InvalidOperationException($"Cannot reduce max capacity below current utilization ({existingWarehouse.CurrentUtilization}).");
        }

        _mapper.Map(updateWarehouseDto, existingWarehouse);
        _unitOfWork.Warehouses.Update(existingWarehouse);
        await _unitOfWork.SaveChangesAsync(cancellationToken);

        _logger.LogInformation("Warehouse updated successfully with ID: {WarehouseId}", id);
        return _mapper.Map<WarehouseDto>(existingWarehouse);
    }

    public async Task<bool> SetActiveStatusAsync(int id, bool isActive, CancellationToken cancellationToken = default)
    {
        var warehouse = await _unitOfWork.Warehouses.GetByIdAsync(id, cancellationToken);
        if (warehouse == null)
        {
            return false;
        }

        // If deactivating, check if warehouse has inventory
        if (!isActive && warehouse.CurrentUtilization > 0)
        {
            throw new InvalidOperationException("Cannot deactivate warehouse that contains inventory.");
        }

        warehouse.IsActive = isActive;
        _unitOfWork.Warehouses.Update(warehouse);
        await _unitOfWork.SaveChangesAsync(cancellationToken);

        _logger.LogInformation("Warehouse status changed to {Status} for ID: {WarehouseId}", 
            isActive ? "Active" : "Inactive", id);
        return true;
    }

    public async Task<bool> DeleteAsync(int id, CancellationToken cancellationToken = default)
    {
        var warehouse = await _unitOfWork.Warehouses.GetByIdAsync(id, cancellationToken);
        if (warehouse == null)
        {
            return false;
        }

        // Check if warehouse has inventory
        if (warehouse.CurrentUtilization > 0)
        {
            throw new InvalidOperationException("Cannot delete warehouse that contains inventory.");
        }

        // Check if warehouse has inventory transactions
        var transactions = await _unitOfWork.InventoryTransactions.GetAsync(
            filter: t => t.WarehouseId == id, cancellationToken: cancellationToken);
        if (transactions.Any())
        {
            throw new InvalidOperationException("Cannot delete warehouse that has transaction history.");
        }

        _unitOfWork.Warehouses.Delete(warehouse);
        await _unitOfWork.SaveChangesAsync(cancellationToken);

        _logger.LogInformation("Warehouse deleted successfully with ID: {WarehouseId}", id);
        return true;
    }

    public async Task<bool> TransferItemsAsync(WarehouseTransferDto transferDto, CancellationToken cancellationToken = default)
    {
        // Validate transfer
        if (!await ValidateTransferAsync(transferDto, cancellationToken))
        {
            return false;
        }

        await _unitOfWork.BeginTransactionAsync(cancellationToken);
        try
        {
            var fromWarehouse = await _unitOfWork.Warehouses.GetByIdAsync(transferDto.FromWarehouseId, cancellationToken);
            var toWarehouse = await _unitOfWork.Warehouses.GetByIdAsync(transferDto.ToWarehouseId, cancellationToken);
            var item = await _unitOfWork.Items.GetByIdAsync(transferDto.ItemId, cancellationToken);

            if (fromWarehouse == null || toWarehouse == null || item == null)
            {
                throw new ArgumentException("Invalid warehouse or item ID.");
            }

            // Create outbound transaction
            var outTransaction = new InventoryTransaction
            {
                TransactionDate = transferDto.TransferDate,
                TransactionType = "Transfer-Out",
                Quantity = transferDto.Quantity,
                UnitCost = item.CostPrice,
                TotalCost = transferDto.Quantity * item.CostPrice,
                ReferenceNumber = $"TRF-OUT-{DateTime.UtcNow:yyyyMMddHHmmss}",
                Notes = transferDto.Notes,
                ItemId = transferDto.ItemId,
                WarehouseId = transferDto.FromWarehouseId,
                CompanyId = fromWarehouse.CompanyId
            };

            // Create inbound transaction
            var inTransaction = new InventoryTransaction
            {
                TransactionDate = transferDto.TransferDate,
                TransactionType = "Transfer-In",
                Quantity = transferDto.Quantity,
                UnitCost = item.CostPrice,
                TotalCost = transferDto.Quantity * item.CostPrice,
                ReferenceNumber = $"TRF-IN-{DateTime.UtcNow:yyyyMMddHHmmss}",
                Notes = transferDto.Notes,
                ItemId = transferDto.ItemId,
                WarehouseId = transferDto.ToWarehouseId,
                CompanyId = toWarehouse.CompanyId
            };

            await _unitOfWork.InventoryTransactions.AddAsync(outTransaction, cancellationToken);
            await _unitOfWork.InventoryTransactions.AddAsync(inTransaction, cancellationToken);

            // Update warehouse utilizations (simplified - in real scenario, this would be more complex)
            var itemVolume = transferDto.Quantity; // Assuming 1:1 ratio for simplicity
            fromWarehouse.CurrentUtilization -= itemVolume;
            toWarehouse.CurrentUtilization += itemVolume;

            _unitOfWork.Warehouses.Update(fromWarehouse);
            _unitOfWork.Warehouses.Update(toWarehouse);

            await _unitOfWork.SaveChangesAsync(cancellationToken);
            await _unitOfWork.CommitTransactionAsync(cancellationToken);

            _logger.LogInformation("Items transferred successfully from warehouse {FromId} to {ToId}", 
                transferDto.FromWarehouseId, transferDto.ToWarehouseId);
            return true;
        }
        catch
        {
            await _unitOfWork.RollbackTransactionAsync(cancellationToken);
            throw;
        }
    }

    public async Task<bool> UpdateUtilizationAsync(int warehouseId, decimal utilizationChange, CancellationToken cancellationToken = default)
    {
        var warehouse = await _unitOfWork.Warehouses.GetByIdAsync(warehouseId, cancellationToken);
        if (warehouse == null)
        {
            return false;
        }

        var newUtilization = warehouse.CurrentUtilization + utilizationChange;
        if (newUtilization < 0)
        {
            throw new InvalidOperationException("Warehouse utilization cannot be negative.");
        }

        if (newUtilization > warehouse.MaxCapacity)
        {
            throw new InvalidOperationException("Warehouse utilization cannot exceed maximum capacity.");
        }

        warehouse.CurrentUtilization = newUtilization;
        _unitOfWork.Warehouses.Update(warehouse);
        await _unitOfWork.SaveChangesAsync(cancellationToken);

        return true;
    }

    public async Task<WarehouseCapacityReportDto> GetCapacityReportAsync(int warehouseId, CancellationToken cancellationToken = default)
    {
        var warehouse = await _unitOfWork.Warehouses.GetByIdAsync(warehouseId, cancellationToken);
        if (warehouse == null)
        {
            throw new ArgumentException($"Warehouse with ID {warehouseId} not found.");
        }

        var warehouseDto = _mapper.Map<WarehouseDto>(warehouse);

        // Get inventory transactions for this warehouse
        var transactions = await _unitOfWork.InventoryTransactions.GetAsync(
            filter: t => t.WarehouseId == warehouseId,
            includeProperties: "Item",
            cancellationToken: cancellationToken);

        // Calculate statistics (simplified)
        var totalItemsCount = transactions.Count();
        var totalValue = transactions.Sum(t => t.TotalCost);

        return new WarehouseCapacityReportDto
        {
            Warehouse = warehouseDto,
            TotalItemsCount = totalItemsCount,
            TotalValue = totalValue,
            ItemsByCategory = new List<CategorySummaryDto>(),
            LowStockItems = new List<LowStockItemDto>()
        };
    }

    public async Task<object> GetUtilizationSummaryAsync(int companyId, CancellationToken cancellationToken = default)
    {
        var warehouses = await GetByCompanyAsync(companyId, cancellationToken);
        
        var totalCapacity = warehouses.Sum(w => w.MaxCapacity);
        var totalUtilization = warehouses.Sum(w => w.CurrentUtilization);
        var averageUtilization = warehouses.Any() ? warehouses.Average(w => w.UtilizationPercentage) : 0;

        return new
        {
            TotalWarehouses = warehouses.Count(),
            ActiveWarehouses = warehouses.Count(w => w.IsActive),
            TotalCapacity = totalCapacity,
            TotalUtilization = totalUtilization,
            AvailableCapacity = totalCapacity - totalUtilization,
            AverageUtilizationPercentage = averageUtilization,
            OverCapacityWarehouses = warehouses.Count(w => w.UtilizationPercentage > 90),
            UnderUtilizedWarehouses = warehouses.Count(w => w.UtilizationPercentage < 50)
        };
    }

    public async Task<bool> HasSufficientCapacityAsync(int warehouseId, decimal requiredCapacity, CancellationToken cancellationToken = default)
    {
        var warehouse = await _unitOfWork.Warehouses.GetByIdAsync(warehouseId, cancellationToken);
        if (warehouse == null || !warehouse.IsActive)
        {
            return false;
        }

        return (warehouse.MaxCapacity - warehouse.CurrentUtilization) >= requiredCapacity;
    }

    public async Task<WarehouseDto?> GetOptimalWarehouseAsync(int companyId, decimal requiredCapacity, CancellationToken cancellationToken = default)
    {
        var warehouses = await GetActiveByCompanyAsync(companyId, cancellationToken);
        
        var suitableWarehouse = warehouses
            .Where(w => (w.MaxCapacity - w.CurrentUtilization) >= requiredCapacity)
            .OrderBy(w => w.UtilizationPercentage) // Prefer less utilized warehouses
            .FirstOrDefault();

        return suitableWarehouse;
    }

    public async Task<object> GetWarehouseStatisticsAsync(int warehouseId, CancellationToken cancellationToken = default)
    {
        var warehouse = await GetByIdAsync(warehouseId, cancellationToken);
        if (warehouse == null)
        {
            throw new ArgumentException($"Warehouse with ID {warehouseId} not found.");
        }

        var transactions = await _unitOfWork.InventoryTransactions.GetAsync(
            filter: t => t.WarehouseId == warehouseId,
            cancellationToken: cancellationToken);

        var last30Days = DateTime.UtcNow.AddDays(-30);
        var recentTransactions = transactions.Where(t => t.TransactionDate >= last30Days);

        return new
        {
            Warehouse = warehouse,
            TotalTransactions = transactions.Count(),
            RecentTransactions = recentTransactions.Count(),
            TotalValue = transactions.Sum(t => t.TotalCost),
            AverageTransactionValue = transactions.Any() ? transactions.Average(t => t.TotalCost) : 0,
            LastTransactionDate = transactions.Any() ? transactions.Max(t => t.TransactionDate) : (DateTime?)null,
            TransactionsByType = transactions.GroupBy(t => t.TransactionType)
                .Select(g => new { Type = g.Key, Count = g.Count(), TotalValue = g.Sum(t => t.TotalCost) })
                .ToList()
        };
    }

    public async Task<bool> ValidateTransferAsync(WarehouseTransferDto transferDto, CancellationToken cancellationToken = default)
    {
        if (transferDto.FromWarehouseId == transferDto.ToWarehouseId)
        {
            throw new ArgumentException("Source and destination warehouses cannot be the same.");
        }

        var fromWarehouse = await _unitOfWork.Warehouses.GetByIdAsync(transferDto.FromWarehouseId, cancellationToken);
        var toWarehouse = await _unitOfWork.Warehouses.GetByIdAsync(transferDto.ToWarehouseId, cancellationToken);
        var item = await _unitOfWork.Items.GetByIdAsync(transferDto.ItemId, cancellationToken);

        if (fromWarehouse == null || !fromWarehouse.IsActive)
        {
            throw new ArgumentException("Source warehouse not found or inactive.");
        }

        if (toWarehouse == null || !toWarehouse.IsActive)
        {
            throw new ArgumentException("Destination warehouse not found or inactive.");
        }

        if (item == null || !item.IsActive)
        {
            throw new ArgumentException("Item not found or inactive.");
        }

        // Check if destination warehouse has sufficient capacity
        var requiredCapacity = transferDto.Quantity; // Simplified
        if (!await HasSufficientCapacityAsync(transferDto.ToWarehouseId, requiredCapacity, cancellationToken))
        {
            throw new InvalidOperationException("Destination warehouse does not have sufficient capacity.");
        }

        return true;
    }

    private async Task ValidateWarehouseForCreateAsync(CreateWarehouseDto createWarehouseDto, CancellationToken cancellationToken)
    {
        // Validate company exists
        var company = await _unitOfWork.Companies.GetByIdAsync(createWarehouseDto.CompanyId, cancellationToken);
        if (company == null)
        {
            throw new ArgumentException("Invalid company ID.", nameof(createWarehouseDto.CompanyId));
        }

        // Check for duplicate warehouse name within company
        var existingWarehouses = await _unitOfWork.Warehouses.GetAsync(
            filter: w => w.CompanyId == createWarehouseDto.CompanyId && w.Name == createWarehouseDto.Name,
            cancellationToken: cancellationToken);

        if (existingWarehouses.Any())
        {
            throw new ArgumentException("A warehouse with this name already exists in the company.");
        }
    }

    private async Task ValidateWarehouseForUpdateAsync(int id, UpdateWarehouseDto updateWarehouseDto, CancellationToken cancellationToken)
    {
        var currentWarehouse = await _unitOfWork.Warehouses.GetByIdAsync(id, cancellationToken);
        if (currentWarehouse == null)
        {
            throw new ArgumentException("Warehouse not found.");
        }

        // Check for duplicate warehouse name within company (excluding current warehouse)
        var existingWarehouses = await _unitOfWork.Warehouses.GetAsync(
            filter: w => w.Id != id && w.CompanyId == currentWarehouse.CompanyId && w.Name == updateWarehouseDto.Name,
            cancellationToken: cancellationToken);

        if (existingWarehouses.Any())
        {
            throw new ArgumentException("A warehouse with this name already exists in the company.");
        }
    }
}
