using BusinessManagementSystem.Application.DTOs.FinancialTransaction;

namespace BusinessManagementSystem.Application.Services.Interfaces;

/// <summary>
/// Financial Transaction service interface for business logic operations
/// واجهة خدمة المعاملة المالية لعمليات منطق الأعمال
/// </summary>
public interface IFinancialTransactionService
{
    /// <summary>
    /// Get all financial transactions
    /// الحصول على جميع المعاملات المالية
    /// </summary>
    Task<IEnumerable<FinancialTransactionDto>> GetAllAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// Get financial transaction by ID
    /// الحصول على المعاملة المالية بواسطة المعرف
    /// </summary>
    Task<FinancialTransactionDto?> GetByIdAsync(int id, CancellationToken cancellationToken = default);

    /// <summary>
    /// Get financial transactions by company
    /// الحصول على المعاملات المالية بواسطة الشركة
    /// </summary>
    Task<IEnumerable<FinancialTransactionDto>> GetByCompanyAsync(int companyId, CancellationToken cancellationToken = default);

    /// <summary>
    /// Get financial transactions by type
    /// الحصول على المعاملات المالية بواسطة النوع
    /// </summary>
    Task<IEnumerable<FinancialTransactionDto>> GetByTypeAsync(int companyId, string transactionType, CancellationToken cancellationToken = default);

    /// <summary>
    /// Get financial transactions by person
    /// الحصول على المعاملات المالية بواسطة الشخص
    /// </summary>
    Task<IEnumerable<FinancialTransactionDto>> GetByPersonAsync(int personId, CancellationToken cancellationToken = default);

    /// <summary>
    /// Get financial transactions by date range
    /// الحصول على المعاملات المالية بواسطة نطاق التاريخ
    /// </summary>
    Task<IEnumerable<FinancialTransactionDto>> GetByDateRangeAsync(int companyId, DateTime startDate, DateTime endDate, CancellationToken cancellationToken = default);

    /// <summary>
    /// Get financial transactions by invoice
    /// الحصول على المعاملات المالية بواسطة الفاتورة
    /// </summary>
    Task<IEnumerable<FinancialTransactionDto>> GetByInvoiceAsync(int invoiceId, CancellationToken cancellationToken = default);

    /// <summary>
    /// Create new financial transaction
    /// إنشاء معاملة مالية جديدة
    /// </summary>
    Task<FinancialTransactionDto> CreateAsync(CreateFinancialTransactionDto createTransactionDto, CancellationToken cancellationToken = default);

    /// <summary>
    /// Update existing financial transaction
    /// تحديث معاملة مالية موجودة
    /// </summary>
    Task<FinancialTransactionDto> UpdateAsync(int id, UpdateFinancialTransactionDto updateTransactionDto, CancellationToken cancellationToken = default);

    /// <summary>
    /// Delete financial transaction
    /// حذف المعاملة المالية
    /// </summary>
    Task<bool> DeleteAsync(int id, CancellationToken cancellationToken = default);

    /// <summary>
    /// Create transaction for invoice payment
    /// إنشاء معاملة لدفع الفاتورة
    /// </summary>
    Task<FinancialTransactionDto> CreateInvoicePaymentAsync(int invoiceId, decimal amount, string paymentMethod, CancellationToken cancellationToken = default);

    /// <summary>
    /// Create transaction for partner capital
    /// إنشاء معاملة لرأس مال الشريك
    /// </summary>
    Task<FinancialTransactionDto> CreatePartnerCapitalTransactionAsync(int partnerId, decimal amount, string transactionType, string description, CancellationToken cancellationToken = default);

    /// <summary>
    /// Get cash flow report
    /// الحصول على تقرير التدفق النقدي
    /// </summary>
    Task<CashFlowReportDto> GetCashFlowReportAsync(int companyId, DateTime startDate, DateTime endDate, CancellationToken cancellationToken = default);

    /// <summary>
    /// Get balance summary
    /// الحصول على ملخص الرصيد
    /// </summary>
    Task<BalanceSummaryDto> GetBalanceSummaryAsync(int companyId, CancellationToken cancellationToken = default);

    /// <summary>
    /// Get profit and loss report
    /// الحصول على تقرير الأرباح والخسائر
    /// </summary>
    Task<ProfitLossReportDto> GetProfitLossReportAsync(int companyId, DateTime startDate, DateTime endDate, CancellationToken cancellationToken = default);

    /// <summary>
    /// Get total income by date range
    /// الحصول على إجمالي الدخل بواسطة نطاق التاريخ
    /// </summary>
    Task<decimal> GetTotalIncomeByDateRangeAsync(int companyId, DateTime startDate, DateTime endDate, CancellationToken cancellationToken = default);

    /// <summary>
    /// Get total expenses by date range
    /// الحصول على إجمالي المصروفات بواسطة نطاق التاريخ
    /// </summary>
    Task<decimal> GetTotalExpensesByDateRangeAsync(int companyId, DateTime startDate, DateTime endDate, CancellationToken cancellationToken = default);

    /// <summary>
    /// Get current balance
    /// الحصول على الرصيد الحالي
    /// </summary>
    Task<decimal> GetCurrentBalanceAsync(int companyId, CancellationToken cancellationToken = default);

    /// <summary>
    /// Get transaction types
    /// الحصول على أنواع المعاملات
    /// </summary>
    Task<IEnumerable<string>> GetTransactionTypesAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// Validate transaction
    /// التحقق من صحة المعاملة
    /// </summary>
    Task<bool> ValidateTransactionAsync(CreateFinancialTransactionDto transactionDto, CancellationToken cancellationToken = default);

    /// <summary>
    /// Get monthly summary
    /// الحصول على الملخص الشهري
    /// </summary>
    Task<IEnumerable<object>> GetMonthlySummaryAsync(int companyId, int year, CancellationToken cancellationToken = default);
}
