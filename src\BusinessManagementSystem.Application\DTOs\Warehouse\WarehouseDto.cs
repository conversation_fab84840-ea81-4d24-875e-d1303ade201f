using BusinessManagementSystem.Application.DTOs.Common;
using System.ComponentModel.DataAnnotations;

namespace BusinessManagementSystem.Application.DTOs.Warehouse;

/// <summary>
/// Warehouse DTO for data transfer
/// DTO المستودع لنقل البيانات
/// </summary>
public class WarehouseDto : BaseDto
{
    /// <summary>
    /// Warehouse name
    /// اسم المستودع
    /// </summary>
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// Warehouse description
    /// وصف المستودع
    /// </summary>
    public string? Description { get; set; }

    /// <summary>
    /// Warehouse location/address
    /// موقع/عنوان المستودع
    /// </summary>
    public string? Location { get; set; }

    /// <summary>
    /// Maximum capacity of the warehouse
    /// السعة القصوى للمستودع
    /// </summary>
    public decimal MaxCapacity { get; set; }

    /// <summary>
    /// Current utilization of the warehouse
    /// الاستخدام الحالي للمستودع
    /// </summary>
    public decimal CurrentUtilization { get; set; }

    /// <summary>
    /// Whether the warehouse is active
    /// ما إذا كان المستودع نشطًا
    /// </summary>
    public bool IsActive { get; set; }

    /// <summary>
    /// Company ID
    /// معرف الشركة
    /// </summary>
    public int CompanyId { get; set; }

    /// <summary>
    /// Company name
    /// اسم الشركة
    /// </summary>
    public string CompanyName { get; set; } = string.Empty;

    /// <summary>
    /// Available capacity (MaxCapacity - CurrentUtilization)
    /// السعة المتاحة (السعة القصوى - الاستخدام الحالي)
    /// </summary>
    public decimal AvailableCapacity => MaxCapacity - CurrentUtilization;

    /// <summary>
    /// Utilization percentage
    /// نسبة الاستخدام
    /// </summary>
    public decimal UtilizationPercentage => MaxCapacity > 0 ? (CurrentUtilization / MaxCapacity) * 100 : 0;
}

/// <summary>
/// Create Warehouse DTO
/// DTO إنشاء المستودع
/// </summary>
public class CreateWarehouseDto
{
    /// <summary>
    /// Warehouse name
    /// اسم المستودع
    /// </summary>
    [Required(ErrorMessage = "Warehouse name is required")]
    [StringLength(100, ErrorMessage = "Warehouse name cannot exceed 100 characters")]
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// Warehouse description
    /// وصف المستودع
    /// </summary>
    [StringLength(500, ErrorMessage = "Description cannot exceed 500 characters")]
    public string? Description { get; set; }

    /// <summary>
    /// Warehouse location/address
    /// موقع/عنوان المستودع
    /// </summary>
    [StringLength(200, ErrorMessage = "Location cannot exceed 200 characters")]
    public string? Location { get; set; }

    /// <summary>
    /// Maximum capacity of the warehouse
    /// السعة القصوى للمستودع
    /// </summary>
    [Range(0, double.MaxValue, ErrorMessage = "Max capacity must be a positive number")]
    public decimal MaxCapacity { get; set; }

    /// <summary>
    /// Whether the warehouse is active
    /// ما إذا كان المستودع نشطًا
    /// </summary>
    public bool IsActive { get; set; } = true;

    /// <summary>
    /// Company ID
    /// معرف الشركة
    /// </summary>
    public int CompanyId { get; set; }
}

/// <summary>
/// Update Warehouse DTO
/// DTO تحديث المستودع
/// </summary>
public class UpdateWarehouseDto
{
    /// <summary>
    /// Warehouse name
    /// اسم المستودع
    /// </summary>
    [Required(ErrorMessage = "Warehouse name is required")]
    [StringLength(100, ErrorMessage = "Warehouse name cannot exceed 100 characters")]
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// Warehouse description
    /// وصف المستودع
    /// </summary>
    [StringLength(500, ErrorMessage = "Description cannot exceed 500 characters")]
    public string? Description { get; set; }

    /// <summary>
    /// Warehouse location/address
    /// موقع/عنوان المستودع
    /// </summary>
    [StringLength(200, ErrorMessage = "Location cannot exceed 200 characters")]
    public string? Location { get; set; }

    /// <summary>
    /// Maximum capacity of the warehouse
    /// السعة القصوى للمستودع
    /// </summary>
    [Range(0, double.MaxValue, ErrorMessage = "Max capacity must be a positive number")]
    public decimal MaxCapacity { get; set; }

    /// <summary>
    /// Whether the warehouse is active
    /// ما إذا كان المستودع نشطًا
    /// </summary>
    public bool IsActive { get; set; }
}

/// <summary>
/// Warehouse Transfer DTO
/// DTO نقل المستودع
/// </summary>
public class WarehouseTransferDto
{
    /// <summary>
    /// Source warehouse ID
    /// معرف المستودع المصدر
    /// </summary>
    [Required(ErrorMessage = "Source warehouse is required")]
    public int FromWarehouseId { get; set; }

    /// <summary>
    /// Destination warehouse ID
    /// معرف المستودع الوجهة
    /// </summary>
    [Required(ErrorMessage = "Destination warehouse is required")]
    public int ToWarehouseId { get; set; }

    /// <summary>
    /// Item ID to transfer
    /// معرف الصنف المراد نقله
    /// </summary>
    [Required(ErrorMessage = "Item is required")]
    public int ItemId { get; set; }

    /// <summary>
    /// Quantity to transfer
    /// الكمية المراد نقلها
    /// </summary>
    [Range(0.01, double.MaxValue, ErrorMessage = "Quantity must be greater than zero")]
    public decimal Quantity { get; set; }

    /// <summary>
    /// Transfer notes
    /// ملاحظات النقل
    /// </summary>
    [StringLength(500, ErrorMessage = "Notes cannot exceed 500 characters")]
    public string? Notes { get; set; }

    /// <summary>
    /// Transfer date
    /// تاريخ النقل
    /// </summary>
    public DateTime TransferDate { get; set; } = DateTime.UtcNow;
}

/// <summary>
/// Warehouse Capacity Report DTO
/// DTO تقرير سعة المستودع
/// </summary>
public class WarehouseCapacityReportDto
{
    /// <summary>
    /// Warehouse information
    /// معلومات المستودع
    /// </summary>
    public WarehouseDto Warehouse { get; set; } = null!;

    /// <summary>
    /// Total items count in warehouse
    /// إجمالي عدد الأصناف في المستودع
    /// </summary>
    public int TotalItemsCount { get; set; }

    /// <summary>
    /// Total value of items in warehouse
    /// إجمالي قيمة الأصناف في المستودع
    /// </summary>
    public decimal TotalValue { get; set; }

    /// <summary>
    /// Items by category
    /// الأصناف حسب الفئة
    /// </summary>
    public List<CategorySummaryDto> ItemsByCategory { get; set; } = new();

    /// <summary>
    /// Low stock items in this warehouse
    /// الأصناف منخفضة المخزون في هذا المستودع
    /// </summary>
    public List<LowStockItemDto> LowStockItems { get; set; } = new();
}

/// <summary>
/// Category Summary DTO
/// DTO ملخص الفئة
/// </summary>
public class CategorySummaryDto
{
    /// <summary>
    /// Category name
    /// اسم الفئة
    /// </summary>
    public string CategoryName { get; set; } = string.Empty;

    /// <summary>
    /// Items count in category
    /// عدد الأصناف في الفئة
    /// </summary>
    public int ItemsCount { get; set; }

    /// <summary>
    /// Total quantity in category
    /// إجمالي الكمية في الفئة
    /// </summary>
    public decimal TotalQuantity { get; set; }

    /// <summary>
    /// Total value in category
    /// إجمالي القيمة في الفئة
    /// </summary>
    public decimal TotalValue { get; set; }
}

/// <summary>
/// Low Stock Item DTO
/// DTO الصنف منخفض المخزون
/// </summary>
public class LowStockItemDto
{
    /// <summary>
    /// Item ID
    /// معرف الصنف
    /// </summary>
    public int ItemId { get; set; }

    /// <summary>
    /// Item name
    /// اسم الصنف
    /// </summary>
    public string ItemName { get; set; } = string.Empty;

    /// <summary>
    /// Current quantity
    /// الكمية الحالية
    /// </summary>
    public decimal CurrentQuantity { get; set; }

    /// <summary>
    /// Minimum stock level
    /// الحد الأدنى للمخزون
    /// </summary>
    public decimal MinimumStockLevel { get; set; }

    /// <summary>
    /// Reorder point
    /// نقطة إعادة الطلب
    /// </summary>
    public decimal ReorderPoint { get; set; }
}
