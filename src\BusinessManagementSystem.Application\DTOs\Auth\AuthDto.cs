namespace BusinessManagementSystem.Application.DTOs.Auth;

/// <summary>
/// Login request DTO
/// DTO طلب تسجيل الدخول
/// </summary>
public class LoginDto
{
    /// <summary>
    /// Username or email
    /// اسم المستخدم أو البريد الإلكتروني
    /// </summary>
    public string Username { get; set; } = string.Empty;

    /// <summary>
    /// Password
    /// كلمة المرور
    /// </summary>
    public string Password { get; set; } = string.Empty;
}

/// <summary>
/// Login response DTO
/// DTO استجابة تسجيل الدخول
/// </summary>
public class LoginResponseDto
{
    /// <summary>
    /// JWT access token
    /// رمز الوصول JWT
    /// </summary>
    public string AccessToken { get; set; } = string.Empty;

    /// <summary>
    /// Refresh token
    /// رمز التحديث
    /// </summary>
    public string RefreshToken { get; set; } = string.Empty;

    /// <summary>
    /// Token expiry time
    /// وقت انتهاء الرمز
    /// </summary>
    public DateTime ExpiresAt { get; set; }

    /// <summary>
    /// User information
    /// معلومات المستخدم
    /// </summary>
    public UserInfoDto User { get; set; } = new();
}

/// <summary>
/// User information DTO for authentication
/// DTO معلومات المستخدم للمصادقة
/// </summary>
public class UserInfoDto
{
    /// <summary>
    /// User identifier
    /// معرف المستخدم
    /// </summary>
    public int Id { get; set; }

    /// <summary>
    /// Username
    /// اسم المستخدم
    /// </summary>
    public string Username { get; set; } = string.Empty;

    /// <summary>
    /// Full name
    /// الاسم الكامل
    /// </summary>
    public string FullName { get; set; } = string.Empty;

    /// <summary>
    /// Email address
    /// عنوان البريد الإلكتروني
    /// </summary>
    public string? Email { get; set; }

    /// <summary>
    /// Role name
    /// اسم الدور
    /// </summary>
    public string Role { get; set; } = string.Empty;

    /// <summary>
    /// Company identifier
    /// معرف الشركة
    /// </summary>
    public int CompanyId { get; set; }

    /// <summary>
    /// Company name
    /// اسم الشركة
    /// </summary>
    public string CompanyName { get; set; } = string.Empty;

    /// <summary>
    /// User permissions
    /// صلاحيات المستخدم
    /// </summary>
    public List<PermissionDto> Permissions { get; set; } = new();
}

/// <summary>
/// Permission DTO
/// DTO الصلاحية
/// </summary>
public class PermissionDto
{
    /// <summary>
    /// Page or module name
    /// اسم الصفحة أو الوحدة
    /// </summary>
    public string PageName { get; set; } = string.Empty;

    /// <summary>
    /// Can add permission
    /// صلاحية الإضافة
    /// </summary>
    public bool CanAdd { get; set; }

    /// <summary>
    /// Can edit permission
    /// صلاحية التعديل
    /// </summary>
    public bool CanEdit { get; set; }

    /// <summary>
    /// Can delete permission
    /// صلاحية الحذف
    /// </summary>
    public bool CanDelete { get; set; }

    /// <summary>
    /// Can view permission
    /// صلاحية العرض
    /// </summary>
    public bool CanView { get; set; }
}

/// <summary>
/// Refresh token request DTO
/// DTO طلب تحديث الرمز
/// </summary>
public class RefreshTokenDto
{
    /// <summary>
    /// Refresh token
    /// رمز التحديث
    /// </summary>
    public string RefreshToken { get; set; } = string.Empty;
}

/// <summary>
/// Token response DTO
/// DTO استجابة الرمز
/// </summary>
public class TokenResponseDto
{
    /// <summary>
    /// JWT access token
    /// رمز الوصول JWT
    /// </summary>
    public string AccessToken { get; set; } = string.Empty;

    /// <summary>
    /// Refresh token
    /// رمز التحديث
    /// </summary>
    public string RefreshToken { get; set; } = string.Empty;

    /// <summary>
    /// Token expiry time
    /// وقت انتهاء الرمز
    /// </summary>
    public DateTime ExpiresAt { get; set; }
}
