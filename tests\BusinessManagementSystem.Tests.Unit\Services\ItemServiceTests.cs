using AutoMapper;
using FluentAssertions;
using Microsoft.Extensions.Logging;
using Moq;
using BusinessManagementSystem.Application.DTOs.Item;
using BusinessManagementSystem.Application.Interfaces;
using BusinessManagementSystem.Application.Services.Implementations;
using BusinessManagementSystem.Domain.Entities;
using System.Linq.Expressions;

namespace BusinessManagementSystem.Tests.Unit.Services;

/// <summary>
/// Unit tests for ItemService
/// اختبارات الوحدة لخدمة الأصناف
/// </summary>
public class ItemServiceTests
{
    private readonly Mock<IUnitOfWork> _mockUnitOfWork;
    private readonly Mock<IMapper> _mockMapper;
    private readonly Mock<ILogger<ItemService>> _mockLogger;
    private readonly ItemService _itemService;

    public ItemServiceTests()
    {
        _mockUnitOfWork = new Mock<IUnitOfWork>();
        _mockMapper = new Mock<IMapper>();
        _mockLogger = new Mock<ILogger<ItemService>>();
        _itemService = new ItemService(_mockUnitOfWork.Object, _mockMapper.Object, _mockLogger.Object);
    }

    [Fact]
    public async Task GetAllAsync_ShouldReturnAllItems_WhenItemsExist()
    {
        // Arrange
        var items = new List<Item>
        {
            new Item { Id = 1, Name = "Item 1", CompanyId = 1 },
            new Item { Id = 2, Name = "Item 2", CompanyId = 1 }
        };

        var itemDtos = new List<ItemDto>
        {
            new ItemDto { Id = 1, Name = "Item 1", CompanyId = 1 },
            new ItemDto { Id = 2, Name = "Item 2", CompanyId = 1 }
        };

        _mockUnitOfWork.Setup(x => x.Items.GetAsync(
            It.IsAny<Expression<Func<Item, bool>>>(),
            It.IsAny<Func<IQueryable<Item>, IOrderedQueryable<Item>>>(),
            It.IsAny<string>(),
            It.IsAny<CancellationToken>()))
            .ReturnsAsync(items);

        _mockMapper.Setup(x => x.Map<IEnumerable<ItemDto>>(items))
            .Returns(itemDtos);

        // Act
        var result = await _itemService.GetAllAsync();

        // Assert
        result.Should().NotBeNull();
        result.Should().HaveCount(2);
        result.Should().BeEquivalentTo(itemDtos);
    }

    [Fact]
    public async Task GetByIdAsync_ShouldReturnItem_WhenItemExists()
    {
        // Arrange
        var itemId = 1;
        var item = new Item { Id = itemId, Name = "Test Item", CompanyId = 1 };
        var itemDto = new ItemDto { Id = itemId, Name = "Test Item", CompanyId = 1 };

        _mockUnitOfWork.Setup(x => x.Items.GetByIdAsync(itemId, It.IsAny<CancellationToken>()))
            .ReturnsAsync(item);

        _mockMapper.Setup(x => x.Map<ItemDto>(item))
            .Returns(itemDto);

        // Act
        var result = await _itemService.GetByIdAsync(itemId);

        // Assert
        result.Should().NotBeNull();
        result.Should().BeEquivalentTo(itemDto);
    }

    [Fact]
    public async Task GetByIdAsync_ShouldReturnNull_WhenItemDoesNotExist()
    {
        // Arrange
        var itemId = 999;

        _mockUnitOfWork.Setup(x => x.Items.GetByIdAsync(itemId, It.IsAny<CancellationToken>()))
            .ReturnsAsync((Item?)null);

        // Act
        var result = await _itemService.GetByIdAsync(itemId);

        // Assert
        result.Should().BeNull();
    }

    [Fact]
    public async Task CreateAsync_ShouldCreateItem_WhenValidDataProvided()
    {
        // Arrange
        var createItemDto = new CreateItemDto
        {
            Name = "New Item",
            Description = "Test Description",
            UnitPrice = 100.00m,
            CostPrice = 80.00m,
            CompanyId = 1
        };

        var item = new Item
        {
            Id = 1,
            Name = "New Item",
            Description = "Test Description",
            UnitPrice = 100.00m,
            CostPrice = 80.00m,
            CompanyId = 1
        };

        var itemDto = new ItemDto
        {
            Id = 1,
            Name = "New Item",
            Description = "Test Description",
            UnitPrice = 100.00m,
            CostPrice = 80.00m,
            CompanyId = 1
        };

        var company = new Company { Id = 1, Name = "Test Company" };

        _mockUnitOfWork.Setup(x => x.Companies.GetByIdAsync(1, It.IsAny<CancellationToken>()))
            .ReturnsAsync(company);

        _mockUnitOfWork.Setup(x => x.Items.GetAsync(
            It.IsAny<Expression<Func<Item, bool>>>(),
            It.IsAny<Func<IQueryable<Item>, IOrderedQueryable<Item>>>(),
            It.IsAny<string>(),
            It.IsAny<CancellationToken>()))
            .ReturnsAsync(new List<Item>());

        _mockMapper.Setup(x => x.Map<Item>(createItemDto))
            .Returns(item);

        _mockMapper.Setup(x => x.Map<ItemDto>(item))
            .Returns(itemDto);

        _mockUnitOfWork.Setup(x => x.Items.AddAsync(It.IsAny<Item>(), It.IsAny<CancellationToken>()))
            .Returns(Task.CompletedTask);

        _mockUnitOfWork.Setup(x => x.SaveChangesAsync(It.IsAny<CancellationToken>()))
            .Returns(Task.CompletedTask);

        // Act
        var result = await _itemService.CreateAsync(createItemDto);

        // Assert
        result.Should().NotBeNull();
        result.Should().BeEquivalentTo(itemDto);
        _mockUnitOfWork.Verify(x => x.Items.AddAsync(It.IsAny<Item>(), It.IsAny<CancellationToken>()), Times.Once);
        _mockUnitOfWork.Verify(x => x.SaveChangesAsync(It.IsAny<CancellationToken>()), Times.Once);
    }

    [Fact]
    public async Task CreateAsync_ShouldThrowArgumentException_WhenCompanyDoesNotExist()
    {
        // Arrange
        var createItemDto = new CreateItemDto
        {
            Name = "New Item",
            CompanyId = 999
        };

        _mockUnitOfWork.Setup(x => x.Companies.GetByIdAsync(999, It.IsAny<CancellationToken>()))
            .ReturnsAsync((Company?)null);

        // Act & Assert
        await _itemService.Invoking(x => x.CreateAsync(createItemDto))
            .Should().ThrowAsync<ArgumentException>()
            .WithMessage("Invalid company ID.*");
    }

    [Fact]
    public async Task CreateAsync_ShouldThrowArgumentException_WhenItemNameAlreadyExists()
    {
        // Arrange
        var createItemDto = new CreateItemDto
        {
            Name = "Existing Item",
            CompanyId = 1
        };

        var company = new Company { Id = 1, Name = "Test Company" };
        var existingItems = new List<Item>
        {
            new Item { Id = 1, Name = "Existing Item", CompanyId = 1 }
        };

        _mockUnitOfWork.Setup(x => x.Companies.GetByIdAsync(1, It.IsAny<CancellationToken>()))
            .ReturnsAsync(company);

        _mockUnitOfWork.Setup(x => x.Items.GetAsync(
            It.IsAny<Expression<Func<Item, bool>>>(),
            It.IsAny<Func<IQueryable<Item>, IOrderedQueryable<Item>>>(),
            It.IsAny<string>(),
            It.IsAny<CancellationToken>()))
            .ReturnsAsync(existingItems);

        // Act & Assert
        await _itemService.Invoking(x => x.CreateAsync(createItemDto))
            .Should().ThrowAsync<ArgumentException>()
            .WithMessage("An item with this name already exists in the company.");
    }

    [Fact]
    public async Task UpdateAsync_ShouldUpdateItem_WhenValidDataProvided()
    {
        // Arrange
        var itemId = 1;
        var updateItemDto = new UpdateItemDto
        {
            Name = "Updated Item",
            Description = "Updated Description",
            UnitPrice = 150.00m
        };

        var existingItem = new Item
        {
            Id = itemId,
            Name = "Original Item",
            Description = "Original Description",
            UnitPrice = 100.00m,
            CompanyId = 1
        };

        var updatedItemDto = new ItemDto
        {
            Id = itemId,
            Name = "Updated Item",
            Description = "Updated Description",
            UnitPrice = 150.00m,
            CompanyId = 1
        };

        _mockUnitOfWork.Setup(x => x.Items.GetByIdAsync(itemId, It.IsAny<CancellationToken>()))
            .ReturnsAsync(existingItem);

        _mockUnitOfWork.Setup(x => x.Items.GetAsync(
            It.IsAny<Expression<Func<Item, bool>>>(),
            It.IsAny<Func<IQueryable<Item>, IOrderedQueryable<Item>>>(),
            It.IsAny<string>(),
            It.IsAny<CancellationToken>()))
            .ReturnsAsync(new List<Item>());

        _mockMapper.Setup(x => x.Map(updateItemDto, existingItem))
            .Returns(existingItem);

        _mockMapper.Setup(x => x.Map<ItemDto>(existingItem))
            .Returns(updatedItemDto);

        _mockUnitOfWork.Setup(x => x.Items.Update(existingItem));
        _mockUnitOfWork.Setup(x => x.SaveChangesAsync(It.IsAny<CancellationToken>()))
            .Returns(Task.CompletedTask);

        // Act
        var result = await _itemService.UpdateAsync(itemId, updateItemDto);

        // Assert
        result.Should().NotBeNull();
        result.Should().BeEquivalentTo(updatedItemDto);
        _mockUnitOfWork.Verify(x => x.Items.Update(existingItem), Times.Once);
        _mockUnitOfWork.Verify(x => x.SaveChangesAsync(It.IsAny<CancellationToken>()), Times.Once);
    }

    [Fact]
    public async Task UpdateAsync_ShouldThrowArgumentException_WhenItemDoesNotExist()
    {
        // Arrange
        var itemId = 999;
        var updateItemDto = new UpdateItemDto { Name = "Updated Item" };

        _mockUnitOfWork.Setup(x => x.Items.GetByIdAsync(itemId, It.IsAny<CancellationToken>()))
            .ReturnsAsync((Item?)null);

        // Act & Assert
        await _itemService.Invoking(x => x.UpdateAsync(itemId, updateItemDto))
            .Should().ThrowAsync<ArgumentException>()
            .WithMessage($"Item with ID {itemId} not found.*");
    }

    [Fact]
    public async Task DeleteAsync_ShouldDeleteItem_WhenItemExistsAndHasNoTransactions()
    {
        // Arrange
        var itemId = 1;
        var item = new Item { Id = itemId, Name = "Test Item", CompanyId = 1 };

        _mockUnitOfWork.Setup(x => x.Items.GetByIdAsync(itemId, It.IsAny<CancellationToken>()))
            .ReturnsAsync(item);

        _mockUnitOfWork.Setup(x => x.InventoryTransactions.GetAsync(
            It.IsAny<Expression<Func<InventoryTransaction, bool>>>(),
            It.IsAny<Func<IQueryable<InventoryTransaction>, IOrderedQueryable<InventoryTransaction>>>(),
            It.IsAny<string>(),
            It.IsAny<CancellationToken>()))
            .ReturnsAsync(new List<InventoryTransaction>());

        _mockUnitOfWork.Setup(x => x.InvoiceDetails.GetAsync(
            It.IsAny<Expression<Func<InvoiceDetail, bool>>>(),
            It.IsAny<Func<IQueryable<InvoiceDetail>, IOrderedQueryable<InvoiceDetail>>>(),
            It.IsAny<string>(),
            It.IsAny<CancellationToken>()))
            .ReturnsAsync(new List<InvoiceDetail>());

        _mockUnitOfWork.Setup(x => x.Items.Delete(item));
        _mockUnitOfWork.Setup(x => x.SaveChangesAsync(It.IsAny<CancellationToken>()))
            .Returns(Task.CompletedTask);

        // Act
        var result = await _itemService.DeleteAsync(itemId);

        // Assert
        result.Should().BeTrue();
        _mockUnitOfWork.Verify(x => x.Items.Delete(item), Times.Once);
        _mockUnitOfWork.Verify(x => x.SaveChangesAsync(It.IsAny<CancellationToken>()), Times.Once);
    }

    [Fact]
    public async Task DeleteAsync_ShouldReturnFalse_WhenItemDoesNotExist()
    {
        // Arrange
        var itemId = 999;

        _mockUnitOfWork.Setup(x => x.Items.GetByIdAsync(itemId, It.IsAny<CancellationToken>()))
            .ReturnsAsync((Item?)null);

        // Act
        var result = await _itemService.DeleteAsync(itemId);

        // Assert
        result.Should().BeFalse();
        _mockUnitOfWork.Verify(x => x.Items.Delete(It.IsAny<Item>()), Times.Never);
        _mockUnitOfWork.Verify(x => x.SaveChangesAsync(It.IsAny<CancellationToken>()), Times.Never);
    }

    [Fact]
    public async Task DeleteAsync_ShouldThrowInvalidOperationException_WhenItemHasTransactions()
    {
        // Arrange
        var itemId = 1;
        var item = new Item { Id = itemId, Name = "Test Item", CompanyId = 1 };
        var transactions = new List<InventoryTransaction>
        {
            new InventoryTransaction { Id = 1, ItemId = itemId }
        };

        _mockUnitOfWork.Setup(x => x.Items.GetByIdAsync(itemId, It.IsAny<CancellationToken>()))
            .ReturnsAsync(item);

        _mockUnitOfWork.Setup(x => x.InventoryTransactions.GetAsync(
            It.IsAny<Expression<Func<InventoryTransaction, bool>>>(),
            It.IsAny<Func<IQueryable<InventoryTransaction>, IOrderedQueryable<InventoryTransaction>>>(),
            It.IsAny<string>(),
            It.IsAny<CancellationToken>()))
            .ReturnsAsync(transactions);

        // Act & Assert
        await _itemService.Invoking(x => x.DeleteAsync(itemId))
            .Should().ThrowAsync<InvalidOperationException>()
            .WithMessage("Cannot delete item that has transaction history.");
    }

    [Fact]
    public async Task GetByCompanyAsync_ShouldReturnItemsForCompany()
    {
        // Arrange
        var companyId = 1;
        var items = new List<Item>
        {
            new Item { Id = 1, Name = "Item 1", CompanyId = companyId },
            new Item { Id = 2, Name = "Item 2", CompanyId = companyId }
        };

        var itemDtos = new List<ItemDto>
        {
            new ItemDto { Id = 1, Name = "Item 1", CompanyId = companyId },
            new ItemDto { Id = 2, Name = "Item 2", CompanyId = companyId }
        };

        _mockUnitOfWork.Setup(x => x.Items.GetAsync(
            It.IsAny<Expression<Func<Item, bool>>>(),
            It.IsAny<Func<IQueryable<Item>, IOrderedQueryable<Item>>>(),
            It.IsAny<string>(),
            It.IsAny<CancellationToken>()))
            .ReturnsAsync(items);

        _mockMapper.Setup(x => x.Map<IEnumerable<ItemDto>>(items))
            .Returns(itemDtos);

        // Act
        var result = await _itemService.GetByCompanyAsync(companyId);

        // Assert
        result.Should().NotBeNull();
        result.Should().HaveCount(2);
        result.Should().OnlyContain(x => x.CompanyId == companyId);
    }

    [Fact]
    public async Task GenerateBarcodeAsync_ShouldGenerateValidBarcode()
    {
        // Arrange
        var itemId = 1;
        var item = new Item { Id = itemId, Name = "Test Item", CompanyId = 1 };

        _mockUnitOfWork.Setup(x => x.Items.GetByIdAsync(itemId, It.IsAny<CancellationToken>()))
            .ReturnsAsync(item);

        _mockUnitOfWork.Setup(x => x.Items.Update(item));
        _mockUnitOfWork.Setup(x => x.SaveChangesAsync(It.IsAny<CancellationToken>()))
            .Returns(Task.CompletedTask);

        // Act
        var result = await _itemService.GenerateBarcodeAsync(itemId);

        // Assert
        result.Should().NotBeNullOrEmpty();
        result.Should().HaveLength(13); // EAN-13 format
        result.Should().MatchRegex(@"^\d{13}$"); // Should be all digits
        item.Barcode.Should().Be(result);
        _mockUnitOfWork.Verify(x => x.Items.Update(item), Times.Once);
        _mockUnitOfWork.Verify(x => x.SaveChangesAsync(It.IsAny<CancellationToken>()), Times.Once);
    }

    [Fact]
    public async Task GenerateBarcodeAsync_ShouldThrowArgumentException_WhenItemDoesNotExist()
    {
        // Arrange
        var itemId = 999;

        _mockUnitOfWork.Setup(x => x.Items.GetByIdAsync(itemId, It.IsAny<CancellationToken>()))
            .ReturnsAsync((Item?)null);

        // Act & Assert
        await _itemService.Invoking(x => x.GenerateBarcodeAsync(itemId))
            .Should().ThrowAsync<ArgumentException>()
            .WithMessage($"Item with ID {itemId} not found.*");
    }
}
