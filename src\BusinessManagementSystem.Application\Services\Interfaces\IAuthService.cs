using BusinessManagementSystem.Application.DTOs.Auth;

namespace BusinessManagementSystem.Application.Services.Interfaces;

/// <summary>
/// Authentication service interface for login and token management
/// واجهة خدمة المصادقة لتسجيل الدخول وإدارة الرموز
/// </summary>
public interface IAuthService
{
    /// <summary>
    /// Authenticate user and generate tokens
    /// مصادقة المستخدم وتوليد الرموز
    /// </summary>
    Task<LoginResponseDto?> LoginAsync(LoginDto loginDto, CancellationToken cancellationToken = default);

    /// <summary>
    /// Refresh access token using refresh token
    /// تحديث رمز الوصول باستخدام رمز التحديث
    /// </summary>
    Task<TokenResponseDto?> RefreshTokenAsync(RefreshTokenDto refreshTokenDto, CancellationToken cancellationToken = default);

    /// <summary>
    /// Logout user and invalidate refresh token
    /// تسجيل خروج المستخدم وإبطال رمز التحديث
    /// </summary>
    Task<bool> LogoutAsync(string refreshToken, CancellationToken cancellationToken = default);

    /// <summary>
    /// Generate JWT access token for user
    /// توليد رمز وصول JWT للمستخدم
    /// </summary>
    Task<string> GenerateAccessTokenAsync(int userId, CancellationToken cancellationToken = default);

    /// <summary>
    /// Generate refresh token for user
    /// توليد رمز التحديث للمستخدم
    /// </summary>
    Task<string> GenerateRefreshTokenAsync(int userId, CancellationToken cancellationToken = default);

    /// <summary>
    /// Validate refresh token
    /// التحقق من صحة رمز التحديث
    /// </summary>
    Task<bool> ValidateRefreshTokenAsync(string refreshToken, CancellationToken cancellationToken = default);

    /// <summary>
    /// Get user information from token
    /// الحصول على معلومات المستخدم من الرمز
    /// </summary>
    Task<UserInfoDto?> GetUserInfoFromTokenAsync(string token, CancellationToken cancellationToken = default);
}
