using BusinessManagementSystem.Application.DTOs.Common;
using System.ComponentModel.DataAnnotations;

namespace BusinessManagementSystem.Application.DTOs.InventoryTransaction;

/// <summary>
/// Inventory Transaction DTO for data transfer
/// DTO معاملة المخزون لنقل البيانات
/// </summary>
public class InventoryTransactionDto : BaseDto
{
    /// <summary>
    /// Transaction date
    /// تاريخ المعاملة
    /// </summary>
    public DateTime TransactionDate { get; set; }

    /// <summary>
    /// Transaction type (In, Out, Transfer, Adjustment)
    /// نوع المعاملة (دخول، خروج، نقل، تعديل)
    /// </summary>
    public string TransactionType { get; set; } = string.Empty;

    /// <summary>
    /// Quantity involved in transaction
    /// الكمية المشاركة في المعاملة
    /// </summary>
    public decimal Quantity { get; set; }

    /// <summary>
    /// Unit cost at time of transaction
    /// تكلفة الوحدة وقت المعاملة
    /// </summary>
    public decimal UnitCost { get; set; }

    /// <summary>
    /// Total cost (Quantity * UnitCost)
    /// إجمالي التكلفة (الكمية * تكلفة الوحدة)
    /// </summary>
    public decimal TotalCost { get; set; }

    /// <summary>
    /// Transaction reference number
    /// الرقم المرجعي للمعاملة
    /// </summary>
    public string? ReferenceNumber { get; set; }

    /// <summary>
    /// Transaction notes
    /// ملاحظات المعاملة
    /// </summary>
    public string? Notes { get; set; }

    /// <summary>
    /// Item ID
    /// معرف الصنف
    /// </summary>
    public int ItemId { get; set; }

    /// <summary>
    /// Item name
    /// اسم الصنف
    /// </summary>
    public string ItemName { get; set; } = string.Empty;

    /// <summary>
    /// Item barcode
    /// باركود الصنف
    /// </summary>
    public string? ItemBarcode { get; set; }

    /// <summary>
    /// Warehouse ID
    /// معرف المستودع
    /// </summary>
    public int WarehouseId { get; set; }

    /// <summary>
    /// Warehouse name
    /// اسم المستودع
    /// </summary>
    public string WarehouseName { get; set; } = string.Empty;

    /// <summary>
    /// Company ID
    /// معرف الشركة
    /// </summary>
    public int CompanyId { get; set; }

    /// <summary>
    /// Company name
    /// اسم الشركة
    /// </summary>
    public string CompanyName { get; set; } = string.Empty;

    /// <summary>
    /// Related invoice ID (if applicable)
    /// معرف الفاتورة المرتبطة (إن وجدت)
    /// </summary>
    public int? InvoiceId { get; set; }

    /// <summary>
    /// Related invoice number
    /// رقم الفاتورة المرتبطة
    /// </summary>
    public string? InvoiceNumber { get; set; }
}

/// <summary>
/// Create Inventory Transaction DTO
/// DTO إنشاء معاملة المخزون
/// </summary>
public class CreateInventoryTransactionDto
{
    /// <summary>
    /// Transaction date
    /// تاريخ المعاملة
    /// </summary>
    public DateTime TransactionDate { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// Transaction type (In, Out, Transfer, Adjustment)
    /// نوع المعاملة (دخول، خروج، نقل، تعديل)
    /// </summary>
    [Required(ErrorMessage = "Transaction type is required")]
    [StringLength(20, ErrorMessage = "Transaction type cannot exceed 20 characters")]
    public string TransactionType { get; set; } = string.Empty;

    /// <summary>
    /// Quantity involved in transaction
    /// الكمية المشاركة في المعاملة
    /// </summary>
    [Range(0.01, double.MaxValue, ErrorMessage = "Quantity must be greater than zero")]
    public decimal Quantity { get; set; }

    /// <summary>
    /// Unit cost at time of transaction
    /// تكلفة الوحدة وقت المعاملة
    /// </summary>
    [Range(0, double.MaxValue, ErrorMessage = "Unit cost must be a positive number")]
    public decimal UnitCost { get; set; }

    /// <summary>
    /// Transaction reference number
    /// الرقم المرجعي للمعاملة
    /// </summary>
    [StringLength(50, ErrorMessage = "Reference number cannot exceed 50 characters")]
    public string? ReferenceNumber { get; set; }

    /// <summary>
    /// Transaction notes
    /// ملاحظات المعاملة
    /// </summary>
    [StringLength(500, ErrorMessage = "Notes cannot exceed 500 characters")]
    public string? Notes { get; set; }

    /// <summary>
    /// Item ID
    /// معرف الصنف
    /// </summary>
    [Required(ErrorMessage = "Item is required")]
    public int ItemId { get; set; }

    /// <summary>
    /// Warehouse ID
    /// معرف المستودع
    /// </summary>
    [Required(ErrorMessage = "Warehouse is required")]
    public int WarehouseId { get; set; }

    /// <summary>
    /// Company ID
    /// معرف الشركة
    /// </summary>
    public int CompanyId { get; set; }

    /// <summary>
    /// Related invoice ID (if applicable)
    /// معرف الفاتورة المرتبطة (إن وجدت)
    /// </summary>
    public int? InvoiceId { get; set; }
}

/// <summary>
/// Update Inventory Transaction DTO
/// DTO تحديث معاملة المخزون
/// </summary>
public class UpdateInventoryTransactionDto
{
    /// <summary>
    /// Transaction date
    /// تاريخ المعاملة
    /// </summary>
    public DateTime TransactionDate { get; set; }

    /// <summary>
    /// Transaction type (In, Out, Transfer, Adjustment)
    /// نوع المعاملة (دخول، خروج، نقل، تعديل)
    /// </summary>
    [Required(ErrorMessage = "Transaction type is required")]
    [StringLength(20, ErrorMessage = "Transaction type cannot exceed 20 characters")]
    public string TransactionType { get; set; } = string.Empty;

    /// <summary>
    /// Quantity involved in transaction
    /// الكمية المشاركة في المعاملة
    /// </summary>
    [Range(0.01, double.MaxValue, ErrorMessage = "Quantity must be greater than zero")]
    public decimal Quantity { get; set; }

    /// <summary>
    /// Unit cost at time of transaction
    /// تكلفة الوحدة وقت المعاملة
    /// </summary>
    [Range(0, double.MaxValue, ErrorMessage = "Unit cost must be a positive number")]
    public decimal UnitCost { get; set; }

    /// <summary>
    /// Transaction reference number
    /// الرقم المرجعي للمعاملة
    /// </summary>
    [StringLength(50, ErrorMessage = "Reference number cannot exceed 50 characters")]
    public string? ReferenceNumber { get; set; }

    /// <summary>
    /// Transaction notes
    /// ملاحظات المعاملة
    /// </summary>
    [StringLength(500, ErrorMessage = "Notes cannot exceed 500 characters")]
    public string? Notes { get; set; }
}

/// <summary>
/// Inventory Movement Report DTO
/// DTO تقرير حركة المخزون
/// </summary>
public class InventoryMovementReportDto
{
    /// <summary>
    /// Report period start date
    /// تاريخ بداية فترة التقرير
    /// </summary>
    public DateTime StartDate { get; set; }

    /// <summary>
    /// Report period end date
    /// تاريخ نهاية فترة التقرير
    /// </summary>
    public DateTime EndDate { get; set; }

    /// <summary>
    /// Total transactions count
    /// إجمالي عدد المعاملات
    /// </summary>
    public int TotalTransactions { get; set; }

    /// <summary>
    /// Total quantity in
    /// إجمالي الكمية الداخلة
    /// </summary>
    public decimal TotalQuantityIn { get; set; }

    /// <summary>
    /// Total quantity out
    /// إجمالي الكمية الخارجة
    /// </summary>
    public decimal TotalQuantityOut { get; set; }

    /// <summary>
    /// Net quantity movement
    /// صافي حركة الكمية
    /// </summary>
    public decimal NetQuantityMovement { get; set; }

    /// <summary>
    /// Total value in
    /// إجمالي القيمة الداخلة
    /// </summary>
    public decimal TotalValueIn { get; set; }

    /// <summary>
    /// Total value out
    /// إجمالي القيمة الخارجة
    /// </summary>
    public decimal TotalValueOut { get; set; }

    /// <summary>
    /// Net value movement
    /// صافي حركة القيمة
    /// </summary>
    public decimal NetValueMovement { get; set; }

    /// <summary>
    /// Transactions by type
    /// المعاملات حسب النوع
    /// </summary>
    public List<TransactionTypeSummaryDto> TransactionsByType { get; set; } = new();

    /// <summary>
    /// Top items by movement
    /// أهم الأصناف حسب الحركة
    /// </summary>
    public List<ItemMovementSummaryDto> TopItemsByMovement { get; set; } = new();
}

/// <summary>
/// Transaction Type Summary DTO
/// DTO ملخص نوع المعاملة
/// </summary>
public class TransactionTypeSummaryDto
{
    /// <summary>
    /// Transaction type
    /// نوع المعاملة
    /// </summary>
    public string TransactionType { get; set; } = string.Empty;

    /// <summary>
    /// Count of transactions
    /// عدد المعاملات
    /// </summary>
    public int Count { get; set; }

    /// <summary>
    /// Total quantity
    /// إجمالي الكمية
    /// </summary>
    public decimal TotalQuantity { get; set; }

    /// <summary>
    /// Total value
    /// إجمالي القيمة
    /// </summary>
    public decimal TotalValue { get; set; }
}

/// <summary>
/// Item Movement Summary DTO
/// DTO ملخص حركة الصنف
/// </summary>
public class ItemMovementSummaryDto
{
    /// <summary>
    /// Item ID
    /// معرف الصنف
    /// </summary>
    public int ItemId { get; set; }

    /// <summary>
    /// Item name
    /// اسم الصنف
    /// </summary>
    public string ItemName { get; set; } = string.Empty;

    /// <summary>
    /// Total transactions
    /// إجمالي المعاملات
    /// </summary>
    public int TotalTransactions { get; set; }

    /// <summary>
    /// Total quantity moved
    /// إجمالي الكمية المنقولة
    /// </summary>
    public decimal TotalQuantityMoved { get; set; }

    /// <summary>
    /// Total value moved
    /// إجمالي القيمة المنقولة
    /// </summary>
    public decimal TotalValueMoved { get; set; }
}
