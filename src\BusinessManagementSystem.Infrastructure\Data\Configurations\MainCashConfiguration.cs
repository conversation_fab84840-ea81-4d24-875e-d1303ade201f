using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using BusinessManagementSystem.Domain.Entities;

namespace BusinessManagementSystem.Infrastructure.Data.Configurations;

/// <summary>
/// Entity configuration for MainCash
/// تكوين الكيان للخزينة الرئيسية
/// </summary>
public class MainCashConfiguration : IEntityTypeConfiguration<MainCash>
{
    public void Configure(EntityTypeBuilder<MainCash> builder)
    {
        builder.ToTable("MainCashes");

        builder.HasKey(mc => mc.Id);

        builder.Property(mc => mc.Balance)
            .HasColumnType("decimal(18,2)")
            .HasDefaultValue(0);

        builder.Property(mc => mc.Currency)
            .HasMaxLength(3)
            .HasDefaultValue("USD");

        builder.Property(mc => mc.LastUpdated)
            .IsRequired();

        // Indexes
        builder.HasIndex(mc => mc.CompanyId)
            .IsUnique();

        // Relationships
        builder.HasOne(mc => mc.Company)
            .WithOne(c => c.MainCash)
            .HasForeignKey<MainCash>(mc => mc.CompanyId)
            .OnDelete(DeleteBehavior.Cascade);

        builder.HasMany(mc => mc.FinancialTransactions)
            .WithOne(ft => ft.MainCash)
            .HasForeignKey(ft => ft.MainCashId)
            .OnDelete(DeleteBehavior.Restrict);
    }
}
