using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using BusinessManagementSystem.Application.DTOs.Person;
using BusinessManagementSystem.Application.Services.Interfaces;

namespace BusinessManagementSystem.API.Controllers;

/// <summary>
/// Persons controller for customer/supplier management operations
/// وحدة تحكم الأشخاص لعمليات إدارة العملاء/الموردين
/// </summary>
[Authorize]
public class PersonsController : BaseApiController
{
    private readonly IPersonService _personService;
    private readonly ILogger<PersonsController> _logger;

    public PersonsController(IPersonService personService, ILogger<PersonsController> logger)
    {
        _personService = personService;
        _logger = logger;
    }

    /// <summary>
    /// Get all persons with pagination and filtering
    /// الحصول على جميع الأشخاص مع التصفح والتصفية
    /// </summary>
    [HttpGet]
    public async Task<IActionResult> GetAll(
        [FromQuery] int page = 1, 
        [FromQuery] int pageSize = 10,
        [FromQuery] string? personType = null,
        [FromQuery] string? search = null,
        [FromQuery] bool? isActive = null,
        CancellationToken cancellationToken = default)
    {
        try
        {
            if (!HasPermission("Persons", "View"))
            {
                return CreateErrorResponse("You don't have permission to view persons.", 403);
            }

            var companyId = GetCurrentCompanyId();
            IEnumerable<PersonDto> persons;

            if (!string.IsNullOrEmpty(search))
            {
                persons = await _personService.SearchAsync(companyId, search, cancellationToken);
            }
            else if (!string.IsNullOrEmpty(personType))
            {
                persons = await _personService.GetByTypeAsync(companyId, personType, cancellationToken);
            }
            else
            {
                persons = await _personService.GetByCompanyAsync(companyId, cancellationToken);
            }

            // Apply active filter
            if (isActive.HasValue)
            {
                persons = persons.Where(p => p.IsActive == isActive.Value);
            }

            // Apply pagination
            var totalCount = persons.Count();
            var pagedPersons = persons
                .Skip((page - 1) * pageSize)
                .Take(pageSize)
                .ToList();

            var result = new
            {
                Items = pagedPersons,
                TotalCount = totalCount,
                Page = page,
                PageSize = pageSize,
                TotalPages = (int)Math.Ceiling((double)totalCount / pageSize)
            };

            return CreateResponse(result, "Persons retrieved successfully.");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving persons");
            return CreateErrorResponse("An error occurred while retrieving persons.", 500);
        }
    }

    /// <summary>
    /// Get person by ID
    /// الحصول على الشخص بواسطة المعرف
    /// </summary>
    [HttpGet("{id}")]
    public async Task<IActionResult> GetById(int id, CancellationToken cancellationToken = default)
    {
        try
        {
            if (!HasPermission("Persons", "View"))
            {
                return CreateErrorResponse("You don't have permission to view persons.", 403);
            }

            var person = await _personService.GetByIdAsync(id, cancellationToken);
            if (person == null)
            {
                return CreateErrorResponse("Person not found.", 404);
            }

            // Check if user can access this person (same company)
            var companyId = GetCurrentCompanyId();
            if (person.CompanyId != companyId)
            {
                return CreateErrorResponse("You don't have permission to view this person.", 403);
            }

            return CreateResponse(person, "Person retrieved successfully.");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving person with ID: {PersonId}", id);
            return CreateErrorResponse("An error occurred while retrieving the person.", 500);
        }
    }

    /// <summary>
    /// Get customers
    /// الحصول على العملاء
    /// </summary>
    [HttpGet("customers")]
    public async Task<IActionResult> GetCustomers(CancellationToken cancellationToken = default)
    {
        try
        {
            if (!HasPermission("Persons", "View"))
            {
                return CreateErrorResponse("You don't have permission to view customers.", 403);
            }

            var companyId = GetCurrentCompanyId();
            var customers = await _personService.GetCustomersAsync(companyId, cancellationToken);
            
            return CreateResponse(customers, "Customers retrieved successfully.");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving customers");
            return CreateErrorResponse("An error occurred while retrieving customers.", 500);
        }
    }

    /// <summary>
    /// Get suppliers
    /// الحصول على الموردين
    /// </summary>
    [HttpGet("suppliers")]
    public async Task<IActionResult> GetSuppliers(CancellationToken cancellationToken = default)
    {
        try
        {
            if (!HasPermission("Persons", "View"))
            {
                return CreateErrorResponse("You don't have permission to view suppliers.", 403);
            }

            var companyId = GetCurrentCompanyId();
            var suppliers = await _personService.GetSuppliersAsync(companyId, cancellationToken);
            
            return CreateResponse(suppliers, "Suppliers retrieved successfully.");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving suppliers");
            return CreateErrorResponse("An error occurred while retrieving suppliers.", 500);
        }
    }

    /// <summary>
    /// Create new person
    /// إنشاء شخص جديد
    /// </summary>
    [HttpPost]
    public async Task<IActionResult> Create([FromBody] CreatePersonDto createPersonDto, CancellationToken cancellationToken = default)
    {
        try
        {
            if (!HasPermission("Persons", "Add"))
            {
                return CreateErrorResponse("You don't have permission to create persons.", 403);
            }

            if (!ModelState.IsValid)
            {
                return CreateErrorResponse("Invalid person data.", 400);
            }

            // Ensure the person is created for the current user's company
            createPersonDto.CompanyId = GetCurrentCompanyId();

            var person = await _personService.CreateAsync(createPersonDto, cancellationToken);
            
            _logger.LogInformation("Person created successfully with ID: {PersonId} by user: {UserId}", 
                person.Id, GetCurrentUserId());
            
            return CreatedAtAction(nameof(GetById), new { id = person.Id }, 
                CreateResponse(person, "Person created successfully."));
        }
        catch (ArgumentException ex)
        {
            _logger.LogWarning(ex, "Validation error creating person");
            return CreateErrorResponse(ex.Message, 400);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating person");
            return CreateErrorResponse("An error occurred while creating the person.", 500);
        }
    }

    /// <summary>
    /// Update existing person
    /// تحديث شخص موجود
    /// </summary>
    [HttpPut("{id}")]
    public async Task<IActionResult> Update(int id, [FromBody] UpdatePersonDto updatePersonDto, CancellationToken cancellationToken = default)
    {
        try
        {
            if (!HasPermission("Persons", "Edit"))
            {
                return CreateErrorResponse("You don't have permission to edit persons.", 403);
            }

            if (!ModelState.IsValid)
            {
                return CreateErrorResponse("Invalid person data.", 400);
            }

            var person = await _personService.UpdateAsync(id, updatePersonDto, cancellationToken);
            
            _logger.LogInformation("Person updated successfully with ID: {PersonId} by user: {UserId}", 
                id, GetCurrentUserId());
            
            return CreateResponse(person, "Person updated successfully.");
        }
        catch (ArgumentException ex)
        {
            _logger.LogWarning(ex, "Validation error updating person with ID: {PersonId}", id);
            return CreateErrorResponse(ex.Message, 400);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating person with ID: {PersonId}", id);
            return CreateErrorResponse("An error occurred while updating the person.", 500);
        }
    }

    /// <summary>
    /// Set person active status
    /// تعيين حالة تفعيل الشخص
    /// </summary>
    [HttpPatch("{id}/status")]
    public async Task<IActionResult> SetActiveStatus(int id, [FromBody] bool isActive, CancellationToken cancellationToken = default)
    {
        try
        {
            if (!HasPermission("Persons", "Edit"))
            {
                return CreateErrorResponse("You don't have permission to change person status.", 403);
            }

            var result = await _personService.SetActiveStatusAsync(id, isActive, cancellationToken);
            if (!result)
            {
                return CreateErrorResponse("Person not found.", 404);
            }

            _logger.LogInformation("Person status changed successfully for ID: {PersonId} to {Status} by user: {UserId}", 
                id, isActive ? "Active" : "Inactive", GetCurrentUserId());
            
            return CreateResponse<object?>(null, $"Person {(isActive ? "activated" : "deactivated")} successfully.");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error changing status for person ID: {PersonId}", id);
            return CreateErrorResponse("An error occurred while changing person status.", 500);
        }
    }

    /// <summary>
    /// Delete person
    /// حذف الشخص
    /// </summary>
    [HttpDelete("{id}")]
    public async Task<IActionResult> Delete(int id, CancellationToken cancellationToken = default)
    {
        try
        {
            if (!HasPermission("Persons", "Delete"))
            {
                return CreateErrorResponse("You don't have permission to delete persons.", 403);
            }

            var result = await _personService.DeleteAsync(id, cancellationToken);
            if (!result)
            {
                return CreateErrorResponse("Person not found.", 404);
            }

            _logger.LogInformation("Person deleted successfully with ID: {PersonId} by user: {UserId}", 
                id, GetCurrentUserId());
            
            return CreateResponse<object?>(null, "Person deleted successfully.");
        }
        catch (InvalidOperationException ex)
        {
            _logger.LogWarning(ex, "Cannot delete person with ID: {PersonId}", id);
            return CreateErrorResponse(ex.Message, 400);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deleting person with ID: {PersonId}", id);
            return CreateErrorResponse("An error occurred while deleting the person.", 500);
        }
    }

    /// <summary>
    /// Get person balance
    /// الحصول على رصيد الشخص
    /// </summary>
    [HttpGet("{id}/balance")]
    public async Task<IActionResult> GetBalance(int id, CancellationToken cancellationToken = default)
    {
        try
        {
            if (!HasPermission("Persons", "View"))
            {
                return CreateErrorResponse("You don't have permission to view person balance.", 403);
            }

            var balance = await _personService.GetPersonBalanceAsync(id, cancellationToken);
            return CreateResponse(balance, "Person balance retrieved successfully.");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving balance for person ID: {PersonId}", id);
            return CreateErrorResponse("An error occurred while retrieving person balance.", 500);
        }
    }

    /// <summary>
    /// Get person transactions
    /// الحصول على معاملات الشخص
    /// </summary>
    [HttpGet("{id}/transactions")]
    public async Task<IActionResult> GetTransactions(int id, CancellationToken cancellationToken = default)
    {
        try
        {
            if (!HasPermission("Persons", "View"))
            {
                return CreateErrorResponse("You don't have permission to view person transactions.", 403);
            }

            var transactions = await _personService.GetPersonTransactionsAsync(id, cancellationToken);
            return CreateResponse(transactions, "Person transactions retrieved successfully.");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving transactions for person ID: {PersonId}", id);
            return CreateErrorResponse("An error occurred while retrieving person transactions.", 500);
        }
    }

    /// <summary>
    /// Get person invoices
    /// الحصول على فواتير الشخص
    /// </summary>
    [HttpGet("{id}/invoices")]
    public async Task<IActionResult> GetInvoices(int id, CancellationToken cancellationToken = default)
    {
        try
        {
            if (!HasPermission("Persons", "View"))
            {
                return CreateErrorResponse("You don't have permission to view person invoices.", 403);
            }

            var invoices = await _personService.GetPersonInvoicesAsync(id, cancellationToken);
            return CreateResponse(invoices, "Person invoices retrieved successfully.");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving invoices for person ID: {PersonId}", id);
            return CreateErrorResponse("An error occurred while retrieving person invoices.", 500);
        }
    }

    /// <summary>
    /// Get person statistics
    /// الحصول على إحصائيات الشخص
    /// </summary>
    [HttpGet("{id}/statistics")]
    public async Task<IActionResult> GetStatistics(int id, CancellationToken cancellationToken = default)
    {
        try
        {
            if (!HasPermission("Persons", "View"))
            {
                return CreateErrorResponse("You don't have permission to view person statistics.", 403);
            }

            var statistics = await _personService.GetPersonStatisticsAsync(id, cancellationToken);
            return CreateResponse(statistics, "Person statistics retrieved successfully.");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving statistics for person ID: {PersonId}", id);
            return CreateErrorResponse("An error occurred while retrieving person statistics.", 500);
        }
    }
}
