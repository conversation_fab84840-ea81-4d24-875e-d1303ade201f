using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using BusinessManagementSystem.Application.DTOs.Partner;
using BusinessManagementSystem.Application.Services.Interfaces;

namespace BusinessManagementSystem.API.Controllers;

/// <summary>
/// Partners controller for business partner management operations
/// وحدة تحكم الشركاء لعمليات إدارة شركاء الأعمال
/// </summary>
[Authorize]
public class PartnersController : BaseApiController
{
    private readonly IPartnerService _partnerService;
    private readonly ILogger<PartnersController> _logger;

    public PartnersController(IPartnerService partnerService, ILogger<PartnersController> logger)
    {
        _partnerService = partnerService;
        _logger = logger;
    }

    /// <summary>
    /// Get all partners with pagination
    /// الحصول على جميع الشركاء مع التصفح
    /// </summary>
    [HttpGet]
    public async Task<IActionResult> GetAll(
        [FromQuery] int page = 1, 
        [FromQuery] int pageSize = 10,
        [FromQuery] bool? isActive = null,
        CancellationToken cancellationToken = default)
    {
        try
        {
            if (!HasPermission("Partners", "View"))
            {
                return CreateErrorResponse("You don't have permission to view partners.", 403);
            }

            var companyId = GetCurrentCompanyId();
            IEnumerable<PartnerDto> partners;

            if (isActive.HasValue)
            {
                partners = await _partnerService.GetActiveByCompanyAsync(companyId, cancellationToken);
                if (!isActive.Value)
                {
                    var allPartners = await _partnerService.GetByCompanyAsync(companyId, cancellationToken);
                    partners = allPartners.Where(p => !p.IsActive);
                }
            }
            else
            {
                partners = await _partnerService.GetByCompanyAsync(companyId, cancellationToken);
            }

            // Apply pagination
            var totalCount = partners.Count();
            var pagedPartners = partners
                .Skip((page - 1) * pageSize)
                .Take(pageSize)
                .ToList();

            var result = new
            {
                Items = pagedPartners,
                TotalCount = totalCount,
                Page = page,
                PageSize = pageSize,
                TotalPages = (int)Math.Ceiling((double)totalCount / pageSize)
            };

            return CreateResponse(result, "Partners retrieved successfully.");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving partners");
            return CreateErrorResponse("An error occurred while retrieving partners.", 500);
        }
    }

    /// <summary>
    /// Get partner by ID
    /// الحصول على الشريك بواسطة المعرف
    /// </summary>
    [HttpGet("{id}")]
    public async Task<IActionResult> GetById(int id, CancellationToken cancellationToken = default)
    {
        try
        {
            if (!HasPermission("Partners", "View"))
            {
                return CreateErrorResponse("You don't have permission to view partners.", 403);
            }

            var partner = await _partnerService.GetByIdAsync(id, cancellationToken);
            if (partner == null)
            {
                return CreateErrorResponse("Partner not found.", 404);
            }

            // Check if user can access this partner (same company)
            var companyId = GetCurrentCompanyId();
            if (partner.CompanyId != companyId)
            {
                return CreateErrorResponse("You don't have permission to view this partner.", 403);
            }

            return CreateResponse(partner, "Partner retrieved successfully.");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving partner with ID: {PartnerId}", id);
            return CreateErrorResponse("An error occurred while retrieving the partner.", 500);
        }
    }

    /// <summary>
    /// Create new partner
    /// إنشاء شريك جديد
    /// </summary>
    [HttpPost]
    public async Task<IActionResult> Create([FromBody] CreatePartnerDto createPartnerDto, CancellationToken cancellationToken = default)
    {
        try
        {
            if (!HasPermission("Partners", "Add"))
            {
                return CreateErrorResponse("You don't have permission to create partners.", 403);
            }

            if (!ModelState.IsValid)
            {
                return CreateErrorResponse("Invalid partner data.", 400);
            }

            // Ensure the partner is created for the current user's company
            createPartnerDto.CompanyId = GetCurrentCompanyId();

            var partner = await _partnerService.CreateAsync(createPartnerDto, cancellationToken);
            
            _logger.LogInformation("Partner created successfully with ID: {PartnerId} by user: {UserId}", 
                partner.Id, GetCurrentUserId());
            
            return CreatedAtAction(nameof(GetById), new { id = partner.Id }, 
                CreateResponse(partner, "Partner created successfully."));
        }
        catch (ArgumentException ex)
        {
            _logger.LogWarning(ex, "Validation error creating partner");
            return CreateErrorResponse(ex.Message, 400);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating partner");
            return CreateErrorResponse("An error occurred while creating the partner.", 500);
        }
    }

    /// <summary>
    /// Update existing partner
    /// تحديث شريك موجود
    /// </summary>
    [HttpPut("{id}")]
    public async Task<IActionResult> Update(int id, [FromBody] UpdatePartnerDto updatePartnerDto, CancellationToken cancellationToken = default)
    {
        try
        {
            if (!HasPermission("Partners", "Edit"))
            {
                return CreateErrorResponse("You don't have permission to edit partners.", 403);
            }

            if (!ModelState.IsValid)
            {
                return CreateErrorResponse("Invalid partner data.", 400);
            }

            var partner = await _partnerService.UpdateAsync(id, updatePartnerDto, cancellationToken);
            
            _logger.LogInformation("Partner updated successfully with ID: {PartnerId} by user: {UserId}", 
                id, GetCurrentUserId());
            
            return CreateResponse(partner, "Partner updated successfully.");
        }
        catch (ArgumentException ex)
        {
            _logger.LogWarning(ex, "Validation error updating partner with ID: {PartnerId}", id);
            return CreateErrorResponse(ex.Message, 400);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating partner with ID: {PartnerId}", id);
            return CreateErrorResponse("An error occurred while updating the partner.", 500);
        }
    }

    /// <summary>
    /// Add capital contribution
    /// إضافة مساهمة رأس المال
    /// </summary>
    [HttpPost("{id}/capital-contribution")]
    public async Task<IActionResult> AddCapitalContribution(int id, [FromBody] CapitalContributionDto contributionDto, CancellationToken cancellationToken = default)
    {
        try
        {
            if (!HasPermission("Partners", "Edit"))
            {
                return CreateErrorResponse("You don't have permission to manage partner capital.", 403);
            }

            if (!ModelState.IsValid)
            {
                return CreateErrorResponse("Invalid contribution data.", 400);
            }

            var result = await _partnerService.AddCapitalContributionAsync(id, contributionDto, cancellationToken);
            
            _logger.LogInformation("Capital contribution added for partner ID: {PartnerId} by user: {UserId}", 
                id, GetCurrentUserId());
            
            return CreateResponse(result, "Capital contribution added successfully.");
        }
        catch (ArgumentException ex)
        {
            _logger.LogWarning(ex, "Validation error adding capital contribution for partner ID: {PartnerId}", id);
            return CreateErrorResponse(ex.Message, 400);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error adding capital contribution for partner ID: {PartnerId}", id);
            return CreateErrorResponse("An error occurred while adding capital contribution.", 500);
        }
    }

    /// <summary>
    /// Process capital withdrawal
    /// معالجة سحب رأس المال
    /// </summary>
    [HttpPost("{id}/capital-withdrawal")]
    public async Task<IActionResult> ProcessCapitalWithdrawal(int id, [FromBody] CapitalWithdrawalDto withdrawalDto, CancellationToken cancellationToken = default)
    {
        try
        {
            if (!HasPermission("Partners", "Edit"))
            {
                return CreateErrorResponse("You don't have permission to manage partner capital.", 403);
            }

            if (!ModelState.IsValid)
            {
                return CreateErrorResponse("Invalid withdrawal data.", 400);
            }

            var result = await _partnerService.ProcessCapitalWithdrawalAsync(id, withdrawalDto, cancellationToken);
            
            _logger.LogInformation("Capital withdrawal processed for partner ID: {PartnerId} by user: {UserId}", 
                id, GetCurrentUserId());
            
            return CreateResponse(result, "Capital withdrawal processed successfully.");
        }
        catch (ArgumentException ex)
        {
            _logger.LogWarning(ex, "Validation error processing capital withdrawal for partner ID: {PartnerId}", id);
            return CreateErrorResponse(ex.Message, 400);
        }
        catch (InvalidOperationException ex)
        {
            _logger.LogWarning(ex, "Business rule violation processing capital withdrawal for partner ID: {PartnerId}", id);
            return CreateErrorResponse(ex.Message, 400);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing capital withdrawal for partner ID: {PartnerId}", id);
            return CreateErrorResponse("An error occurred while processing capital withdrawal.", 500);
        }
    }

    /// <summary>
    /// Get partner capital history
    /// الحصول على تاريخ رأس مال الشريك
    /// </summary>
    [HttpGet("{id}/capital-history")]
    public async Task<IActionResult> GetCapitalHistory(int id, CancellationToken cancellationToken = default)
    {
        try
        {
            if (!HasPermission("Partners", "View"))
            {
                return CreateErrorResponse("You don't have permission to view partner capital history.", 403);
            }

            var history = await _partnerService.GetCapitalHistoryAsync(id, cancellationToken);
            return CreateResponse(history, "Partner capital history retrieved successfully.");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving capital history for partner ID: {PartnerId}", id);
            return CreateErrorResponse("An error occurred while retrieving capital history.", 500);
        }
    }

    /// <summary>
    /// Set partner active status
    /// تعيين حالة تفعيل الشريك
    /// </summary>
    [HttpPatch("{id}/status")]
    public async Task<IActionResult> SetActiveStatus(int id, [FromBody] bool isActive, CancellationToken cancellationToken = default)
    {
        try
        {
            if (!HasPermission("Partners", "Edit"))
            {
                return CreateErrorResponse("You don't have permission to change partner status.", 403);
            }

            var result = await _partnerService.SetActiveStatusAsync(id, isActive, cancellationToken);
            if (!result)
            {
                return CreateErrorResponse("Partner not found.", 404);
            }

            _logger.LogInformation("Partner status changed successfully for ID: {PartnerId} to {Status} by user: {UserId}", 
                id, isActive ? "Active" : "Inactive", GetCurrentUserId());
            
            return CreateResponse<object?>(null, $"Partner {(isActive ? "activated" : "deactivated")} successfully.");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error changing status for partner ID: {PartnerId}", id);
            return CreateErrorResponse("An error occurred while changing partner status.", 500);
        }
    }

    /// <summary>
    /// Delete partner
    /// حذف الشريك
    /// </summary>
    [HttpDelete("{id}")]
    public async Task<IActionResult> Delete(int id, CancellationToken cancellationToken = default)
    {
        try
        {
            if (!HasPermission("Partners", "Delete"))
            {
                return CreateErrorResponse("You don't have permission to delete partners.", 403);
            }

            var result = await _partnerService.DeleteAsync(id, cancellationToken);
            if (!result)
            {
                return CreateErrorResponse("Partner not found.", 404);
            }

            _logger.LogInformation("Partner deleted successfully with ID: {PartnerId} by user: {UserId}", 
                id, GetCurrentUserId());
            
            return CreateResponse<object?>(null, "Partner deleted successfully.");
        }
        catch (InvalidOperationException ex)
        {
            _logger.LogWarning(ex, "Cannot delete partner with ID: {PartnerId}", id);
            return CreateErrorResponse(ex.Message, 400);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deleting partner with ID: {PartnerId}", id);
            return CreateErrorResponse("An error occurred while deleting the partner.", 500);
        }
    }

    /// <summary>
    /// Get partner statistics
    /// الحصول على إحصائيات الشريك
    /// </summary>
    [HttpGet("{id}/statistics")]
    public async Task<IActionResult> GetStatistics(int id, CancellationToken cancellationToken = default)
    {
        try
        {
            if (!HasPermission("Partners", "View"))
            {
                return CreateErrorResponse("You don't have permission to view partner statistics.", 403);
            }

            var statistics = await _partnerService.GetPartnerStatisticsAsync(id, cancellationToken);
            return CreateResponse(statistics, "Partner statistics retrieved successfully.");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving statistics for partner ID: {PartnerId}", id);
            return CreateErrorResponse("An error occurred while retrieving partner statistics.", 500);
        }
    }
}
