using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using BusinessManagementSystem.Domain.Common;

namespace BusinessManagementSystem.Domain.Entities;

/// <summary>
/// Item entity - stores product/material information
/// كيان الصنف - يخزن معلومات المنتجات/المواد
/// </summary>
public class Item : BaseEntity
{
    /// <summary>
    /// Item name
    /// اسم الصنف
    /// </summary>
    [Required]
    [MaxLength(200)]
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// Item barcode
    /// باركود الصنف
    /// </summary>
    [MaxLength(50)]
    public string? Barcode { get; set; }

    /// <summary>
    /// Item description
    /// وصف الصنف
    /// </summary>
    [MaxLength(1000)]
    public string? Description { get; set; }

    /// <summary>
    /// Item type (RawMaterial/FinishedProduct)
    /// نوع الصنف (مادة خام/منتج نهائي)
    /// </summary>
    [Required]
    [MaxLength(20)]
    public string ItemType { get; set; } = string.Empty;

    /// <summary>
    /// Item image file path
    /// مسار ملف صورة الصنف
    /// </summary>
    [MaxLength(500)]
    public string? ImagePath { get; set; }

    /// <summary>
    /// Unit of measurement (kg, piece, liter, etc.)
    /// وحدة القياس (كيلو، قطعة، لتر، إلخ)
    /// </summary>
    [Required]
    [MaxLength(20)]
    public string UnitOfMeasurement { get; set; } = string.Empty;

    /// <summary>
    /// Unit price
    /// سعر الوحدة
    /// </summary>
    [Column(TypeName = "decimal(18,2)")]
    public decimal UnitPrice { get; set; }

    /// <summary>
    /// Cost price
    /// سعر التكلفة
    /// </summary>
    [Column(TypeName = "decimal(18,2)")]
    public decimal CostPrice { get; set; }

    /// <summary>
    /// Current stock quantity
    /// كمية المخزون الحالية
    /// </summary>
    [Column(TypeName = "decimal(18,2)")]
    public decimal StockQuantity { get; set; }

    /// <summary>
    /// Minimum stock level
    /// الحد الأدنى للمخزون
    /// </summary>
    [Column(TypeName = "decimal(18,2)")]
    public decimal MinimumStockLevel { get; set; }

    /// <summary>
    /// Maximum stock level
    /// الحد الأقصى للمخزون
    /// </summary>
    [Column(TypeName = "decimal(18,2)")]
    public decimal MaximumStockLevel { get; set; }

    /// <summary>
    /// Reorder point
    /// نقطة إعادة الطلب
    /// </summary>
    [Column(TypeName = "decimal(18,2)")]
    public decimal ReorderPoint { get; set; }

    /// <summary>
    /// Item category
    /// فئة الصنف
    /// </summary>
    [MaxLength(100)]
    public string? Category { get; set; }

    /// <summary>
    /// Item image URL
    /// رابط صورة الصنف
    /// </summary>
    [MaxLength(500)]
    public string? ImageUrl { get; set; }

    /// <summary>
    /// Whether the item is active
    /// ما إذا كان الصنف نشطًا
    /// </summary>
    public bool IsActive { get; set; } = true;

    /// <summary>
    /// Company identifier (Foreign Key)
    /// معرف الشركة (مفتاح خارجي)
    /// </summary>
    [Required]
    public int CompanyId { get; set; }

    // Navigation properties
    [ForeignKey(nameof(CompanyId))]
    public virtual Company Company { get; set; } = null!;
    
    public virtual ICollection<InventoryTransaction> InventoryTransactions { get; set; } = new List<InventoryTransaction>();
    public virtual ICollection<InvoiceDetail> InvoiceDetails { get; set; } = new List<InvoiceDetail>();
}
