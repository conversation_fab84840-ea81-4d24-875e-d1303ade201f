using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using BusinessManagementSystem.Domain.Entities;

namespace BusinessManagementSystem.Infrastructure.Data.Configurations;

/// <summary>
/// Entity configuration for InventoryTransaction
/// تكوين الكيان لمعاملة المخزون
/// </summary>
public class InventoryTransactionConfiguration : IEntityTypeConfiguration<InventoryTransaction>
{
    public void Configure(EntityTypeBuilder<InventoryTransaction> builder)
    {
        builder.ToTable("InventoryTransactions");

        builder.HasKey(it => it.Id);

        builder.Property(it => it.TransactionDate)
            .IsRequired();

        builder.Property(it => it.TransactionType)
            .IsRequired()
            .HasMaxLength(10);

        builder.Property(it => it.Quantity)
            .IsRequired();

        builder.Property(it => it.ReferenceNumber)
            .HasMaxLength(50);

        builder.Property(it => it.Notes)
            .HasMaxLength(1000);

        // Indexes
        builder.HasIndex(it => it.TransactionDate);

        builder.HasIndex(it => new { it.ItemId, it.WarehouseId, it.TransactionDate });

        builder.HasIndex(it => it.ReferenceNumber)
            .HasFilter("[ReferenceNumber] IS NOT NULL");

        // Relationships
        builder.HasOne(it => it.Item)
            .WithMany(i => i.InventoryTransactions)
            .HasForeignKey(it => it.ItemId)
            .OnDelete(DeleteBehavior.Restrict);

        builder.HasOne(it => it.Warehouse)
            .WithMany(w => w.InventoryTransactions)
            .HasForeignKey(it => it.WarehouseId)
            .OnDelete(DeleteBehavior.Restrict);

        builder.HasOne(it => it.Company)
            .WithMany(c => c.InventoryTransactions)
            .HasForeignKey(it => it.CompanyId)
            .OnDelete(DeleteBehavior.Cascade);
    }
}
