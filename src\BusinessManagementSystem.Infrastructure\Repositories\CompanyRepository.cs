using Microsoft.EntityFrameworkCore;
using BusinessManagementSystem.Application.Interfaces.Repositories;
using BusinessManagementSystem.Domain.Entities;
using BusinessManagementSystem.Infrastructure.Data;

namespace BusinessManagementSystem.Infrastructure.Repositories;

/// <summary>
/// Company repository implementation with specific operations
/// تنفيذ مستودع الشركة مع العمليات المخصصة
/// </summary>
public class CompanyRepository : GenericRepository<Company>, ICompanyRepository
{
    public CompanyRepository(BusinessManagementDbContext context) : base(context)
    {
    }

    public async Task<Company?> GetByNameAsync(string name, CancellationToken cancellationToken = default)
    {
        return await _dbSet
            .FirstOrDefaultAsync(c => c.Name == name, cancellationToken);
    }

    public async Task<Company?> GetByCommercialRegistrationNumberAsync(string registrationNumber, CancellationToken cancellationToken = default)
    {
        return await _dbSet
            .FirstOrDefaultAsync(c => c.CommercialRegistrationNumber == registrationNumber, cancellationToken);
    }

    public async Task<Company?> GetByTaxNumberAsync(string taxNumber, CancellationToken cancellationToken = default)
    {
        return await _dbSet
            .FirstOrDefaultAsync(c => c.TaxNumber == taxNumber, cancellationToken);
    }

    public async Task<Company?> GetWithAllRelatedDataAsync(int companyId, CancellationToken cancellationToken = default)
    {
        return await _dbSet
            .Include(c => c.Departments)
            .Include(c => c.Partners)
            .Include(c => c.MainCash)
            .Include(c => c.Warehouses)
            .Include(c => c.Users)
                .ThenInclude(u => u.Role)
            .FirstOrDefaultAsync(c => c.Id == companyId, cancellationToken);
    }
}
