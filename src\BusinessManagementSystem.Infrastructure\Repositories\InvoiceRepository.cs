using Microsoft.EntityFrameworkCore;
using BusinessManagementSystem.Application.Interfaces.Repositories;
using BusinessManagementSystem.Domain.Entities;
using BusinessManagementSystem.Infrastructure.Data;

namespace BusinessManagementSystem.Infrastructure.Repositories;

/// <summary>
/// Invoice repository implementation with specific operations
/// تنفيذ مستودع الفاتورة مع العمليات المخصصة
/// </summary>
public class InvoiceRepository : GenericRepository<Invoice>, IInvoiceRepository
{
    public InvoiceRepository(BusinessManagementDbContext context) : base(context)
    {
    }

    public async Task<Invoice?> GetByInvoiceNumberAsync(string invoiceNumber, CancellationToken cancellationToken = default)
    {
        return await _dbSet
            .Include(i => i.Person)
            .Include(i => i.Company)
            .FirstOrDefaultAsync(i => i.InvoiceNumber == invoiceNumber, cancellationToken);
    }

    public async Task<Invoice?> GetWithDetailsAsync(int invoiceId, CancellationToken cancellationToken = default)
    {
        return await _dbSet
            .Include(i => i.Person)
            .Include(i => i.Company)
            .Include(i => i.InvoiceDetails)
                .ThenInclude(id => id.Item)
            .FirstOrDefaultAsync(i => i.Id == invoiceId, cancellationToken);
    }

    public async Task<IEnumerable<Invoice>> GetByCompanyAndTypeAsync(int companyId, string invoiceType, CancellationToken cancellationToken = default)
    {
        return await _dbSet
            .Include(i => i.Person)
            .Where(i => i.CompanyId == companyId && i.InvoiceType == invoiceType)
            .OrderByDescending(i => i.InvoiceDate)
            .ToListAsync(cancellationToken);
    }

    public async Task<IEnumerable<Invoice>> GetByPersonAsync(int personId, CancellationToken cancellationToken = default)
    {
        return await _dbSet
            .Include(i => i.Company)
            .Where(i => i.PersonId == personId)
            .OrderByDescending(i => i.InvoiceDate)
            .ToListAsync(cancellationToken);
    }

    public async Task<IEnumerable<Invoice>> GetByDateRangeAsync(int companyId, DateTime startDate, DateTime endDate, CancellationToken cancellationToken = default)
    {
        return await _dbSet
            .Include(i => i.Person)
            .Where(i => i.CompanyId == companyId && 
                       i.InvoiceDate >= startDate && 
                       i.InvoiceDate <= endDate)
            .OrderByDescending(i => i.InvoiceDate)
            .ToListAsync(cancellationToken);
    }

    public async Task<decimal> GetSalesTotalByDateRangeAsync(int companyId, DateTime startDate, DateTime endDate, CancellationToken cancellationToken = default)
    {
        return await _dbSet
            .Where(i => i.CompanyId == companyId && 
                       i.InvoiceType == "Sales" && 
                       i.Status == "Confirmed" &&
                       i.InvoiceDate >= startDate && 
                       i.InvoiceDate <= endDate)
            .SumAsync(i => i.TotalAmount + i.TaxAmount - i.DiscountAmount, cancellationToken);
    }

    public async Task<decimal> GetPurchasesTotalByDateRangeAsync(int companyId, DateTime startDate, DateTime endDate, CancellationToken cancellationToken = default)
    {
        return await _dbSet
            .Where(i => i.CompanyId == companyId && 
                       i.InvoiceType == "Purchase" && 
                       i.Status == "Confirmed" &&
                       i.InvoiceDate >= startDate && 
                       i.InvoiceDate <= endDate)
            .SumAsync(i => i.TotalAmount + i.TaxAmount - i.DiscountAmount, cancellationToken);
    }

    public async Task<string> GenerateNextInvoiceNumberAsync(int companyId, string invoiceType, CancellationToken cancellationToken = default)
    {
        var currentYear = DateTime.UtcNow.Year;
        var prefix = invoiceType.Substring(0, 3).ToUpper();
        
        var lastInvoice = await _dbSet
            .Where(i => i.CompanyId == companyId && 
                       i.InvoiceType == invoiceType &&
                       i.InvoiceNumber.StartsWith($"{prefix}-{currentYear}"))
            .OrderByDescending(i => i.InvoiceNumber)
            .FirstOrDefaultAsync(cancellationToken);

        if (lastInvoice == null)
        {
            return $"{prefix}-{currentYear}-0001";
        }

        var lastNumberPart = lastInvoice.InvoiceNumber.Split('-').LastOrDefault();
        if (int.TryParse(lastNumberPart, out int lastNumber))
        {
            return $"{prefix}-{currentYear}-{(lastNumber + 1):D4}";
        }

        return $"{prefix}-{currentYear}-0001";
    }
}
