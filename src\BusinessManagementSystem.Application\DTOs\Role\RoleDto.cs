using BusinessManagementSystem.Application.DTOs.Common;
using System.ComponentModel.DataAnnotations;

namespace BusinessManagementSystem.Application.DTOs.Role;

/// <summary>
/// Role DTO for data transfer
/// DTO الدور لنقل البيانات
/// </summary>
public class RoleDto : BaseDto
{
    /// <summary>
    /// Role name
    /// اسم الدور
    /// </summary>
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// Role description
    /// وصف الدور
    /// </summary>
    public string? Description { get; set; }

    /// <summary>
    /// Whether the role is system-defined (cannot be deleted)
    /// ما إذا كان الدور معرف من النظام (لا يمكن حذفه)
    /// </summary>
    public bool IsSystemRole { get; set; }

    /// <summary>
    /// Whether the role is active
    /// ما إذا كان الدور نشطًا
    /// </summary>
    public bool IsActive { get; set; }

    /// <summary>
    /// Number of users assigned to this role
    /// عدد المستخدمين المعينين لهذا الدور
    /// </summary>
    public int UsersCount { get; set; }

    /// <summary>
    /// Number of permissions assigned to this role
    /// عدد الصلاحيات المعينة لهذا الدور
    /// </summary>
    public int PermissionsCount { get; set; }

    /// <summary>
    /// Role permissions
    /// صلاحيات الدور
    /// </summary>
    public List<RolePermissionSummaryDto> Permissions { get; set; } = new();
}

/// <summary>
/// Create Role DTO
/// DTO إنشاء الدور
/// </summary>
public class CreateRoleDto
{
    /// <summary>
    /// Role name
    /// اسم الدور
    /// </summary>
    [Required(ErrorMessage = "Role name is required")]
    [StringLength(50, ErrorMessage = "Role name cannot exceed 50 characters")]
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// Role description
    /// وصف الدور
    /// </summary>
    [StringLength(200, ErrorMessage = "Description cannot exceed 200 characters")]
    public string? Description { get; set; }

    /// <summary>
    /// Whether the role is active
    /// ما إذا كان الدور نشطًا
    /// </summary>
    public bool IsActive { get; set; } = true;

    /// <summary>
    /// Initial permissions for the role
    /// الصلاحيات الأولية للدور
    /// </summary>
    public List<CreateRolePermissionDto> Permissions { get; set; } = new();
}

/// <summary>
/// Update Role DTO
/// DTO تحديث الدور
/// </summary>
public class UpdateRoleDto
{
    /// <summary>
    /// Role name
    /// اسم الدور
    /// </summary>
    [Required(ErrorMessage = "Role name is required")]
    [StringLength(50, ErrorMessage = "Role name cannot exceed 50 characters")]
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// Role description
    /// وصف الدور
    /// </summary>
    [StringLength(200, ErrorMessage = "Description cannot exceed 200 characters")]
    public string? Description { get; set; }

    /// <summary>
    /// Whether the role is active
    /// ما إذا كان الدور نشطًا
    /// </summary>
    public bool IsActive { get; set; }
}

/// <summary>
/// Role Permission Summary DTO
/// DTO ملخص صلاحيات الدور
/// </summary>
public class RolePermissionSummaryDto
{
    /// <summary>
    /// Page name
    /// اسم الصفحة
    /// </summary>
    public string PageName { get; set; } = string.Empty;

    /// <summary>
    /// Can add permission
    /// صلاحية الإضافة
    /// </summary>
    public bool CanAdd { get; set; }

    /// <summary>
    /// Can edit permission
    /// صلاحية التعديل
    /// </summary>
    public bool CanEdit { get; set; }

    /// <summary>
    /// Can delete permission
    /// صلاحية الحذف
    /// </summary>
    public bool CanDelete { get; set; }

    /// <summary>
    /// Can view permission
    /// صلاحية العرض
    /// </summary>
    public bool CanView { get; set; }
}

/// <summary>
/// Create Role Permission DTO
/// DTO إنشاء صلاحية الدور
/// </summary>
public class CreateRolePermissionDto
{
    /// <summary>
    /// Page name
    /// اسم الصفحة
    /// </summary>
    [Required(ErrorMessage = "Page name is required")]
    [StringLength(50, ErrorMessage = "Page name cannot exceed 50 characters")]
    public string PageName { get; set; } = string.Empty;

    /// <summary>
    /// Can add permission
    /// صلاحية الإضافة
    /// </summary>
    public bool CanAdd { get; set; }

    /// <summary>
    /// Can edit permission
    /// صلاحية التعديل
    /// </summary>
    public bool CanEdit { get; set; }

    /// <summary>
    /// Can delete permission
    /// صلاحية الحذف
    /// </summary>
    public bool CanDelete { get; set; }

    /// <summary>
    /// Can view permission
    /// صلاحية العرض
    /// </summary>
    public bool CanView { get; set; }
}

/// <summary>
/// Role Statistics DTO
/// DTO إحصائيات الدور
/// </summary>
public class RoleStatisticsDto
{
    /// <summary>
    /// Role information
    /// معلومات الدور
    /// </summary>
    public RoleDto Role { get; set; } = null!;

    /// <summary>
    /// Active users count
    /// عدد المستخدمين النشطين
    /// </summary>
    public int ActiveUsersCount { get; set; }

    /// <summary>
    /// Inactive users count
    /// عدد المستخدمين غير النشطين
    /// </summary>
    public int InactiveUsersCount { get; set; }

    /// <summary>
    /// Users by company
    /// المستخدمون حسب الشركة
    /// </summary>
    public List<UsersByCompanyDto> UsersByCompany { get; set; } = new();

    /// <summary>
    /// Recent user activities
    /// أنشطة المستخدمين الحديثة
    /// </summary>
    public List<RecentUserActivityDto> RecentActivities { get; set; } = new();
}

/// <summary>
/// Users by Company DTO
/// DTO المستخدمون حسب الشركة
/// </summary>
public class UsersByCompanyDto
{
    /// <summary>
    /// Company ID
    /// معرف الشركة
    /// </summary>
    public int CompanyId { get; set; }

    /// <summary>
    /// Company name
    /// اسم الشركة
    /// </summary>
    public string CompanyName { get; set; } = string.Empty;

    /// <summary>
    /// Users count in this company
    /// عدد المستخدمين في هذه الشركة
    /// </summary>
    public int UsersCount { get; set; }

    /// <summary>
    /// Active users count
    /// عدد المستخدمين النشطين
    /// </summary>
    public int ActiveUsersCount { get; set; }
}

/// <summary>
/// Recent User Activity DTO
/// DTO نشاط المستخدم الحديث
/// </summary>
public class RecentUserActivityDto
{
    /// <summary>
    /// User ID
    /// معرف المستخدم
    /// </summary>
    public int UserId { get; set; }

    /// <summary>
    /// User name
    /// اسم المستخدم
    /// </summary>
    public string UserName { get; set; } = string.Empty;

    /// <summary>
    /// Full name
    /// الاسم الكامل
    /// </summary>
    public string FullName { get; set; } = string.Empty;

    /// <summary>
    /// Last login date
    /// تاريخ آخر تسجيل دخول
    /// </summary>
    public DateTime? LastLoginDate { get; set; }

    /// <summary>
    /// Company name
    /// اسم الشركة
    /// </summary>
    public string CompanyName { get; set; } = string.Empty;

    /// <summary>
    /// Is user active
    /// هل المستخدم نشط
    /// </summary>
    public bool IsActive { get; set; }
}

/// <summary>
/// Bulk Role Permission Update DTO
/// DTO تحديث صلاحيات الدور بالجملة
/// </summary>
public class BulkRolePermissionUpdateDto
{
    /// <summary>
    /// Role ID
    /// معرف الدور
    /// </summary>
    [Required(ErrorMessage = "Role ID is required")]
    public int RoleId { get; set; }

    /// <summary>
    /// Permissions to update
    /// الصلاحيات المراد تحديثها
    /// </summary>
    [Required(ErrorMessage = "Permissions are required")]
    public List<CreateRolePermissionDto> Permissions { get; set; } = new();

    /// <summary>
    /// Whether to replace all existing permissions or merge
    /// ما إذا كان سيتم استبدال جميع الصلاحيات الموجودة أم دمجها
    /// </summary>
    public bool ReplaceExisting { get; set; } = true;
}
